// Brick<PERSON>hain Routes Helper
// Run this file with Node.js to see all available routes

const fs = require('fs');
const path = require('path');
const appDir = path.join(__dirname, '..');

// Function to recursively find all page files
function findPages(dir, basePath = '', routes = []) {
  const files = fs.readdirSync(dir);

  for (const file of files) {
    const filePath = path.join(dir, file);
    const relativePath = path.relative(appDir, filePath);
    const isDirectory = fs.statSync(filePath).isDirectory();

    if (isDirectory) {
      // Skip node_modules and hidden folders
      if (file === 'node_modules' || file.startsWith('.')) {
        continue;
      }

      // Calculate the route path
      let routePath = basePath;
      if (file !== 'app') {
        routePath = file === 'page' ? basePath : path.join(basePath, file);
      }

      // Recursively search in subdirectories
      findPages(filePath, routePath, routes);
    } else if (file === 'page.tsx' || file === 'page.jsx' || file === 'page.js') {
      // Found a page file, add its route
      routes.push('/' + basePath);
    }
  }

  return routes;
}

// Main function
function listRoutes() {
  console.log('BrickChain Available Routes');
  console.log('==========================');
  console.log('');

  const routes = findPages(appDir);

  // Sort routes alphabetically
  routes.sort();

  // Group routes by category
  const groupedRoutes = {
    'Main Pages': routes.filter(r => !r.includes('/')),
    'Debug Pages': routes.filter(r => r.startsWith('/debug')),
    'Dashboard': routes.filter(r => r.startsWith('/dashboard')),
    'Other Pages': routes.filter(r => {
      return !r.startsWith('/debug') &&
             !r.startsWith('/dashboard') &&
             r.includes('/');
    })
  };

  // Print grouped routes
  for (const [category, categoryRoutes] of Object.entries(groupedRoutes)) {
    if (categoryRoutes.length > 0) {
      console.log(`${category}:`);
      categoryRoutes.forEach(route => {
        const normalizedRoute = route === '//' ? '/' : route;
        console.log(`  http://localhost:3000${normalizedRoute}`);
      });
      console.log('');
    }
  }

  console.log('To access these routes:');
  console.log('1. Make sure your development server is running (npm run dev)');
  console.log('2. Open your browser and navigate to the URLs above');
  console.log('');
  console.log('For the debug pages, use:');
  console.log('  http://localhost:3000/debug');
  console.log('  http://localhost:3000/debug/test-returns');
}

// Execute the function
listRoutes();