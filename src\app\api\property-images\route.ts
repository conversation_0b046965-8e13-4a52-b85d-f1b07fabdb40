import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { create as createW3Client } from '@web3-storage/w3up-client';

export const runtime = 'nodejs';

/**
 * GET /api/property-images?propertyId={propertyId}
 * 
 * Get all images for a property
 */
export async function GET(req: NextRequest) {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    // Get property ID from query params
    const { searchParams } = new URL(req.url);
    const propertyId = searchParams.get('propertyId');
    
    if (!propertyId) {
      return NextResponse.json({ error: 'Missing propertyId parameter' }, { status: 400 });
    }
    
    // Verify authentication
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Get property to check ownership
    const { data: property, error: propertyError } = await supabase
      .from('properties')
      .select('owner_id')
      .eq('id', propertyId)
      .single();
    
    if (propertyError) {
      return NextResponse.json({ error: 'Property not found' }, { status: 404 });
    }
    
    // Get user profile to check role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();
    
    if (profileError) {
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 });
    }
    
    // Check if user is admin, owner of the property, or if the property is published
    const isAdmin = profile.role === 'admin';
    const isOwner = property.owner_id === user.id;
    
    if (!isAdmin && !isOwner) {
      // Check if property is published
      const { data: publishedProperty, error: publishedError } = await supabase
        .from('properties')
        .select('published')
        .eq('id', propertyId)
        .single();
      
      if (publishedError || !publishedProperty.published) {
        return NextResponse.json({ error: 'Unauthorized to view this property' }, { status: 403 });
      }
    }
    
    // Get all images for the property
    const { data: images, error: imagesError } = await supabase
      .from('property_images')
      .select('*')
      .eq('property_id', propertyId)
      .order('sort_order', { ascending: true });
    
    if (imagesError) {
      return NextResponse.json({ error: 'Failed to get property images' }, { status: 500 });
    }
    
    return NextResponse.json({ images });
  } catch (error) {
    console.error('Error in GET /api/property-images:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * POST /api/property-images
 * 
 * Upload a new property image
 * 
 * Body: {
 *   propertyId: string,
 *   imageType: string,
 *   sortOrder: number,
 *   imageUrl?: string (optional, if already uploaded to Supabase)
 * }
 * 
 * File should be included in the request as FormData
 */
export async function POST(req: NextRequest) {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    // Verify authentication
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Parse form data
    const formData = await req.formData();
    const propertyId = formData.get('propertyId') as string;
    const imageType = formData.get('imageType') as string;
    const sortOrder = parseInt(formData.get('sortOrder') as string || '0');
    const file = formData.get('file') as File;
    
    if (!propertyId) {
      return NextResponse.json({ error: 'Missing propertyId' }, { status: 400 });
    }
    
    if (!imageType) {
      return NextResponse.json({ error: 'Missing imageType' }, { status: 400 });
    }
    
    // Check if user is owner of the property
    const { data: property, error: propertyError } = await supabase
      .from('properties')
      .select('owner_id')
      .eq('id', propertyId)
      .single();
    
    if (propertyError) {
      return NextResponse.json({ error: 'Property not found' }, { status: 404 });
    }
    
    if (property.owner_id !== user.id) {
      // Check if user is admin
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single();
      
      if (profileError || profile.role !== 'admin') {
        return NextResponse.json({ error: 'Unauthorized to modify this property' }, { status: 403 });
      }
    }
    
    // Handle file upload
    let imageUrl: string;
    let imageCid: string | null = null;
    
    if (file) {
      // Try to upload to IPFS first
      try {
        // Initialize web3.storage client
        const client = await createW3Client();
        
        // Check if we need to authenticate
        const WEB3_STORAGE_EMAIL = process.env.NEXT_PUBLIC_WEB3_STORAGE_EMAIL;
        const WEB3_STORAGE_SPACE_DID = process.env.NEXT_PUBLIC_WEB3_STORAGE_SPACE_DID;
        
        if (WEB3_STORAGE_EMAIL && WEB3_STORAGE_SPACE_DID) {
          if (!client.currentSpace()) {
            await client.login(WEB3_STORAGE_EMAIL);
            await client.setCurrentSpace(WEB3_STORAGE_SPACE_DID);
          }
        }
        
        // Upload to IPFS
        const cid = await client.uploadFile(file);
        imageCid = cid.toString();
        imageUrl = `https://w3s.link/ipfs/${imageCid}`;
      } catch (ipfsError) {
        console.error('IPFS upload failed, falling back to Supabase:', ipfsError);
        
        // Fall back to Supabase Storage
        const fileName = `property-${propertyId}/${imageType}/${Date.now()}-${file.name}`;
        
        const { error: uploadError, data: uploadData } = await supabase.storage
          .from('properties')
          .upload(fileName, file, {
            cacheControl: '3600',
            upsert: true
          });
        
        if (uploadError) {
          return NextResponse.json({ error: 'Failed to upload image' }, { status: 500 });
        }
        
        const { data: urlData } = supabase.storage
          .from('properties')
          .getPublicUrl(fileName);
        
        imageUrl = urlData.publicUrl;
      }
    } else {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }
    
    // Save image metadata to database
    const { data: imageData, error: insertError } = await supabase
      .from('property_images')
      .insert({
        property_id: propertyId,
        image_url: imageUrl,
        image_cid: imageCid,
        image_type: imageType,
        file_name: file.name,
        file_size: file.size,
        file_type: file.type,
        sort_order: sortOrder
      })
      .select()
      .single();
    
    if (insertError) {
      return NextResponse.json({ error: 'Failed to save image metadata' }, { status: 500 });
    }
    
    return NextResponse.json({ image: imageData });
  } catch (error) {
    console.error('Error in POST /api/property-images:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * DELETE /api/property-images?imageId={imageId}
 * 
 * Delete a property image
 */
export async function DELETE(req: NextRequest) {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    // Get image ID from query params
    const { searchParams } = new URL(req.url);
    const imageId = searchParams.get('imageId');
    
    if (!imageId) {
      return NextResponse.json({ error: 'Missing imageId parameter' }, { status: 400 });
    }
    
    // Verify authentication
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Get the image to check property ownership
    const { data: image, error: imageError } = await supabase
      .from('property_images')
      .select('property_id')
      .eq('id', imageId)
      .single();
    
    if (imageError) {
      return NextResponse.json({ error: 'Image not found' }, { status: 404 });
    }
    
    // Check if user is owner of the property
    const { data: property, error: propertyError } = await supabase
      .from('properties')
      .select('owner_id')
      .eq('id', image.property_id)
      .single();
    
    if (propertyError) {
      return NextResponse.json({ error: 'Property not found' }, { status: 404 });
    }
    
    if (property.owner_id !== user.id) {
      // Check if user is admin
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single();
      
      if (profileError || profile.role !== 'admin') {
        return NextResponse.json({ error: 'Unauthorized to delete this image' }, { status: 403 });
      }
    }
    
    // Delete the image from the database
    const { error: deleteError } = await supabase
      .from('property_images')
      .delete()
      .eq('id', imageId);
    
    if (deleteError) {
      return NextResponse.json({ error: 'Failed to delete image' }, { status: 500 });
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error in DELETE /api/property-images:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
