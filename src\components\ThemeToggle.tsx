'use client';

import React from 'react';
import { useTheme } from '@/context/ThemeContext';
import { Sun, Moon, Monitor } from 'lucide-react';

interface ThemeToggleProps {
  variant?: 'icon' | 'button' | 'select';
  className?: string;
}

export default function ThemeToggle({ variant = 'icon', className = '' }: ThemeToggleProps) {
  const { theme, setTheme, resolvedTheme } = useTheme();

  const toggleTheme = () => {
    if (theme === 'dark') {
      setTheme('light');
    } else if (theme === 'light') {
      setTheme('system');
    } else {
      setTheme('dark');
    }
  };

  if (variant === 'select') {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <select
          value={theme}
          onChange={(e) => setTheme(e.target.value as 'light' | 'dark' | 'system')}
          className="bg-indigo-900/50 border border-indigo-700 rounded-lg text-white px-3 py-2 focus:ring-2 focus:ring-indigo-500"
        >
          <option value="light">Light</option>
          <option value="dark">Dark</option>
          <option value="system">System</option>
        </select>
      </div>
    );
  }

  if (variant === 'button') {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <button
          onClick={() => setTheme('light')}
          className={`p-2 rounded-l-lg ${
            theme === 'light' ? 'bg-indigo-600 text-white' : 'bg-indigo-900/30 text-gray-400'
          }`}
          aria-label="Light mode"
        >
          <Sun size={18} />
        </button>
        <button
          onClick={() => setTheme('system')}
          className={`p-2 ${
            theme === 'system' ? 'bg-indigo-600 text-white' : 'bg-indigo-900/30 text-gray-400'
          }`}
          aria-label="System theme"
        >
          <Monitor size={18} />
        </button>
        <button
          onClick={() => setTheme('dark')}
          className={`p-2 rounded-r-lg ${
            theme === 'dark' ? 'bg-indigo-600 text-white' : 'bg-indigo-900/30 text-gray-400'
          }`}
          aria-label="Dark mode"
        >
          <Moon size={18} />
        </button>
      </div>
    );
  }

  // Default icon variant
  return (
    <button
      onClick={toggleTheme}
      className={`p-2 rounded-lg hover:bg-indigo-900/30 ${className}`}
      aria-label="Toggle theme"
    >
      {theme === 'dark' ? (
        <Moon size={20} className="text-indigo-300" />
      ) : theme === 'light' ? (
        <Sun size={20} className="text-indigo-600" />
      ) : resolvedTheme === 'dark' ? (
        <Moon size={20} className="text-indigo-300" />
      ) : (
        <Sun size={20} className="text-indigo-600" />
      )}
    </button>
  );
}
