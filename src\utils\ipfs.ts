/**
 * IPFS Integration Utilities
 * 
 * This file contains helper functions for interacting with IPFS.
 * We use NFT.Storage as the primary IPFS pinning service with Supabase storage as fallback.
 */

// @ts-ignore: Adjusting import path if necessary for project structure
import { create, type Client, type SpaceDID, type EmailAddress } from '@web3-storage/w3up-client';

// We'll be using the fetch API for HTTP uploads
export interface IPFSUploadResponse {
  success: boolean;
  cid?: string;
  url?: string;
  error?: string;
  fallback?: boolean;
}

// Define IPFS gateway URLs to try (in order) with environment variable overrides
const IPFS_GATEWAYS = [
  process.env.NEXT_PUBLIC_IPFS_GATEWAY_1 || 'https://ipfs.io/ipfs/',
  process.env.NEXT_PUBLIC_IPFS_GATEWAY_2 || 'https://gateway.pinata.cloud/ipfs/',
  process.env.NEXT_PUBLIC_IPFS_GATEWAY_3 || 'https://cloudflare-ipfs.com/ipfs/',
  process.env.NEXT_PUBLIC_IPFS_GATEWAY_4 || 'https://ipfs.infura.io/ipfs/'
];

// Default gateway to use for URLs (can be customized via env var)
const DEFAULT_GATEWAY = process.env.NEXT_PUBLIC_DEFAULT_IPFS_GATEWAY || IPFS_GATEWAYS[0];

/**
 * Upload file to IPFS
 * 
 * This function attempts to upload a file to IPFS using web3.storage as the primary,
 * NFT.Storage as secondary, and finally Supabase storage as a fallback.
 * 
 * @param file File to upload
 * @returns Promise with CID and URL
 */
export async function uploadToIPFS(file: File): Promise<IPFSUploadResponse> {
  const WEB3_STORAGE_EMAIL = process.env.NEXT_PUBLIC_WEB3_STORAGE_EMAIL;
  const WEB3_STORAGE_SPACE_DID = process.env.NEXT_PUBLIC_WEB3_STORAGE_SPACE_DID;
  const WEB3_STORAGE_TIMEOUT = parseInt(process.env.NEXT_PUBLIC_WEB3_STORAGE_TIMEOUT || '60000', 10); // 60s timeout

  // 1. Try web3.storage
  if (WEB3_STORAGE_EMAIL && WEB3_STORAGE_SPACE_DID) {
    const web3StorageController = new AbortController(); // Although not directly used by client.uploadFile, useful for Promise.race
    
    try {
      console.log('[ipfs.ts] Attempting upload with web3.storage...');
      const client = await create();
      
      // Check if logged in, if not, try to login
      if (!client.currentSpace()) {
        await client.login(WEB3_STORAGE_EMAIL as EmailAddress);
        await client.setCurrentSpace(WEB3_STORAGE_SPACE_DID as SpaceDID);
      } else {
        // If already logged in, ensure current space is set (it might persist from previous calls)
        await client.setCurrentSpace(WEB3_STORAGE_SPACE_DID as SpaceDID);
      }

      const uploadPromise = client.uploadFile(file);
      
      const cidResult = await Promise.race([
        uploadPromise,
        new Promise((_, reject) => 
          setTimeout(() => {
            web3StorageController.abort(); // Signal abortion
            reject(new Error('web3.storage upload timed out'));
          }, WEB3_STORAGE_TIMEOUT)
        )
      ]);

      // Assuming cidResult is the CID object if successful
      const cid = cidResult as any; // Type assertion for CID object from web3.storage

      if (!cid || typeof cid.toString !== 'function') {
        throw new Error('No valid CID returned from web3.storage');
      }

      const cidString = cid.toString();
      console.log('[ipfs.ts] web3.storage upload successful. CID:', cidString);
      return {
        success: true,
        cid: cidString,
        url: `${DEFAULT_GATEWAY}${cidString}`
      };
    } catch (web3StorageError: any) {
      console.warn(`[ipfs.ts] web3.storage upload failed: ${web3StorageError.message || web3StorageError}. Falling back to NFT.Storage.`);
    }
  } else {
    console.warn('[ipfs.ts] web3.storage email or space DID not configured. Skipping web3.storage and trying NFT.Storage.');
  }

  // 2. Try NFT.Storage
  const NFT_STORAGE_API_KEY = process.env.NEXT_PUBLIC_NFT_STORAGE_API_KEY;
  if (NFT_STORAGE_API_KEY) {
    const nftStorageController = new AbortController();
    const nftStorageTimeoutDuration = parseInt(process.env.NEXT_PUBLIC_NFT_STORAGE_TIMEOUT || '30000', 10);
    const nftStorageTimeoutId = setTimeout(() => nftStorageController.abort(), nftStorageTimeoutDuration);
    
    try {
      console.log('[ipfs.ts] Attempting upload with NFT.Storage...');
      const formData = new FormData();
      formData.append('file', file);

      const nftStorageApiUrl = process.env.NEXT_PUBLIC_NFT_STORAGE_API_URL || 'https://api.nft.storage/upload';
      const response = await fetch(nftStorageApiUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${NFT_STORAGE_API_KEY}`
        },
        body: formData,
        signal: nftStorageController.signal
      });
      
      clearTimeout(nftStorageTimeoutId);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('[ipfs.ts] NFT.Storage response error text:', errorText);
        throw new Error(`Failed to upload to NFT.Storage: ${response.status} ${response.statusText} - ${errorText}`);
      }
      
      const data = await response.json();
      console.log('[ipfs.ts] NFT.Storage response data:', data);
      
      if (!data.ok) {
        throw new Error(`NFT.Storage API error: ${data.error?.message || 'Unknown error from NFT.Storage data object'}`);
      }
      
      const cid = data.value?.cid;
      
      if (!cid) {
        throw new Error('No CID returned from NFT.Storage');
      }
      console.log('[ipfs.ts] NFT.Storage upload successful. CID:', cid);
      return {
        success: true,
        cid,
        url: `${DEFAULT_GATEWAY}${cid}`
      };
    } catch (nftStorageError: any) {
      clearTimeout(nftStorageTimeoutId); // Ensure timeout is cleared on error too
      console.warn(`[ipfs.ts] NFT.Storage upload failed: ${nftStorageError.message || nftStorageError}. Falling back to Supabase/Data URL.`);
       // Fall through to Supabase/Data URL fallback by not returning here
    }
  } else {
    console.warn('[ipfs.ts] NFT.Storage API key not configured. Skipping NFT.Storage and trying fallback methods.');
  }
  
  // 3. Final Fallback (Supabase storage, then data URL)
  console.log('[ipfs.ts] All primary/secondary IPFS uploads failed or were skipped. Using fallback methods.');
  return await uploadToIPFSFallback(file);
}

/**
 * Fallback upload method using Supabase storage
 * This is used if the primary IPFS method fails
 * 
 * @param file File to upload
 * @returns Promise with URL and mock CID
 */
async function uploadToIPFSFallback(file: File): Promise<IPFSUploadResponse> {
  try {
    console.log('Attempting fallback upload to Supabase storage');
    
    // Get environment variables
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';
    
    if (!supabaseUrl || !supabaseAnonKey) {
      throw new Error('Supabase credentials not configured for fallback upload');
    }
    
    // Create a unique filename with timestamp and sanitized original name
    const timestamp = Date.now();
    const safeFileName = file.name.replace(/[^a-zA-Z0-9.]/g, '_');
    const fileName = `fallback-${timestamp}-${safeFileName}`;
    
    // Import Supabase client dynamically to reduce bundle size
    const { createClient } = await import('@supabase/supabase-js');
    const supabase = createClient(supabaseUrl, supabaseAnonKey);
    
    // Check if the 'properties' bucket exists, use it if available
    const { data: buckets } = await supabase.storage.listBuckets();
    
    let bucketName: string;
    
    // Try known buckets in order of preference
    const knownBuckets = ['properties', 'ipfs', 'images', 'public'];
    const availableBuckets = buckets?.map(b => b.name) || [];
    
    // Find the first available bucket from our list
    const foundBucket = knownBuckets.find(name => availableBuckets.includes(name));
    
    if (foundBucket) {
      bucketName = foundBucket;
    } else if (availableBuckets.length > 0) {
      // Use any available bucket if none of our preferences exist
      bucketName = availableBuckets[0];
    } else {
      // Create the properties bucket if no buckets exist
      try {
        await supabase.storage.createBucket('properties', { public: true });
        bucketName = 'properties';
      } catch (bucketError) {
        throw new Error(`Failed to create storage bucket: ${bucketError instanceof Error ? bucketError.message : 'Unknown error'}`);
      }
    }
    
    // Try to upload to the selected bucket
    const uploadResult = await supabase.storage.from(bucketName).upload(fileName, file, {
      cacheControl: '3600',
      upsert: true
    });
    
    if (uploadResult.error) {
      throw uploadResult.error;
    }
    
    // Get the public URL
    const { data: urlData } = supabase.storage.from(bucketName).getPublicUrl(fileName);
    
    if (!urlData || !urlData.publicUrl) {
      throw new Error('Failed to get public URL for uploaded file');
    }
    
    // Generate a mock CID for compatibility
    const mockCid = `supabase-${timestamp}`;
    
    return {
      success: true,
      cid: mockCid,
      url: urlData.publicUrl,
      fallback: true,
      error: 'Using Supabase storage fallback. Content not actually on IPFS.'
    };
  } catch (error) {
    console.error('Error in IPFS fallback upload:', error);
    
    // Try one more fallback: browser data URL as absolute last resort
    try {
      const dataUrl = await fileToDataUrl(file);
      return {
        success: true,
        url: dataUrl,
        fallback: true,
        error: 'Using temporary data URL. This URL will not persist!'
      };
    } catch (dataUrlError) {
      return {
        success: false,
        error: `All upload methods failed. Original error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }
}

/**
 * Convert a file to a data URL as a last resort fallback
 * Note: This is only suitable for small files and temporary use
 * 
 * @param file The file to convert
 * @returns Promise with a data URL
 */
async function fileToDataUrl(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
}

/**
 * Generate the appropriate gateway URL for an IPFS CID
 * 
 * @param cid IPFS CID/hash
 * @param preferredGateway Optional preferred gateway to use
 * @returns Complete gateway URL
 */
export function getIPFSUrl(cid: string, preferredGateway?: string): string {
  // If it's already a URL, return as-is
  if (!cid || cid.startsWith('http') || cid.startsWith('data:')) {
    return cid;
  }
  
  // Clean the CID to remove any 'ipfs://' prefix
  const cleanedCid = cid.replace(/^ipfs:\/\//i, '');
  
  // Use preferred gateway if specified, otherwise use default
  const gateway = preferredGateway || DEFAULT_GATEWAY;
  
  return `${gateway}${cleanedCid}`;
}

/**
 * Check if a URL is an IPFS URL
 * 
 * @param url URL to check
 * @returns Boolean indicating if it's an IPFS URL
 */
export function isIPFSUrl(url: string): boolean {
  if (!url) return false;
  
  return (
    url.startsWith('ipfs://') || 
    IPFS_GATEWAYS.some(gateway => url.startsWith(gateway))
  );
}

/**
 * Extract IPFS CID from a URL
 * 
 * @param url IPFS URL
 * @returns CID or null if not found
 */
export function extractIPFSCid(url: string): string | null {
  if (!url) return null;
  
  if (url.startsWith('ipfs://')) {
    return url.replace(/^ipfs:\/\//i, '');
  }
  
  // Check if it's a gateway URL
  for (const gateway of IPFS_GATEWAYS) {
    if (url.startsWith(gateway)) {
      return url.replace(gateway, '');
    }
  }
  
  return null;
} 