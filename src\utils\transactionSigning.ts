/**
 * Transaction Signing Utilities
 * 
 * This file contains utilities for signing transactions using SIWE (Sign-In with Ethereum)
 * and verifying signatures before processing transactions.
 */

import { SiweMessage } from 'siwe';
import { createClient } from '@supabase/supabase-js';
import { ethers } from 'ethers';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Transaction types
export type TransactionType = 
  | 'property_investment' 
  | 'property_listing' 
  | 'withdraw_funds' 
  | 'claim_rewards'
  | 'update_property'
  | 'cancel_listing';

// Transaction data interface
export interface TransactionData {
  type: TransactionType;
  amount?: string;
  property_id?: string;
  token_id?: string;
  metadata?: Record<string, any>;
  [key: string]: any;
}

// Transaction signature interface
export interface TransactionSignature {
  id?: string;
  user_id: string;
  wallet_address: string;
  transaction_type: TransactionType;
  transaction_data: TransactionData;
  message: string;
  signature: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  created_at?: string;
  completed_at?: string;
  chain_id: number;
  tx_hash?: string;
}

/**
 * Create a SIWE message for transaction signing
 * 
 * @param address Wallet address
 * @param transactionType Type of transaction
 * @param transactionData Transaction data
 * @param chainId Chain ID
 * @returns SIWE message and prepared message string
 */
export const createTransactionMessage = (
  address: string,
  transactionType: TransactionType,
  transactionData: TransactionData,
  chainId: number = 1
): { message: SiweMessage; preparedMessage: string } => {
  // Create a nonce
  const nonce = Math.floor(Math.random() * 1000000).toString();
  
  // Create a human-readable description based on transaction type
  let statement = '';
  
  switch (transactionType) {
    case 'property_investment':
      statement = `I authorize investing ${transactionData.amount} ETH in property ${transactionData.property_id}`;
      break;
    case 'property_listing':
      statement = `I authorize listing my property for ${transactionData.amount} ETH`;
      break;
    case 'withdraw_funds':
      statement = `I authorize withdrawing ${transactionData.amount} ETH to my wallet`;
      break;
    case 'claim_rewards':
      statement = `I authorize claiming rewards of ${transactionData.amount} ETH from my investments`;
      break;
    case 'update_property':
      statement = `I authorize updating property ${transactionData.property_id} details`;
      break;
    case 'cancel_listing':
      statement = `I authorize cancelling the listing for property ${transactionData.property_id}`;
      break;
    default:
      statement = `I authorize this transaction on BrickChain`;
  }
  
  // Add timestamp for additional security
  const timestamp = new Date().toISOString();
  statement += ` at ${timestamp}`;
  
  // Create SIWE message
  const message = new SiweMessage({
    domain: window.location.host,
    address,
    statement,
    uri: window.location.origin,
    version: '1',
    chainId,
    nonce,
    // Include transaction data in the resources field for verification
    resources: [
      `transaction:${transactionType}`,
      `data:${JSON.stringify(transactionData)}`
    ],
  });
  
  // Prepare the message for signing
  const preparedMessage = message.prepareMessage();
  
  return { message, preparedMessage };
};

/**
 * Sign a transaction using SIWE
 * 
 * @param signer Ethers signer
 * @param address Wallet address
 * @param transactionType Type of transaction
 * @param transactionData Transaction data
 * @param chainId Chain ID
 * @returns Transaction signature object
 */
export const signTransaction = async (
  signer: ethers.Signer,
  address: string,
  transactionType: TransactionType,
  transactionData: TransactionData,
  chainId: number = 1
): Promise<TransactionSignature> => {
  try {
    // Create the SIWE message
    const { message, preparedMessage } = createTransactionMessage(
      address,
      transactionType,
      transactionData,
      chainId
    );
    
    // Sign the message
    const signature = await signer.signMessage(preparedMessage);
    
    // Create transaction signature object
    const transactionSignature: TransactionSignature = {
      user_id: '', // Will be filled by the server
      wallet_address: address,
      transaction_type: transactionType,
      transaction_data: transactionData,
      message: preparedMessage,
      signature,
      status: 'pending',
      chain_id: chainId,
      created_at: new Date().toISOString()
    };
    
    return transactionSignature;
  } catch (error) {
    console.error('Error signing transaction:', error);
    throw error;
  }
};

/**
 * Store a transaction signature in the database
 * 
 * @param userId User ID
 * @param transactionSignature Transaction signature object
 * @returns Stored transaction signature with ID
 */
export const storeTransactionSignature = async (
  userId: string,
  transactionSignature: TransactionSignature
): Promise<TransactionSignature> => {
  try {
    // Add user ID to the transaction signature
    const signatureWithUserId = {
      ...transactionSignature,
      user_id: userId
    };
    
    // Store in database
    const { data, error } = await supabase
      .from('transaction_signatures')
      .insert(signatureWithUserId)
      .select()
      .single();
    
    if (error) {
      throw error;
    }
    
    return data;
  } catch (error) {
    console.error('Error storing transaction signature:', error);
    throw error;
  }
};

/**
 * Verify a transaction signature
 * 
 * @param transactionSignature Transaction signature object
 * @returns Whether the signature is valid
 */
export const verifyTransactionSignature = (
  transactionSignature: TransactionSignature
): boolean => {
  try {
    // Recover the address from the signature
    const recoveredAddress = ethers.verifyMessage(
      transactionSignature.message,
      transactionSignature.signature
    );
    
    // Check if the recovered address matches the wallet address
    return recoveredAddress.toLowerCase() === transactionSignature.wallet_address.toLowerCase();
  } catch (error) {
    console.error('Error verifying transaction signature:', error);
    return false;
  }
};

/**
 * Update a transaction signature status
 * 
 * @param signatureId Transaction signature ID
 * @param status New status
 * @param txHash Optional transaction hash
 * @returns Updated transaction signature
 */
export const updateTransactionStatus = async (
  signatureId: string,
  status: 'completed' | 'failed' | 'cancelled',
  txHash?: string
): Promise<TransactionSignature> => {
  try {
    const updateData: Partial<TransactionSignature> = {
      status,
    };
    
    if (status === 'completed') {
      updateData.completed_at = new Date().toISOString();
      if (txHash) {
        updateData.tx_hash = txHash;
      }
    }
    
    const { data, error } = await supabase
      .from('transaction_signatures')
      .update(updateData)
      .eq('id', signatureId)
      .select()
      .single();
    
    if (error) {
      throw error;
    }
    
    return data;
  } catch (error) {
    console.error('Error updating transaction status:', error);
    throw error;
  }
};

/**
 * Get transaction signatures for a user
 * 
 * @param userId User ID
 * @param limit Maximum number of signatures to return
 * @param offset Pagination offset
 * @returns Array of transaction signatures
 */
export const getUserTransactionSignatures = async (
  userId: string,
  limit: number = 10,
  offset: number = 0
): Promise<TransactionSignature[]> => {
  try {
    const { data, error } = await supabase
      .from('transaction_signatures')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);
    
    if (error) {
      throw error;
    }
    
    return data || [];
  } catch (error) {
    console.error('Error getting user transaction signatures:', error);
    throw error;
  }
};
