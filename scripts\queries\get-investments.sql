-- Get all investments with property and investor details
SELECT 
  i.id,
  i.amount,
  i.shares,
  i.transaction_hash,
  i.status,
  i.created_at,
  p.name as property_name,
  p.price as property_price,
  p.location as property_location,
  u.email as investor_email,
  u.wallet_address as investor_wallet,
  ROUND((i.amount::decimal / p.price::decimal) * 100, 2) as ownership_percentage
FROM investments i
JOIN properties p ON i.property_id = p.id
JOIN users u ON i.investor_id = u.id
ORDER BY i.created_at DESC;
