-- Update profiles table to support wallet-based authentication

-- Add wallet_address column to profiles table
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS wallet_address TEXT;

-- Add kyc_status column to profiles table
DO $$ 
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'kyc_status_type') THEN
    CREATE TYPE kyc_status_type AS ENUM ('none', 'pending', 'verified', 'rejected');
  END IF;
END $$;

ALTER TABLE profiles ADD COLUMN IF NOT EXISTS kyc_status kyc_status_type DEFAULT 'none';

-- Add updated_at column to profiles table
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT now();

-- Make role column nullable (since users will start without a role)
ALTER TABLE profiles ALTER COLUMN role DROP NOT NULL;

-- Create a function to update the updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to update the updated_at column
DROP TRIGGER IF EXISTS update_profiles_updated_at ON profiles;
CREATE TRIGGER update_profiles_updated_at
BEFORE UPDATE ON profiles
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Create a unique index on wallet_address
CREATE UNIQUE INDEX IF NOT EXISTS profiles_wallet_address_idx ON profiles (wallet_address) WHERE wallet_address IS NOT NULL;

-- Create KYC documents table if it doesn't exist
CREATE TABLE IF NOT EXISTS kyc_documents (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  document_type TEXT NOT NULL,
  document_category TEXT NOT NULL, -- 'identity', 'address', 'financial'
  file_path TEXT NOT NULL,
  file_name TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  file_type TEXT NOT NULL,
  verification_status kyc_status_type DEFAULT 'pending',
  verification_notes TEXT,
  verified_by UUID REFERENCES auth.users(id),
  verified_at TIMESTAMP WITH TIME ZONE,
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create a trigger to update the updated_at column for kyc_documents
DROP TRIGGER IF EXISTS update_kyc_documents_updated_at ON kyc_documents;
CREATE TRIGGER update_kyc_documents_updated_at
BEFORE UPDATE ON kyc_documents
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Create RLS policies for profiles table
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Policy for users to read their own profile
CREATE POLICY read_own_profile ON profiles
  FOR SELECT
  USING (auth.uid() = id);

-- Policy for users to update their own profile
CREATE POLICY update_own_profile ON profiles
  FOR UPDATE
  USING (auth.uid() = id);

-- Policy for admins to read all profiles
CREATE POLICY admin_read_all_profiles ON profiles
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Policy for admins to update all profiles
CREATE POLICY admin_update_all_profiles ON profiles
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Create RLS policies for kyc_documents table
ALTER TABLE kyc_documents ENABLE ROW LEVEL SECURITY;

-- Policy for users to read their own documents
CREATE POLICY read_own_documents ON kyc_documents
  FOR SELECT
  USING (auth.uid() = user_id);

-- Policy for users to insert their own documents
CREATE POLICY insert_own_documents ON kyc_documents
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Policy for admins to read all documents
CREATE POLICY admin_read_all_documents ON kyc_documents
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Policy for admins to update all documents
CREATE POLICY admin_update_all_documents ON kyc_documents
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );
