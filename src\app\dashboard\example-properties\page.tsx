'use client';
import React, { useState } from 'react';
// No longer need to import DashboardWrapper
import PropertyCard from '../../../components/PropertyCard';
import { mockProperties } from '../../../data/mockProperties';

export default function ExamplePropertiesPage() {
  const [selectedProperty, setSelectedProperty] = useState<typeof mockProperties[0] | null>(null);

  // Create three sample properties that match the image design
  const sampleProperties = [
    {
      id: 'sample1',
      name: 'Downtown Duplex',
      location: 'Accra, Ghana',
      price: 200000,
      priceInEth: 100,
      return_rate: 8.2,
      description: 'Modern duplex in the heart of Accra with rooftop terrace and stunning city views. Perfect for both residential and commercial use with high rental demand.',
      status: 'available' as const,
      image_url: '/properties/downtown-duplex.jpg',
      published: true,
      total_shares: 1000,
      funded_percentage: 65,
      investors_count: 650
    },
    {
      id: 'sample2',
      name: 'Beachfront Villa',
      location: 'Cape Town, South Africa',
      price: 300000,
      priceInEth: 150,
      return_rate: 8.2,
      description: 'Luxurious beachfront villa with panoramic ocean views, infinity pool, and direct beach access. High-yield vacation rental with year-round bookings.',
      status: 'available' as const,
      image_url: '/properties/beachfront-villa.jpg',
      published: true,
      total_shares: 1000,
      funded_percentage: 78,
      investors_count: 780
    },
    {
      id: 'sample3',
      name: 'Urban Apartment',
      location: 'Lagos, Nigeria',
      price: 160000,
      priceInEth: 80,
      return_rate: 8.2,
      description: 'Contemporary apartment in Lagos featuring modern architecture, smart home technology, and energy-efficient design. Excellent investment in a rapidly growing market.',
      status: 'available' as const,
      image_url: '/properties/urban-apartment.jpg',
      published: true,
      total_shares: 1000,
      funded_percentage: 42,
      investors_count: 420
    }
  ];

  return (
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-white mb-2">Example Property Cards</h1>
        <p className="text-gray-300">
          These property cards match the design in the provided image, with improved layout and functionality
        </p>
      </div>

      {/* Sample Properties from Image */}
      <div className="mb-12">
        <h2 className="text-xl font-semibold text-white mb-4 pb-2 border-b border-gray-800">
          Properties (Image-Based Design)
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {sampleProperties.map(property => (
            <PropertyCard
              key={property.id}
              property={property}
              redirectOnView={true}
              variant="marketplace"
            />
          ))}
        </div>
      </div>

      {/* Marketplace Cards Example */}
      <div className="mb-12">
        <h2 className="text-xl font-semibold text-white mb-4 pb-2 border-b border-gray-800">
          Marketplace Cards
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {mockProperties.slice(0, 6).map(property => (
            <PropertyCard
              key={property.id}
              property={property}
              redirectOnView={true}
              variant="marketplace"
            />
          ))}
        </div>
      </div>

      {/* Minimal Cards Example */}
      <div className="mb-12">
        <h2 className="text-xl font-semibold text-white mb-4 pb-2 border-b border-gray-800">
          Minimal Cards (for Compact Listings)
        </h2>
        <div className="grid grid-cols-1 gap-2 max-w-2xl">
          {mockProperties.slice(6, 12).map(property => (
            <PropertyCard
              key={property.id}
              property={property}
              redirectOnView={true}
              variant="minimal"
            />
          ))}
        </div>
      </div>

      {/* Property Detail Modal */}
      {selectedProperty && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-900 rounded-xl shadow-2xl overflow-hidden max-w-4xl w-full max-h-[90vh] flex flex-col">
            <div className="relative h-64 md:h-80 bg-gray-800">
              {selectedProperty.image_url ? (
                <img
                  src={selectedProperty.image_url}
                  alt={selectedProperty.name}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <div className="text-gray-600 text-2xl">No Image Available</div>
                </div>
              )}

              <button
                onClick={() => setSelectedProperty(null)}
                className="absolute top-4 right-4 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors"
              >
                ✕
              </button>
            </div>

            <div className="flex-1 overflow-y-auto p-6">
              <h2 className="text-2xl font-bold text-white mb-2">{selectedProperty.name}</h2>
              <p className="text-gray-400 mb-4">{selectedProperty.location}</p>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                <div className="bg-gray-800 p-3 rounded-lg">
                  <div className="text-gray-500 text-sm">Price</div>
                  <div className="font-bold text-white">{selectedProperty.priceInEth} ETH</div>
                </div>
                <div className="bg-gray-800 p-3 rounded-lg">
                  <div className="text-gray-500 text-sm">Yield</div>
                  <div className="font-bold text-green-500">{selectedProperty.return_rate}% est.</div>
                </div>
                <div className="bg-gray-800 p-3 rounded-lg">
                  <div className="text-gray-500 text-sm">Shares</div>
                  <div className="font-bold text-white">{selectedProperty.total_shares?.toLocaleString()}</div>
                </div>
                <div className="bg-gray-800 p-3 rounded-lg">
                  <div className="text-gray-500 text-sm">Funded</div>
                  <div className="font-bold text-indigo-400">{selectedProperty.funded_percentage}%</div>
                </div>
              </div>

              <h3 className="text-xl font-semibold text-white mb-2">About this property</h3>
              <p className="text-gray-400 mb-6">{selectedProperty.description}</p>

              <div className="mb-6">
                <div className="flex justify-between text-sm mb-1">
                  <span className="text-white">{selectedProperty.funded_percentage}% Funded</span>
                  <span className="text-gray-400">{selectedProperty.investors_count} Investors</span>
                </div>
                <div className="h-2 bg-gray-800 rounded-full overflow-hidden">
                  <div
                    className="h-full bg-gradient-to-r from-purple-500 to-indigo-500"
                    style={{ width: `${selectedProperty.funded_percentage}%` }}
                  ></div>
                </div>
              </div>
            </div>

            <div className="p-6 border-t border-gray-800">
              <button
                className="w-full py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-lg font-semibold hover:from-indigo-700 hover:to-purple-700 transition-colors"
              >
                Invest in Property
              </button>
            </div>
          </div>
        </div>
      )}
  );
}