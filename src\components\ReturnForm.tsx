import { useState, useEffect } from 'react';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

type Property = {
  id: string;
  name: string;
};

type Investor = {
  id: string;
  email: string;
};

type ReturnFormProps = {
  selectedReturn: {
    id: string;
    property_id: string;
    property_name: string;
    amount: number;
    date: string;
    status: 'pending' | 'completed' | 'failed';
    investor_id: string;
    investor_email: string;
  } | null;
  onSave: (data: any) => void;
  onCancel: () => void;
};

type FormData = {
  property_id: string;
  investor_id: string;
  amount: string;
  date: string;
  status: 'pending' | 'completed' | 'failed';
};

export default function ReturnForm({ selectedReturn, onSave, onCancel }: ReturnFormProps) {
  const [properties, setProperties] = useState<Property[]>([]);
  const [investors, setInvestors] = useState<Investor[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [formData, setFormData] = useState<FormData>({
    property_id: '',
    investor_id: '',
    amount: '',
    date: '',
    status: 'pending'
  });

  useEffect(() => {
    fetchData();
    if (selectedReturn) {
      setFormData({
        property_id: selectedReturn.property_id,
        investor_id: selectedReturn.investor_id,
        amount: selectedReturn.amount.toString(),
        date: selectedReturn.date,
        status: selectedReturn.status
      });
    }
  }, [selectedReturn]);

  const fetchData = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      // Fetch properties
      const { data: propertiesData, error: propertiesError } = await supabase
        .from('properties')
        .select('id, name')
        .eq('owner_id', user.id);

      if (propertiesError) throw propertiesError;

      // Fetch investors
      const { data: investorsData, error: investorsError } = await supabase
        .from('profiles')
        .select('id, email')
        .eq('role', 'investor');

      if (investorsError) throw investorsError;

      setProperties(propertiesData || []);
      setInvestors(investorsData || []);
    } catch (error) {
      console.error('Error fetching form data:', error);
      setError('Failed to load form data');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave({
      ...formData,
      amount: parseFloat(formData.amount)
    });
  };

  if (loading) {
    return <div className="text-center py-4">Loading...</div>;
  }

  if (error) {
    return <div className="text-red-500 text-center py-4">{error}</div>;
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label htmlFor="property" className="block text-sm font-medium text-gray-700">
          Property
        </label>
        <select
          id="property"
          value={formData.property_id}
          onChange={(e) => setFormData({ ...formData, property_id: e.target.value })}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          required
        >
          <option value="">Select a property</option>
          {properties.map((property) => (
            <option key={property.id} value={property.id}>
              {property.name}
            </option>
          ))}
        </select>
      </div>

      <div>
        <label htmlFor="investor" className="block text-sm font-medium text-gray-700">
          Investor
        </label>
        <select
          id="investor"
          value={formData.investor_id}
          onChange={(e) => setFormData({ ...formData, investor_id: e.target.value })}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          required
        >
          <option value="">Select an investor</option>
          {investors.map((investor) => (
            <option key={investor.id} value={investor.id}>
              {investor.email}
            </option>
          ))}
        </select>
      </div>

      <div>
        <label htmlFor="amount" className="block text-sm font-medium text-gray-700">
          Amount
        </label>
        <div className="mt-1 relative rounded-md shadow-sm">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <span className="text-gray-500 sm:text-sm">$</span>
          </div>
          <input
            type="number"
            id="amount"
            value={formData.amount}
            onChange={(e) => setFormData({ ...formData, amount: e.target.value })}
            className="block w-full pl-7 pr-12 rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500"
            placeholder="0.00"
            step="0.01"
            min="0"
            required
          />
        </div>
      </div>

      <div>
        <label htmlFor="date" className="block text-sm font-medium text-gray-700">
          Date
        </label>
        <input
          type="date"
          id="date"
          value={formData.date}
          onChange={(e) => setFormData({ ...formData, date: e.target.value })}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          required
        />
      </div>

      <div>
        <label htmlFor="status" className="block text-sm font-medium text-gray-700">
          Status
        </label>
        <select
          id="status"
          value={formData.status}
          onChange={(e) => setFormData({ ...formData, status: e.target.value as 'pending' | 'completed' | 'failed' })}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          required
        >
          <option value="pending">Pending</option>
          <option value="completed">Completed</option>
          <option value="failed">Failed</option>
        </select>
      </div>

      <div className="flex justify-end gap-2 mt-6">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 text-gray-600 hover:text-gray-800"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          {selectedReturn ? 'Update' : 'Add'} Return
        </button>
      </div>
    </form>
  );
} 