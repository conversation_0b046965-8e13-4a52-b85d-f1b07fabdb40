import { getContract } from 'wagmi/actions';
import { Address, parseEther } from 'viem';

// Import contract ABIs
import PropertyRegistryABI from '../../contracts/artifacts/contracts/PropertyRegistry.sol/PropertyRegistry.json';
import FractionalOwnershipABI from '../../contracts/artifacts/contracts/PropertyToken.sol/FractionalOwnership.json';
import BrickTokenABI from '../../contracts/artifacts/contracts/BrickToken.sol/BrickToken.json';
import InvestmentManagerABI from '../../contracts/artifacts/contracts/InvestmentManager.sol/InvestmentManager.json';
import AssetMarketplaceABI from '../../contracts/artifacts/contracts/Marketplace.sol/AssetMarketplace.json';

// Contract addresses (to be updated after deployment)
const CONTRACT_ADDRESSES = {
  propertyRegistry: process.env.NEXT_PUBLIC_PROPERTY_REGISTRY_ADDRESS as Address,
  fractionalOwnership: process.env.NEXT_PUBLIC_FRACTIONAL_OWNERSHIP_ADDRESS as Address,
  brickToken: process.env.NEXT_PUBLIC_BRICK_TOKEN_ADDRESS as Address,
  investmentManager: process.env.NEXT_PUBLIC_INVESTMENT_MANAGER_ADDRESS as Address,
  assetMarketplace: process.env.NEXT_PUBLIC_ASSET_MARKETPLACE_ADDRESS as Address,
};

/**
 * Get the PropertyRegistry contract instance (ERC-721)
 */
export function getPropertyRegistryContract(walletClient: any) {
  return getContract({
    address: CONTRACT_ADDRESSES.propertyRegistry,
    abi: PropertyRegistryABI.abi,
    walletClient,
  });
}

/**
 * Get the FractionalOwnership contract instance (ERC-1155)
 */
export function getFractionalOwnershipContract(walletClient: any) {
  return getContract({
    address: CONTRACT_ADDRESSES.fractionalOwnership,
    abi: FractionalOwnershipABI.abi,
    walletClient,
  });
}

/**
 * Get the BrickToken contract instance (ERC-20)
 */
export function getBrickTokenContract(walletClient: any) {
  return getContract({
    address: CONTRACT_ADDRESSES.brickToken,
    abi: BrickTokenABI.abi,
    walletClient,
  });
}

/**
 * Get the InvestmentManager contract instance
 */
export function getInvestmentManagerContract(walletClient: any) {
  return getContract({
    address: CONTRACT_ADDRESSES.investmentManager,
    abi: InvestmentManagerABI.abi,
    walletClient,
  });
}

/**
 * Get the AssetMarketplace contract instance
 */
export function getAssetMarketplaceContract(walletClient: any) {
  return getContract({
    address: CONTRACT_ADDRESSES.assetMarketplace,
    abi: AssetMarketplaceABI.abi,
    walletClient,
  });
}

/**
 * Fetch all properties from the registry
 */
export async function fetchProperties(publicClient: any) {
  try {
    const propertyRegistry = getContract({
      address: CONTRACT_ADDRESSES.propertyRegistry,
      abi: PropertyRegistryABI.abi,
      publicClient,
    });

    // Get all property IDs
    const propertyIds = await propertyRegistry.read.getAllPropertyIds();

    // Fetch each property
    const properties = await Promise.all(
      propertyIds.map(async (id: bigint) => {
        const property = await propertyRegistry.read.getProperty([id]);
        const isLocked = await propertyRegistry.read.isLocked([id]);

        // Get metadata URI
        const metadataURI = await propertyRegistry.read.tokenURI([id]);

        // Get fractionalization details if property is fractionalized
        let totalShares = 0;
        let availableShares = 0;

        if (isLocked) {
          const fractionalOwnership = getContract({
            address: CONTRACT_ADDRESSES.fractionalOwnership,
            abi: FractionalOwnershipABI.abi,
            publicClient,
          });

          totalShares = Number(await fractionalOwnership.read.getTotalShares([id]));
          availableShares = Number(await fractionalOwnership.read.getAvailableShares([id]));
        }

        return {
          id: Number(id),
          name: property.name,
          location: property.location,
          valuation: Number(property.valuation),
          fractionalized: property.fractionalized,
          owner: property.owner,
          metadataURI,
          totalShares,
          availableShares,
        };
      })
    );

    return properties;
  } catch (error) {
    console.error('Error fetching properties:', error);
    return [];
  }
}

/**
 * Mint a new property
 */
export async function mintProperty(
  walletClient: any,
  name: string,
  location: string,
  valuation: number,
  metadataURI: string
) {
  try {
    const propertyRegistry = getPropertyRegistryContract(walletClient);

    // Execute transaction
    const hash = await propertyRegistry.write.mintProperty(
      [
        await walletClient.account.address,
        name,
        location,
        parseEther(valuation.toString()),
        metadataURI
      ]
    );

    return hash;
  } catch (error) {
    console.error('Error minting property:', error);
    throw error;
  }
}

/**
 * Fractionalize a property
 */
export async function fractionalizeProperty(
  walletClient: any,
  propertyId: number,
  totalShares: number,
  pricePerShare: number
) {
  try {
    const investmentManager = getInvestmentManagerContract(walletClient);
    const propertyRegistry = getPropertyRegistryContract(walletClient);

    // Approve investment manager to transfer the property
    await propertyRegistry.write.approve(
      [CONTRACT_ADDRESSES.investmentManager, BigInt(propertyId)]
    );

    // Execute fractionalization
    const hash = await investmentManager.write.fractionalizeProperty(
      [
        BigInt(propertyId),
        BigInt(totalShares),
        parseEther(pricePerShare.toString())
      ]
    );

    return hash;
  } catch (error) {
    console.error('Error fractionalizing property:', error);
    throw error;
  }
}

/**
 * Buy shares of a property using ETH
 */
export async function buyShares(
  walletClient: any,
  propertyId: number,
  shares: number
) {
  try {
    const investmentManager = getInvestmentManagerContract(walletClient);

    // Get property details to calculate price
    const propertyRegistry = getContract({
      address: CONTRACT_ADDRESSES.propertyRegistry,
      abi: PropertyRegistryABI.abi,
      walletClient,
    });

    const property = await propertyRegistry.read.getProperty([BigInt(propertyId)]);
    const fractionalOwnership = getContract({
      address: CONTRACT_ADDRESSES.fractionalOwnership,
      abi: FractionalOwnershipABI.abi,
      walletClient,
    });

    const totalShares = await fractionalOwnership.read.getTotalShares([BigInt(propertyId)]);
    const sharePrice = property.valuation / totalShares;
    const investmentAmount = sharePrice * BigInt(shares);

    // Execute transaction
    const hash = await investmentManager.write.buyShares(
      [BigInt(propertyId), BigInt(shares)],
      { value: investmentAmount }
    );

    return hash;
  } catch (error) {
    console.error('Error buying shares:', error);
    throw error;
  }
}

/**
 * Buy shares of a property using BCT tokens
 */
export async function buySharesWithBCT(
  walletClient: any,
  propertyId: number,
  shares: number
) {
  try {
    const investmentManager = getInvestmentManagerContract(walletClient);
    const brickToken = getBrickTokenContract(walletClient);

    // Approve BCT tokens for investment manager
    await brickToken.write.approve(
      [CONTRACT_ADDRESSES.investmentManager, ethers.constants.MaxUint256]
    );

    // Execute transaction
    const hash = await investmentManager.write.buySharesWithBCT(
      [BigInt(propertyId), BigInt(shares)]
    );

    return hash;
  } catch (error) {
    console.error('Error buying shares with BCT:', error);
    throw error;
  }
}

/**
 * List a property for sale
 */
export async function listProperty(
  walletClient: any,
  propertyId: number,
  price: number,
  acceptBCT: boolean
) {
  try {
    const assetMarketplace = getAssetMarketplaceContract(walletClient);
    const propertyRegistry = getPropertyRegistryContract(walletClient);

    // Approve marketplace to transfer the property
    await propertyRegistry.write.approve(
      [CONTRACT_ADDRESSES.assetMarketplace, BigInt(propertyId)]
    );

    // Execute transaction
    const hash = await assetMarketplace.write.listProperty(
      [
        BigInt(propertyId),
        parseEther(price.toString()),
        acceptBCT
      ]
    );

    return hash;
  } catch (error) {
    console.error('Error listing property:', error);
    throw error;
  }
}

/**
 * List shares for sale
 */
export async function listShares(
  walletClient: any,
  propertyId: number,
  shares: number,
  price: number,
  acceptBCT: boolean
) {
  try {
    const assetMarketplace = getAssetMarketplaceContract(walletClient);
    const fractionalOwnership = getFractionalOwnershipContract(walletClient);

    // Approve marketplace to transfer shares
    await fractionalOwnership.write.setApprovalForAll(
      [CONTRACT_ADDRESSES.assetMarketplace, true]
    );

    // Execute transaction
    const hash = await assetMarketplace.write.listShares(
      [
        BigInt(propertyId),
        BigInt(shares),
        parseEther(price.toString()),
        acceptBCT
      ]
    );

    return hash;
  } catch (error) {
    console.error('Error listing shares:', error);
    throw error;
  }
}

/**
 * Purchase a listing with ETH
 */
export async function purchase(
  walletClient: any,
  listingId: number,
  price: number
) {
  try {
    const assetMarketplace = getAssetMarketplaceContract(walletClient);

    // Execute transaction
    const hash = await assetMarketplace.write.purchase(
      [BigInt(listingId)],
      { value: parseEther(price.toString()) }
    );

    return hash;
  } catch (error) {
    console.error('Error purchasing listing:', error);
    throw error;
  }
}

/**
 * Purchase a listing with BCT tokens
 */
export async function purchaseWithBCT(
  walletClient: any,
  listingId: number
) {
  try {
    const assetMarketplace = getAssetMarketplaceContract(walletClient);
    const brickToken = getBrickTokenContract(walletClient);

    // Get listing details
    const listing = await assetMarketplace.read.getListing([BigInt(listingId)]);

    // Approve BCT tokens for marketplace
    await brickToken.write.approve(
      [CONTRACT_ADDRESSES.assetMarketplace, listing.price]
    );

    // Execute transaction
    const hash = await assetMarketplace.write.purchaseWithBCT(
      [BigInt(listingId)]
    );

    return hash;
  } catch (error) {
    console.error('Error purchasing with BCT:', error);
    throw error;
  }
}

/**
 * Fetch user's investments
 */
export async function fetchUserInvestments(
  publicClient: any,
  userAddress: Address
) {
  try {
    const fractionalOwnership = getContract({
      address: CONTRACT_ADDRESSES.fractionalOwnership,
      abi: FractionalOwnershipABI.abi,
      publicClient,
    });

    const propertyRegistry = getContract({
      address: CONTRACT_ADDRESSES.propertyRegistry,
      abi: PropertyRegistryABI.abi,
      publicClient,
    });

    // Get all property IDs
    const propertyIds = await propertyRegistry.read.getAllPropertyIds();

    // Fetch investments for each property
    const investments = await Promise.all(
      propertyIds.map(async (id: bigint) => {
        try {
          // Check if user has shares in this property
          const shares = await fractionalOwnership.read.balanceOf([userAddress, id]);

          if (shares === 0n) return null;

          // Get property details
          const property = await propertyRegistry.read.getProperty([id]);

          // Get share percentage
          const sharePercentage = await fractionalOwnership.read.getSharePercentage([userAddress, id]);

          return {
            propertyId: Number(id),
            propertyName: property.name,
            propertyLocation: property.location,
            shares: Number(shares),
            sharePercentage: Number(sharePercentage) / 100, // Convert to percentage
            valuation: Number(property.valuation),
          };
        } catch (error) {
          console.error(`Error fetching investments for property ${id}:`, error);
          return null;
        }
      })
    );

    return investments.filter(Boolean);
  } catch (error) {
    console.error('Error fetching user investments:', error);
    return [];
  }
}

/**
 * Get BCT token balance
 */
export async function getBCTBalance(
  publicClient: any,
  userAddress: Address
) {
  try {
    const brickToken = getContract({
      address: CONTRACT_ADDRESSES.brickToken,
      abi: BrickTokenABI.abi,
      publicClient,
    });

    const balance = await brickToken.read.balanceOf([userAddress]);
    return balance;
  } catch (error) {
    console.error('Error fetching BCT balance:', error);
    return 0n;
  }
}
