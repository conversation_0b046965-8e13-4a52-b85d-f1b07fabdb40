'use client';

import React, { useEffect, useState } from 'react';
import NextLink from 'next/link';
import { usePathname, useRouter } from 'next/navigation';

interface OptimizedLinkProps {
  href: string;
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
  onMouseEnter?: () => void;
  prefetch?: boolean;
  activeClassName?: string;
  exactMatch?: boolean;
}

/**
 * OptimizedLink enhances Next.js Link with improved prefetching and navigation
 * to make page transitions feel instant.
 */
export default function OptimizedLink({
  href,
  children,
  className = '',
  onClick,
  onMouseEnter,
  prefetch = true,
  activeClassName = '',
  exactMatch = false,
}: OptimizedLinkProps) {
  const pathname = usePathname();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [mounted, setMounted] = useState(false);

  // Only determine active state after component has mounted on the client
  useEffect(() => {
    setMounted(true);
  }, []);

  // Determine if the link is active (only on client-side)
  const isActive = mounted && (exactMatch
    ? pathname === href
    : pathname === href || pathname?.startsWith(`${href}/`));

  // Combine classes based on active state
  const combinedClassName = `${className} ${isActive && activeClassName ? activeClassName : ''}`.trim();

  // Prefetch the page when the component mounts
  useEffect(() => {
    if (prefetch) {
      // Prefetch the page
      const prefetchPage = async () => {
        try {
          await router.prefetch(href);
        } catch (error) {
          console.error(`Failed to prefetch page: ${href}`, error);
        }
      };

      prefetchPage();
    }
  }, [href, prefetch, router]);

  // Handle click with loading state
  const handleClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    if (onClick) {
      onClick();
    }

    // If it's an external link or has a special modifier key pressed, let the browser handle it
    if (
      href.startsWith('http') ||
      href.startsWith('mailto:') ||
      href.startsWith('tel:') ||
      e.metaKey ||
      e.ctrlKey ||
      e.shiftKey
    ) {
      return;
    }

    // For internal links, show loading state
    setIsLoading(true);

    // Reset loading state after navigation (or after a timeout as fallback)
    setTimeout(() => {
      setIsLoading(false);
    }, 500);
  };

  return (
    <NextLink
      href={href}
      className={combinedClassName}
      onClick={handleClick}
      onMouseEnter={onMouseEnter}
      prefetch={prefetch}
    >
      {isLoading ? (
        <span className="inline-flex items-center">
          {children}
          <span className="sr-only">Loading...</span>
        </span>
      ) : (
        children
      )}
    </NextLink>
  );
}
