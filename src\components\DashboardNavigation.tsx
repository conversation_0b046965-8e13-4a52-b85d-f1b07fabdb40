'use client';

import React, { useState, useEffect } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { useUser } from '@/context/UserContext';
import { useWallet } from '@/context/WalletContext';
import { useNavigation } from '@/context/NavigationContext';
import {
  Home, Building, Wallet, Settings, Users, ChartBar,
  Menu, X, LogOut, TrendingUp, History, Search,
  LayoutDashboard, PlusCircle, Bell, ExternalLink,
  HardDrive, Shield
} from 'lucide-react';
import ConnectWalletButton from './ConnectWalletButton';
import OptimizedLink from './OptimizedLink';
import { formatAddress } from '@/utils/web3';

// Define navigation items for each role
const navigationConfig = {
  owner: [
    { href: '/dashboard/owner', label: 'Overview', icon: LayoutDashboard },
    { href: '/dashboard/owner/properties', label: 'My Properties', icon: Building },
    { href: '/dashboard/owner/investments', label: 'Investments', icon: Wallet },
    { href: '/dashboard/owner/analytics', label: 'Analytics', icon: ChartBar },
  ],
  investor: [
    { href: '/dashboard/investor', label: 'Overview', icon: LayoutDashboard },
    { href: '/dashboard/investor/portfolio', label: 'My Portfolio', icon: Wallet },
    { href: '/dashboard/investor/opportunities', label: 'Opportunities', icon: Building },
    { href: '/dashboard/investor/history', label: 'History', icon: History },
  ],
  admin: [
    { href: '/dashboard/admin', label: 'Overview', icon: LayoutDashboard },
    { href: '/dashboard/admin/users', label: 'Users', icon: Users },
    { href: '/dashboard/admin/properties', label: 'Properties', icon: Building },
    { href: '/dashboard/admin/analytics', label: 'Analytics', icon: ChartBar },
  ],
};

// Common actions for all roles
const commonActions = [
  { href: '/marketplace', label: 'Marketplace', icon: Search },
  { href: '/dashboard/kyc', label: 'Identity Verification', icon: Shield },
  { href: '/settings', label: 'Settings', icon: Settings },
  { href: '/dashboard/setup-storage', label: 'Setup Storage', icon: HardDrive },
];

type DashboardNavigationProps = {
  children: React.ReactNode;
};

export default function DashboardNavigation({ children }: DashboardNavigationProps) {
  const { user, signOut } = useUser();
  const { isConnected, address, displayAddress, balance, chainId, networkName } = useWallet();
  const pathname = usePathname();
  const router = useRouter();
  const { preloadRoute } = useNavigation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Preload routes on hover
  const handleLinkHover = (href: string) => {
    preloadRoute(href);
  };

  // Close mobile menu when route changes
  useEffect(() => {
    setIsMobileMenuOpen(false);
  }, [pathname]);

  // Get role-specific navigation items
  const role = user?.role || 'investor';
  const navItems = navigationConfig[role] || [];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-950 to-indigo-950">
      <div className="flex flex-1 h-screen">
        {/* Sidebar Navigation - Desktop */}
        <aside className="hidden md:flex flex-col w-64 bg-gray-900/50 border-r border-gray-800 h-full">
          <div className="flex-1 overflow-y-auto py-6 px-4">
            <nav className="space-y-1">
              {navItems.map((item) => {
                const isActive = pathname === item.href;
                return (
                  <OptimizedLink
                    key={item.href}
                    href={item.href}
                    className="flex items-center px-4 py-3 rounded-lg transition-colors text-gray-300 hover:bg-indigo-800/20 hover:text-white"
                    activeClassName="bg-indigo-700/30 text-cyan-400 font-medium"
                    exactMatch={true}
                    onMouseEnter={() => handleLinkHover(item.href)}
                  >
                    <item.icon size={20} className={`mr-3 ${isActive ? 'text-cyan-400' : 'text-gray-400'}`} />
                    <span>{item.label}</span>
                  </OptimizedLink>
                );
              })}

              {/* Divider */}
              <div className="border-t border-gray-800 my-4"></div>

              {/* Common Actions */}
              {commonActions.map((item) => (
                <OptimizedLink
                  key={item.href}
                  href={item.href}
                  className="flex items-center px-4 py-3 rounded-lg text-gray-300 hover:bg-indigo-800/20 hover:text-white transition-colors"
                  activeClassName="bg-indigo-700/30 text-cyan-400 font-medium"
                  onMouseEnter={() => handleLinkHover(item.href)}
                >
                  <item.icon size={20} className="mr-3 text-gray-400" />
                  <span>{item.label}</span>
                </OptimizedLink>
              ))}
            </nav>
          </div>

          {/* Wallet Info */}
          <div className="p-4 border-t border-gray-800">
            {isConnected ? (
              <div className="bg-gray-800/50 p-4 rounded-xl">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-gradient-to-br from-cyan-500 to-indigo-500 rounded-full"></div>
                    <div>
                      <p className="text-sm font-medium text-white">Connected</p>
                      <p className="text-xs text-gray-400">{displayAddress}</p>
                    </div>
                  </div>
                </div>
                {balance && (
                  <div className="mt-2 border-t border-gray-700 pt-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">Balance</span>
                      <span className="text-white">{balance}</span>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <ConnectWalletButton variant="sidebar" />
            )}
          </div>

          {/* Logout Button */}
          <div className="p-4 border-t border-gray-800">
            <button
              onClick={async () => {
                await signOut();
              }}
              className="w-full flex items-center gap-2 px-4 py-2 bg-red-700/80 hover:bg-red-800 text-white rounded-lg transition-colors"
            >
              <LogOut size={18} />
              <span>Logout</span>
            </button>
          </div>
        </aside>

        {/* Mobile Navigation Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden fixed inset-0 z-20 bg-gray-900/95 pt-16">
            <div className="p-4">
              <nav className="space-y-2">
                {navItems.map((item) => (
                  <OptimizedLink
                    key={item.href}
                    href={item.href}
                    className="flex items-center px-4 py-3 rounded-lg text-gray-300 hover:bg-indigo-800/20 hover:text-white transition-colors"
                    activeClassName="bg-indigo-700/30 text-cyan-400 font-medium"
                    exactMatch={true}
                    onMouseEnter={() => handleLinkHover(item.href)}
                  >
                    <item.icon size={20} className="mr-3 text-gray-400" />
                    <span>{item.label}</span>
                  </OptimizedLink>
                ))}

                {/* Divider */}
                <div className="border-t border-gray-800 my-4"></div>

                {/* Common Actions */}
                {commonActions.map((item) => (
                  <OptimizedLink
                    key={item.href}
                    href={item.href}
                    className="flex items-center px-4 py-3 rounded-lg text-gray-300 hover:bg-indigo-800/20 hover:text-white transition-colors"
                    activeClassName="bg-indigo-700/30 text-cyan-400 font-medium"
                    onMouseEnter={() => handleLinkHover(item.href)}
                  >
                    <item.icon size={20} className="mr-3 text-gray-400" />
                    <span>{item.label}</span>
                  </OptimizedLink>
                ))}

                {/* Wallet Connection */}
                <div className="mt-4">
                  <ConnectWalletButton variant="sidebar" />
                </div>

                {/* Logout Button */}
                <button
                  onClick={async () => {
                    await signOut();
                  }}
                  className="w-full flex items-center gap-2 px-4 py-3 mt-4 bg-red-700/80 hover:bg-red-800 text-white rounded-lg transition-colors"
                >
                  <LogOut size={20} className="mr-3" />
                  <span>Logout</span>
                </button>
              </nav>
            </div>
          </div>
        )}

        {/* Main Content */}
        <main className="flex-1 overflow-y-auto">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
