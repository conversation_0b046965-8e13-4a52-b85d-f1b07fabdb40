'use client';

import React from 'react';
import DashboardNavigation from '@/components/DashboardNavigation';
import { useUser } from '@/context/UserContext';
import { Loader2, AlertTriangle } from 'lucide-react';
import Link from 'next/link';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { user, loading } = useUser();

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-gray-950 to-indigo-950">
        <div className="text-center">
          <Loader2 size={40} className="mx-auto animate-spin text-indigo-400 mb-4" />
          <p className="text-gray-400">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-gray-950 to-indigo-950">
        <div className="bg-red-900/40 border border-red-800 rounded-lg p-6 max-w-md">
          <div className="flex items-center space-x-3 mb-4">
            <AlertTriangle size={24} className="text-red-400" />
            <h2 className="text-xl font-bold text-white">Authentication Required</h2>
          </div>
          <p className="text-red-300 mb-6">You need to be signed in to access this dashboard.</p>
          <div className="flex space-x-4">
            <Link href="/" className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 rounded-lg text-white font-medium text-center transition-colors flex-1">
              Connect Wallet
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <DashboardNavigation>
      {children}
    </DashboardNavigation>
  );
}
