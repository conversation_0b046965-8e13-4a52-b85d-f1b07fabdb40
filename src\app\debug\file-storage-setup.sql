-- File Storage System Setup for BrickChain
-- Run this in your Supabase SQL Editor

-- Start a transaction so we can roll back if anything goes wrong
BEGIN;

-- Create property_images table for multiple images per property
CREATE TABLE IF NOT EXISTS property_images (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  property_id UUID REFERENCES properties(id) ON DELETE CASCADE,
  image_url TEXT NOT NULL,
  image_cid TEXT,
  thumbnail_url TEXT,
  image_type VARCHAR(50) NOT NULL, -- 'primary', 'additional', 'floorplan', 'document'
  file_name TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  file_type TEXT NOT NULL,
  width INTEGER,
  height INTEGER,
  optimized BOOLEAN DEFAULT FALSE,
  sort_order INTEGER DEFAULT 0, -- For ordering images
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create enum for document types if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'kyc_document_type') THEN
    CREATE TYPE kyc_document_type AS ENUM (
      'passport', 'national_id', 'drivers_license',
      'proof_of_address', 'bank_statement', 'other'
    );
  END IF;
END$$;

-- Create enum for verification status if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'verification_status') THEN
    CREATE TYPE verification_status AS ENUM (
      'pending', 'verified', 'rejected', 'expired'
    );
  END IF;
END$$;

-- Create KYC documents table
CREATE TABLE IF NOT EXISTS kyc_documents (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  document_type kyc_document_type NOT NULL,
  document_category VARCHAR(50) NOT NULL, -- 'identity', 'address', 'financial'
  file_path TEXT NOT NULL,
  file_name TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  file_type TEXT NOT NULL,
  verification_status verification_status DEFAULT 'pending',
  verification_notes TEXT,
  verified_by UUID REFERENCES auth.users(id),
  verified_at TIMESTAMP WITH TIME ZONE,
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create audit logs table for tracking document verification actions
CREATE TABLE IF NOT EXISTS audit_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  action TEXT NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  target_id UUID,
  target_type TEXT NOT NULL,
  details JSONB,
  ip_address TEXT,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create function to update the updated_at field if it doesn't exist
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add trigger for property_images updated_at
DROP TRIGGER IF EXISTS set_property_images_updated_at ON property_images;
CREATE TRIGGER set_property_images_updated_at
BEFORE UPDATE ON property_images
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();

-- Add trigger for kyc_documents updated_at
DROP TRIGGER IF EXISTS set_kyc_documents_updated_at ON kyc_documents;
CREATE TRIGGER set_kyc_documents_updated_at
BEFORE UPDATE ON kyc_documents
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();

-- Add RLS policies for kyc_documents
ALTER TABLE kyc_documents ENABLE ROW LEVEL SECURITY;

-- Users can only view their own documents
CREATE POLICY "Users can view their own documents"
  ON kyc_documents FOR SELECT
  USING (auth.uid() = user_id);

-- Users can only insert their own documents
CREATE POLICY "Users can insert their own documents"
  ON kyc_documents FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Only admins can update verification status
CREATE POLICY "Only admins can update verification status"
  ON kyc_documents FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Add RLS policies for property_images
ALTER TABLE property_images ENABLE ROW LEVEL SECURITY;

-- Anyone can view property images
CREATE POLICY "Anyone can view property images"
  ON property_images FOR SELECT
  USING (true);

-- Only property owners can insert images
CREATE POLICY "Only property owners can insert images"
  ON property_images FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM properties p
      JOIN profiles pr ON p.owner_id = pr.id
      WHERE p.id = property_id AND pr.id = auth.uid()
    )
  );

-- Only property owners can update images
CREATE POLICY "Only property owners can update images"
  ON property_images FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM properties p
      JOIN profiles pr ON p.owner_id = pr.id
      WHERE p.id = property_id AND pr.id = auth.uid()
    )
  );

-- Only property owners can delete images
CREATE POLICY "Only property owners can delete images"
  ON property_images FOR DELETE
  USING (
    EXISTS (
      SELECT 1 FROM properties p
      JOIN profiles pr ON p.owner_id = pr.id
      WHERE p.id = property_id AND pr.id = auth.uid()
    )
  );

-- Commit the transaction
COMMIT;

-- Note: After running this SQL, go to the Supabase Dashboard and refresh the schema cache
-- 1. Visit the Supabase Dashboard
-- 2. Go to Database > Schema
-- 3. Click "Refresh" in the Schema Editor
