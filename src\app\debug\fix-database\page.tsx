'use client';

import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Loader2, CheckCircle, AlertCircle, Database } from 'lucide-react';

export default function FixDatabasePage() {
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [log, setLog] = useState<string[]>([]);
  const supabase = createClientComponentClient();

  const fixDatabase = async () => {
    setLoading(true);
    setSuccess(false);
    setError(null);
    setLog(['Starting database fix...']);

    try {
      // Check if the user is authenticated and has admin privileges
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('You must be logged in to perform this action');
      }

      // Check if user is admin (optional, remove if not needed)
      const { data: profile } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single();

      if (profile?.role !== 'admin' && profile?.role !== 'owner') {
        throw new Error('You must be an admin or owner to perform this action');
      }

      setLog(prev => [...prev, 'Authentication verified. Proceeding with database fix...']);

      // Fix the investments table by adding the missing amount column
      const { error: columnError } = await supabase.rpc('execute_sql', {
        sql: `
          -- Check if the amount column exists in the investments table
          DO $$
          BEGIN
              IF NOT EXISTS (
                  SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'investments' 
                  AND column_name = 'amount'
              ) THEN
                  -- Add the amount column
                  ALTER TABLE investments ADD COLUMN amount NUMERIC DEFAULT 0;
                  RAISE NOTICE 'Added missing amount column to investments table';
              ELSE
                  RAISE NOTICE 'Amount column already exists in investments table';
              END IF;
              
              -- Check if date_invested column exists
              IF NOT EXISTS (
                  SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'investments' 
                  AND column_name = 'date_invested'
              ) THEN
                  -- Add date_invested column
                  ALTER TABLE investments ADD COLUMN date_invested TIMESTAMP WITH TIME ZONE DEFAULT NOW();
                  RAISE NOTICE 'Added date_invested column to investments table';
              ELSE
                  RAISE NOTICE 'date_invested column already exists in investments table';
              END IF;
          END$$;
        `
      });

      if (columnError) {
        throw new Error(`Error adding columns: ${columnError.message}`);
      }

      setLog(prev => [...prev, 'Successfully added missing columns to investments table']);

      // Verify the investments table structure
      const { data: columns, error: verifyError } = await supabase.rpc('execute_sql', {
        sql: `
          SELECT 
              column_name, 
              data_type
          FROM 
              information_schema.columns
          WHERE 
              table_name = 'investments'
          ORDER BY 
              ordinal_position;
        `
      });

      if (verifyError) {
        throw new Error(`Error verifying table structure: ${verifyError.message}`);
      }

      setLog(prev => [...prev, 'Verified investments table structure:', 
        ...columns.map((col: any) => `- ${col.column_name}: ${col.data_type}`)]);

      setSuccess(true);
      setLog(prev => [...prev, 'Database fix completed successfully!']);
    } catch (err: any) {
      console.error('Error fixing database:', err);
      setError(err.message || 'An unknown error occurred');
      setLog(prev => [...prev, `ERROR: ${err.message || 'An unknown error occurred'}`]);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-950 to-indigo-950 text-white p-8">
      <div className="max-w-3xl mx-auto">
        <div className="flex items-center mb-6">
          <Database className="h-8 w-8 mr-3 text-indigo-400" />
          <h1 className="text-3xl font-bold">Database Fix Utility</h1>
        </div>

        <div className="bg-indigo-900/50 border border-indigo-800 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Fix Missing Columns</h2>
          <p className="text-gray-300 mb-6">
            This utility will add the missing <code className="bg-indigo-800 px-2 py-1 rounded">amount</code> column 
            to the investments table, resolving the error in the owner dashboard.
          </p>

          <button
            onClick={fixDatabase}
            disabled={loading}
            className={`px-4 py-2 rounded-lg flex items-center ${
              loading 
                ? 'bg-indigo-700 cursor-not-allowed' 
                : 'bg-indigo-600 hover:bg-indigo-700'
            } transition-colors`}
          >
            {loading ? (
              <>
                <Loader2 className="animate-spin h-5 w-5 mr-2" />
                Fixing Database...
              </>
            ) : (
              'Fix Database'
            )}
          </button>
        </div>

        {(success || error || log.length > 0) && (
          <div className="bg-gray-900/50 border border-gray-800 rounded-lg p-6">
            {success && (
              <div className="flex items-center mb-4 text-green-400">
                <CheckCircle className="h-5 w-5 mr-2" />
                <span>Database fix completed successfully!</span>
              </div>
            )}

            {error && (
              <div className="flex items-center mb-4 text-red-400">
                <AlertCircle className="h-5 w-5 mr-2" />
                <span>{error}</span>
              </div>
            )}

            {log.length > 0 && (
              <div className="mt-4">
                <h3 className="text-lg font-medium mb-2">Log:</h3>
                <div className="bg-black/30 p-4 rounded-lg font-mono text-sm overflow-auto max-h-96">
                  {log.map((line, i) => (
                    <div key={i} className="mb-1">
                      {line}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
