'use client';
import React, { useState, useEffect } from 'react';
import { useSupabaseClient } from '@supabase/auth-helpers-react';
import { useUser } from '@/context/UserContext';
import { Shield, AlertCircle, CheckCircle, Clock, FileText } from 'lucide-react';
import KYCDocumentUploader from '@/components/KYCDocumentUploader';
import LoadingSpinner from '@/components/LoadingSpinner';
import { getUserKYCDocuments, type KYCDocument } from '@/utils/fileStorage';

export default function KYCPage() {
  const { user } = useUser();
  const supabase = useSupabaseClient();
  const [loading, setLoading] = useState<boolean>(true);
  const [documents, setDocuments] = useState<KYCDocument[]>([]);
  const [error, setError] = useState<string>('');
  const [verificationStatus, setVerificationStatus] = useState<'pending' | 'verified' | 'rejected'>('pending');

  useEffect(() => {
    const fetchDocuments = async () => {
      if (!user?.id) return;
      
      try {
        setLoading(true);
        const docs = await getUserKYCDocuments(user.id);
        setDocuments(docs);
        
        // Determine overall verification status
        if (docs.some(doc => doc.verification_status === 'rejected')) {
          setVerificationStatus('rejected');
        } else if (docs.length > 0 && docs.every(doc => doc.verification_status === 'verified')) {
          setVerificationStatus('verified');
        } else {
          setVerificationStatus('pending');
        }
      } catch (err) {
        console.error('Error fetching KYC documents:', err);
        setError('Failed to load your verification documents');
      } finally {
        setLoading(false);
      }
    };

    if (user?.id) {
      fetchDocuments();
    } else {
      setLoading(false);
    }
  }, [user?.id]);

  const handleDocumentChange = (newDocument: KYCDocument | null) => {
    if (newDocument) {
      setDocuments(prev => {
        // Replace if document of same type exists, otherwise add
        const exists = prev.some(doc => 
          doc.document_type === newDocument.document_type && 
          doc.document_category === newDocument.document_category
        );
        
        if (exists) {
          return prev.map(doc => 
            (doc.document_type === newDocument.document_type && 
             doc.document_category === newDocument.document_category)
              ? newDocument
              : doc
          );
        } else {
          return [...prev, newDocument];
        }
      });
    }
  };

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-white rounded-lg shadow-md p-6 text-center">
          <p className="text-gray-700">Please log in to access your KYC verification page.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">Identity Verification</h1>
          <p className="text-gray-600 mt-1">
            Complete your KYC (Know Your Customer) verification to unlock full platform functionality.
          </p>
          
          {/* Verification Status Banner */}
          <div className={`mt-4 p-4 rounded-lg flex items-center ${
            verificationStatus === 'verified' 
              ? 'bg-green-50 border border-green-200' 
              : verificationStatus === 'rejected'
                ? 'bg-red-50 border border-red-200'
                : 'bg-blue-50 border border-blue-200'
          }`}>
            {verificationStatus === 'verified' ? (
              <>
                <CheckCircle className="h-6 w-6 text-green-500 mr-3" />
                <div>
                  <p className="font-medium text-green-800">Verification Complete</p>
                  <p className="text-sm text-green-700">Your identity has been verified. You now have full access to the platform.</p>
                </div>
              </>
            ) : verificationStatus === 'rejected' ? (
              <>
                <AlertCircle className="h-6 w-6 text-red-500 mr-3" />
                <div>
                  <p className="font-medium text-red-800">Verification Rejected</p>
                  <p className="text-sm text-red-700">One or more of your documents has been rejected. Please review the feedback and resubmit.</p>
                </div>
              </>
            ) : (
              <>
                <Clock className="h-6 w-6 text-blue-500 mr-3" />
                <div>
                  <p className="font-medium text-blue-800">Verification In Progress</p>
                  <p className="text-sm text-blue-700">Your documents are being reviewed. This process typically takes 1-2 business days.</p>
                </div>
              </>
            )}
          </div>
        </div>
        
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg text-red-700">
            <div className="flex items-center">
              <AlertCircle className="h-5 w-5 mr-2" />
              <span>{error}</span>
            </div>
          </div>
        )}
        
        {loading ? (
          <div className="flex justify-center py-8">
            <LoadingSpinner size="large" />
          </div>
        ) : (
          <div className="space-y-8">
            {/* Identity Documents Section */}
            <div className="border border-gray-200 rounded-lg p-6">
              <h2 className="text-xl font-semibold text-gray-800 mb-4">Identity Documents</h2>
              <p className="text-gray-600 mb-6">
                Please provide one of the following government-issued photo ID documents.
              </p>
              
              <div className="space-y-6">
                <KYCDocumentUploader
                  documentType="passport"
                  documentCategory="identity"
                  onDocumentChange={handleDocumentChange}
                  encrypt={true}
                  theme="light"
                />
                
                <div className="border-t border-gray-200 pt-6">
                  <KYCDocumentUploader
                    documentType="national_id"
                    documentCategory="identity"
                    onDocumentChange={handleDocumentChange}
                    encrypt={true}
                    theme="light"
                  />
                </div>
                
                <div className="border-t border-gray-200 pt-6">
                  <KYCDocumentUploader
                    documentType="drivers_license"
                    documentCategory="identity"
                    onDocumentChange={handleDocumentChange}
                    encrypt={true}
                    theme="light"
                  />
                </div>
              </div>
            </div>
            
            {/* Address Verification Section */}
            <div className="border border-gray-200 rounded-lg p-6">
              <h2 className="text-xl font-semibold text-gray-800 mb-4">Proof of Address</h2>
              <p className="text-gray-600 mb-6">
                Please provide a document showing your current residential address (issued within the last 3 months).
              </p>
              
              <KYCDocumentUploader
                documentType="proof_of_address"
                documentCategory="address"
                onDocumentChange={handleDocumentChange}
                encrypt={true}
                theme="light"
              />
            </div>
            
            {/* Financial Information Section */}
            <div className="border border-gray-200 rounded-lg p-6">
              <h2 className="text-xl font-semibold text-gray-800 mb-4">Financial Information</h2>
              <p className="text-gray-600 mb-6">
                Please provide a recent bank statement or other financial document.
              </p>
              
              <KYCDocumentUploader
                documentType="bank_statement"
                documentCategory="financial"
                onDocumentChange={handleDocumentChange}
                encrypt={true}
                theme="light"
              />
            </div>
            
            {/* Privacy Notice */}
            <div className="bg-gray-50 rounded-lg p-6">
              <div className="flex items-start">
                <Shield className="h-6 w-6 text-gray-500 mr-3 mt-0.5" />
                <div>
                  <h3 className="font-medium text-gray-900">Privacy & Security</h3>
                  <p className="text-sm text-gray-600 mt-1">
                    Your documents are encrypted and stored securely. We comply with all relevant data protection regulations.
                    Documents are only accessible to authorized personnel for verification purposes.
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
