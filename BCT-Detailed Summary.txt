Thought for a couple of seconds


I've updated **BCT‑Detailed Summary.txt** to include the **Next.js App‑Router structure**, the **essential pages**, and our **overnight MVP cycle**. Here's the complete, consolidated document ready for your team:

---

# BCT‑Detailed Summary (MVP Edition)

---

## 🧱 PROJECT OVERVIEW

**Name:** BrickChain
**Vision:** Democratize real‑estate investment via blockchain tokenization (full & fractional ownership).

---

## 🚨 PROBLEM & SOLUTION

**Problem:** High entry capital, low liquidity, complex legal structures.
**Solution:**

* ERC‑721 for full‑ownership NFTs
* ERC‑1155 for fractional shares
* BCT (ERC‑20) utility token for fee discounts & rewards

---

## 📄 SMART CONTRACTS

1. **PropertyRegistry (ERC‑721)** – mint, lock/unlock, transfer guards.
2. **FractionalOwnership (ERC‑1155)** – mint/burn shares (100–100 000), track distribution.
3. **BrickToken (ERC‑20)** – BCT supply, burn/mint, used for discounted fees.
4. **AssetMarketplace** – list/buy shares or full NFTs in ETH or BCT, handles fees.
5. **InvestmentManager** – orchestrates fractionalization, share sales, full‑ownership reclaim.

Logic details and events as previously specified.

---

## 🔁 LIFECYCLE FLOW

1. Owner uploads image & docs → IPFS via web3.storage
2. Build/upload `metadata.json` (see template below)
3. Call `mintProperty(metadataURI)` → ERC‑721 minted & stored in Supabase
4. Owner "fractionalizes" → lock ERC‑721 + mint ERC‑1155 shares
5. Investors buy shares via `buyShares()` (ETH/BCT)
6. If one wallet holds 100% shares → unlock & transfer ERC‑721

---

## 📦 METADATA TEMPLATE & IPFS WORKFLOW

```json
{
  "name": "Downtown Duplex, Accra",
  "description": "Modern 4‑bedroom duplex in central Accra.",
  "image": "ipfs://<CID_image>",
  "external_url": "https://brickchain.app/property/123",
  "attributes": [
    { "trait_type": "Property Type", "value": "Residential" },
    { "trait_type": "Location", "value": "Accra, Ghana" },
    { "trait_type": "Valuation", "value": "100 ETH" },
    { "trait_type": "Total Shares", "value": 1000 },
    { "trait_type": "Price Per Share", "value": "0.1 ETH" },
    { "trait_type": "Fractionalized", "value": "false" }
  ],
  "documents": {
    "legal": "ipfs://<CID_legal.pdf>",
    "certificate": "ipfs://<CID_cert.pdf>"
  }
}
```

1. Upload image & docs → get CIDs.
2. Embed in JSON → upload metadata → get `ipfs://<CID>/metadata.json`.
3. Pass that URI into `mintProperty(...)`.

---

## 💻 TECH STACK & INFRASTRUCTURE

* **Frontend:** Next.js (App‑Router) + TailwindCSS
* **Wallet:** RainbowKit + wagmi/viem
* **Backend:** Supabase Auth (Google/email), Postgres, RLS, Edge Functions
* **Storage:** web3.storage (IPFS)
* **Contracts:** Hardhat, Ethers.js, Sonic Blaze testnet

---

## 📂 UPDATED FILE STRUCTURE

```text
src/
 └─ app/
    ├─ layout.tsx            ← global nav + wallet connect
    ├─ page.tsx              ← landing
    ├─ auth/
    │   ├─ signup/page.tsx
    │   └─ login/page.tsx
    ├─ dashboard/
    │   ├─ owner/page.tsx
    │   └─ investor/page.tsx
    ├─ list-property/page.tsx
    ├─ marketplace/page.tsx
    ├─ property/[id]/page.tsx
    ├─ profile/page.tsx
    ├─ token/page.tsx
    ├─ transactions/page.tsx
    └─ education/page.tsx
```

---

## ⏱️ MVP ROADMAP (Tonight)

| Phase | Task | Time |
|-------|------|------|
| 1     | Boilerplate (Next.js, Supabase, Hardhat) | 1h |
| 2     | Auth & RainbowKit wallet | 1.5h |
| 3     | Deploy contracts (ERC‑721,1155,Manager) | 1.5h |
| 4     | Listing UI + IPFS upload + mint | 1h |
| 5     | Fractionalize UI + contract call | 1h |
| 6     | Invest UI + buyShares() | 1h |
| 7     | Demo polish & seed data | 1h |

---

You now have a **single reference document** capturing all core logic, metadata flows, file structure, and your rapid‑MVP plan. Let me know which part you'd like to kick off coding first!

---
