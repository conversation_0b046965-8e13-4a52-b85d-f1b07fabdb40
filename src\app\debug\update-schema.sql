-- Update the properties table to support new features
-- Run this in your Supabase SQL Editor

-- Add a published field to control visibility
ALTER TABLE properties ADD COLUMN IF NOT EXISTS published BOOLEAN DEFAULT false;

-- Ensure status field supports our needed states
-- Check if status is already an enum type
DO $$
BEGIN
    -- If status is just a text field, convert it to an enum
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'properties' 
        AND column_name = 'status' 
        AND data_type = 'text'
    ) THEN
        -- Check if the enum type already exists
        IF NOT EXISTS (
            SELECT 1 FROM pg_type 
            WHERE typname = 'property_status'
        ) THEN
            -- Create the enum type
            CREATE TYPE property_status AS ENUM (
                'draft', 'available', 'pending', 'funded', 'sold', 'archived'
            );
        END IF;
        
        -- Convert the column type (this will convert 'available', etc. to the enum)
        ALTER TABLE properties 
        ALTER COLUMN status TYPE property_status USING status::property_status;
    END IF;
    
    -- If status column doesn't exist at all, add it
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'properties' 
        AND column_name = 'status'
    ) THEN
        -- Check if the enum type already exists
        IF NOT EXISTS (
            SELECT 1 FROM pg_type 
            WHERE typname = 'property_status'
        ) THEN
            -- Create the enum type
            CREATE TYPE property_status AS ENUM (
                'draft', 'available', 'pending', 'funded', 'sold', 'archived'
            );
        END IF;
        
        -- Add the column with a default
        ALTER TABLE properties ADD COLUMN status property_status DEFAULT 'draft';
    END IF;
END$$;

-- Add a description field if it doesn't exist
ALTER TABLE properties ADD COLUMN IF NOT EXISTS description TEXT DEFAULT '';

-- Ensure the image_url field exists
ALTER TABLE properties ADD COLUMN IF NOT EXISTS image_url TEXT DEFAULT '';

-- Add a created_by field if it doesn't exist
ALTER TABLE properties ADD COLUMN IF NOT EXISTS created_at TIMESTAMP WITH TIME ZONE DEFAULT now();

-- Add an updated_at field to track changes
ALTER TABLE properties ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT now();

-- Create a function to update the updated_at field
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add a trigger to automatically update updated_at
DROP TRIGGER IF EXISTS set_properties_updated_at ON properties;
CREATE TRIGGER set_properties_updated_at
BEFORE UPDATE ON properties
FOR EACH ROW
EXECUTE FUNCTION update_modified_column(); 