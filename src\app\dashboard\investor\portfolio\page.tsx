'use client';
import React, { useState, useEffect } from 'react';
import { createClient } from '@supabase/supabase-js';
import { Building, TrendingUp, DollarSign, Calendar, ArrowRight, BarChart2, Pie<PERSON><PERSON> } from 'lucide-react';
// No longer need to import DashboardWrapper
import { useUser } from '../../../../context/UserContext';
import Link from 'next/link';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';
const supabase = createClient(supabaseUrl, supabaseAnonKey);

type Property = {
  name: string;
  image_url: string;
  location: string;
  return_rate: number;
};

type InvestmentWithProperty = {
  id: string;
  property_id: string;
  amount: number;
  date_invested: string;
  properties: Property;
};

type FormattedInvestment = {
  id: string;
  property_id: string;
  amount: number;
  date_invested: string;
  property_name: string;
  property_image: string;
  property_location: string;
  return_rate: number;
  total_value: number;
  monthly_return: number;
};

export default function InvestorPortfolioPage() {
  const { user } = useUser();
  const [investments, setInvestments] = useState<FormattedInvestment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalPortfolioValue, setTotalPortfolioValue] = useState(0);
  const [totalMonthlyReturn, setTotalMonthlyReturn] = useState(0);
  const [averageReturnRate, setAverageReturnRate] = useState(0);

  useEffect(() => {
    if (!user) return;

    // Set up real-time subscription
    const subscription = supabase
      .channel('investments_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'investments',
          filter: `investor_id=eq.${user.id}`
        },
        () => {
          // Refresh data when changes occur
          fetchPortfolioData();
        }
      )
      .subscribe();

    // Initial data fetch
    fetchPortfolioData();

    // Cleanup subscription
    return () => {
      subscription.unsubscribe();
    };
  }, [user]);

  const fetchPortfolioData = async () => {
    if (!user) return;

    try {
      setLoading(true);
      setError(null);

      // Fetch user's investments with property details
      const { data: userInvestments, error: investmentsError } = await supabase
        .from('investments')
        .select(`
          id,
          property_id,
          amount,
          date_invested,
          properties(name, image_url, location, return_rate)
        `)
        .eq('investor_id', user.id) as { data: InvestmentWithProperty[] | null, error: any };

      if (investmentsError) throw investmentsError;

      // Format the investments data
      const formattedInvestments = userInvestments?.map(inv => ({
        id: inv.id,
        property_id: inv.property_id,
        amount: inv.amount,
        date_invested: inv.date_invested,
        property_name: inv.properties.name,
        property_image: inv.properties.image_url,
        property_location: inv.properties.location,
        return_rate: inv.properties.return_rate,
        total_value: inv.amount,
        monthly_return: (inv.amount * inv.properties.return_rate) / 12 / 100
      })) || [];

      setInvestments(formattedInvestments);

      // Calculate portfolio metrics
      const totalValue = formattedInvestments.reduce((sum, inv) => sum + inv.amount, 0);
      const totalMonthly = formattedInvestments.reduce((sum, inv) => sum + (inv.monthly_return || 0), 0);
      const avgReturn = formattedInvestments.length > 0
        ? formattedInvestments.reduce((sum, inv) => sum + inv.return_rate, 0) / formattedInvestments.length
        : 0;

      setTotalPortfolioValue(totalValue);
      setTotalMonthlyReturn(totalMonthly);
      setAverageReturnRate(avgReturn);
    } catch (error: any) {
      const errorMsg = error?.message || JSON.stringify(error) || 'Failed to fetch portfolio data.';
      console.error('Error fetching portfolio data:', errorMsg);
      setError('Failed to load portfolio data. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  return (
      <div className="space-y-6">
        {error && (
          <div className="bg-red-900/30 border border-red-800 rounded-xl p-4">
            <p className="text-red-300 whitespace-pre-line">{error}</p>
          </div>
        )}

        {/* Portfolio Overview */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
          <div className="bg-indigo-900/30 border border-indigo-800 rounded-xl p-4 md:p-6">
            <div className="flex items-start justify-between">
              <div>
                <p className="text-sm md:text-base text-gray-400">Total Portfolio Value</p>
                <h3 className="text-xl md:text-2xl font-bold text-white mt-1">
                  ${loading ? '...' : totalPortfolioValue.toLocaleString()}
                </h3>
              </div>
              <div className="p-2 md:p-3 rounded-lg bg-blue-600">
                <DollarSign className="text-white w-5 h-5 md:w-6 md:h-6" />
              </div>
            </div>
          </div>

          <div className="bg-indigo-900/30 border border-indigo-800 rounded-xl p-4 md:p-6">
            <div className="flex items-start justify-between">
              <div>
                <p className="text-sm md:text-base text-gray-400">Monthly Returns</p>
                <h3 className="text-xl md:text-2xl font-bold text-white mt-1">
                  ${loading ? '...' : totalMonthlyReturn.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                </h3>
              </div>
              <div className="p-2 md:p-3 rounded-lg bg-green-600">
                <TrendingUp className="text-white w-5 h-5 md:w-6 md:h-6" />
              </div>
            </div>
          </div>

          <div className="bg-indigo-900/30 border border-indigo-800 rounded-xl p-4 md:p-6">
            <div className="flex items-start justify-between">
              <div>
                <p className="text-sm md:text-base text-gray-400">Average Return Rate</p>
                <h3 className="text-xl md:text-2xl font-bold text-white mt-1">
                  {loading ? '...' : `${averageReturnRate.toFixed(1)}%`}
                </h3>
              </div>
              <div className="p-2 md:p-3 rounded-lg bg-purple-600">
                <BarChart2 className="text-white w-5 h-5 md:w-6 md:h-6" />
              </div>
            </div>
          </div>
        </div>

        {/* Portfolio Distribution */}
        <div className="bg-indigo-900/30 border border-indigo-800 rounded-xl overflow-hidden">
          <div className="p-4 md:p-6">
            <h3 className="text-lg md:text-xl font-bold text-white">Portfolio Distribution</h3>
            <p className="text-sm md:text-base text-gray-400 mt-1">Your current investment allocation</p>
          </div>

          {loading ? (
            <div className="p-4 md:p-6 space-y-4">
              {[1, 2, 3].map((_, index) => (
                <div key={index} className="animate-pulse flex items-center space-x-4">
                  <div className="w-16 h-16 bg-indigo-800/40 rounded-lg"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-indigo-800/40 rounded w-1/4"></div>
                    <div className="h-3 bg-indigo-800/40 rounded w-1/3"></div>
                  </div>
                  <div className="h-6 bg-indigo-800/40 rounded w-16"></div>
                </div>
              ))}
            </div>
          ) : investments.length === 0 ? (
            <div className="p-8 text-center">
              <PieChart size={48} className="mx-auto text-indigo-600/40 mb-4" />
              <h3 className="text-lg md:text-xl font-semibold text-white mb-2">No Investments Yet</h3>
              <p className="text-sm md:text-base text-gray-400 mb-4">
                Start building your portfolio by investing in properties
              </p>
              <Link
                href="/dashboard/investor/opportunities"
                className="inline-flex items-center px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors"
              >
                Browse Properties
                <ArrowRight size={18} className="ml-2" />
              </Link>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <div className="min-w-full divide-y divide-indigo-800/30">
                {investments.map((investment) => (
                  <div key={investment.id} className="p-4 md:p-6 hover:bg-indigo-800/10">
                    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                      <div className="flex items-center space-x-4">
                        <div className="w-16 h-16 rounded-lg bg-indigo-800/20 overflow-hidden flex-shrink-0">
                          {investment.property_image ? (
                            <img
                              src={investment.property_image}
                              alt={investment.property_name}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <Building size={24} className="m-auto text-indigo-400" />
                          )}
                        </div>
                        <div>
                          <h4 className="font-medium text-white">{investment.property_name}</h4>
                          <p className="text-sm text-gray-400">{investment.property_location}</p>
                        </div>
                      </div>
                      <div className="flex flex-col sm:items-end gap-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-white">
                            ${investment.amount.toLocaleString()}
                          </span>
                          <span className="text-sm text-green-400">
                            {investment.return_rate ? `${investment.return_rate}%` : 'N/A'}
                          </span>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-gray-400">
                          <Calendar size={14} className="mr-1" />
                          {new Date(investment.date_invested).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Performance Metrics */}
        <div className="bg-indigo-900/30 border border-indigo-800 rounded-xl overflow-hidden">
          <div className="p-4 md:p-6">
            <h3 className="text-lg md:text-xl font-bold text-white">Performance Metrics</h3>
            <p className="text-sm md:text-base text-gray-400 mt-1">Key performance indicators for your portfolio</p>
          </div>

          <div className="p-4 md:p-6 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-indigo-800/20 rounded-lg p-4">
              <p className="text-sm text-gray-400">Total Investments</p>
              <p className="text-xl font-semibold text-white mt-1">
                {investments.length}
              </p>
            </div>

            <div className="bg-indigo-800/20 rounded-lg p-4">
              <p className="text-sm text-gray-400">Average Investment</p>
              <p className="text-xl font-semibold text-white mt-1">
                ${investments.length > 0
                  ? (totalPortfolioValue / investments.length).toLocaleString(undefined, { maximumFractionDigits: 0 })
                  : '0'}
              </p>
            </div>

            <div className="bg-indigo-800/20 rounded-lg p-4">
              <p className="text-sm text-gray-400">Projected Annual Return</p>
              <p className="text-xl font-semibold text-white mt-1">
                ${(totalMonthlyReturn * 12).toLocaleString(undefined, { maximumFractionDigits: 2 })}
              </p>
            </div>

            <div className="bg-indigo-800/20 rounded-lg p-4">
              <p className="text-sm text-gray-400">Portfolio Growth</p>
              <p className="text-xl font-semibold text-green-400 mt-1">
                +{averageReturnRate.toFixed(1)}%
              </p>
            </div>
          </div>
        </div>
      </div>
  );
}