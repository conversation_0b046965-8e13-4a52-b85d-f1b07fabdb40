'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@supabase/supabase-js';
import { Search, Filter, Mail, Phone, Building, DollarSign, UserPlus } from 'lucide-react';
import LoadingSpinner from '@/components/LoadingSpinner';
import InvestorInviteModal from '@/components/InvestorInviteModal';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

type Investor = {
  id: string;
  email: string;
  full_name: string;
  phone: string;
  company: string;
  total_invested: number;
  active_investments: number;
  last_investment_date: string;
};

export default function OwnerInvestorsPage() {
  const [investors, setInvestors] = useState<Investor[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'total_invested' | 'active_investments' | 'last_investment_date'>('total_invested');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);

  useEffect(() => {
    fetchInvestors();
  }, []);

  const fetchInvestors = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      // Get all investors who have invested in the owner's properties
      const { data, error } = await supabase
        .from('investments')
        .select(`
          investor_id,
          amount,
          date_invested,
          profiles (
            id,
            email,
            full_name,
            phone,
            company
          )
        `)
        .eq('property_id', supabase
          .from('properties')
          .select('id')
          .eq('owner_id', user.id)
        );

      if (error) throw error;

      // Process and aggregate investor data
      const investorMap = new Map<string, Investor>();
      
      data?.forEach(investment => {
        const profile = investment.profiles;
        if (!profile) return;

        const existing = investorMap.get(profile.id);
        if (existing) {
          existing.total_invested += investment.amount;
          existing.active_investments += 1;
          if (new Date(investment.date_invested) > new Date(existing.last_investment_date)) {
            existing.last_investment_date = investment.date_invested;
          }
        } else {
          investorMap.set(profile.id, {
            id: profile.id,
            email: profile.email,
            full_name: profile.full_name || '',
            phone: profile.phone || '',
            company: profile.company || '',
            total_invested: investment.amount,
            active_investments: 1,
            last_investment_date: investment.date_invested
          });
        }
      });

      setInvestors(Array.from(investorMap.values()));
    } catch (error) {
      console.error('Error fetching investors:', error);
      setError('Failed to load investors');
    } finally {
      setLoading(false);
    }
  };

  const filteredInvestors = investors
    .filter(investor => 
      investor.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      investor.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      investor.company.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .sort((a, b) => {
      const aValue = a[sortBy];
      const bValue = b[sortBy];
      const modifier = sortOrder === 'asc' ? 1 : -1;
      
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return aValue.localeCompare(bValue) * modifier;
      }
      return ((aValue as number) - (bValue as number)) * modifier;
    });

  const handleSort = (field: typeof sortBy) => {
    if (field === sortBy) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('desc');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-red-500">{error}</div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <h1 className="text-2xl font-bold">Investors</h1>
        <button
          onClick={() => setIsInviteModalOpen(true)}
          className="flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          <UserPlus size={20} />
          Invite Investor
        </button>
      </div>

      <div className="mb-6">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <input
            type="text"
            placeholder="Search investors..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Investor
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort('total_invested')}
              >
                <div className="flex items-center gap-1">
                  <DollarSign size={16} />
                  Total Invested
                  {sortBy === 'total_invested' && (
                    <span>{sortOrder === 'asc' ? '↑' : '↓'}</span>
                  )}
                </div>
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort('active_investments')}
              >
                <div className="flex items-center gap-1">
                  <Building size={16} />
                  Active Investments
                  {sortBy === 'active_investments' && (
                    <span>{sortOrder === 'asc' ? '↑' : '↓'}</span>
                  )}
                </div>
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort('last_investment_date')}
              >
                <div className="flex items-center gap-1">
                  Last Investment
                  {sortBy === 'last_investment_date' && (
                    <span>{sortOrder === 'asc' ? '↑' : '↓'}</span>
                  )}
                </div>
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Contact
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredInvestors.map((investor) => (
              <tr key={investor.id}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10 bg-gray-200 rounded-full flex items-center justify-center">
                      <span className="text-gray-500 font-medium">
                        {investor.full_name.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900">
                        {investor.full_name}
                      </div>
                      <div className="text-sm text-gray-500">
                        {investor.company}
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium">
                    ${investor.total_invested.toLocaleString()}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">
                    {investor.active_investments}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-500">
                    {new Date(investor.last_investment_date).toLocaleDateString()}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center gap-4">
                    <a
                      href={`mailto:${investor.email}`}
                      className="text-blue-600 hover:text-blue-900"
                    >
                      <Mail size={16} />
                    </a>
                    {investor.phone && (
                      <a
                        href={`tel:${investor.phone}`}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        <Phone size={16} />
                      </a>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {filteredInvestors.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500">No investors found</p>
        </div>
      )}

      {isInviteModalOpen && (
        <InvestorInviteModal
          onClose={() => setIsInviteModalOpen(false)}
          onSuccess={() => {
            fetchInvestors();
          }}
        />
      )}
    </div>
  );
} 