'use client';

import React, { useState, useEffect } from 'react';
import { useSupabaseClient } from '@supabase/auth-helpers-react';
import { useUser } from '@/context/UserContext';
import { useWallet } from '@/context/WalletContext';
import { useTheme } from '@/context/ThemeContext';
import {
  User,
  Mail,
  Phone,
  Building,
  Wallet,
  Bell,
  Lock,
  Globe,
  Moon,
  Sun,
  Shield,
  Link as LinkIcon,
  Save,
  AlertTriangle,
  Check,
  Loader2
} from 'lucide-react';
import ConnectWalletButton from '@/components/ConnectWalletButton';
import ThemeToggle from '@/components/ThemeToggle';

export default function SettingsPage() {
  const { user, refreshUser, session } = useUser();
  const { isConnected } = useWallet();
  const { theme, setTheme, resolvedTheme } = useTheme();
  const supabase = useSupabaseClient();

  // Form states
  const [formData, setFormData] = useState({
    full_name: '',
    email: '',
    phone: '',
    company_name: '',
    current_password: '',
    new_password: '',
    confirm_password: '',
    language: 'en',
    theme: 'system',
    email_notifications: true,
    app_notifications: true,
    marketing_emails: false,
    two_factor_auth: false
  });

  // UI states
  const [activeTab, setActiveTab] = useState('profile');
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState('');
  const [error, setError] = useState('');
  // We'll track password changes through the success message instead
  // const [passwordChanged, setPasswordChanged] = useState(false);

  // Load user data
  useEffect(() => {
    if (user) {
      setFormData(prevData => ({
        ...prevData,
        full_name: user.full_name || '',
        phone: user.phone || '',
        company_name: user.company_name || ''
      }));
    }

    if (session?.user) {
      setFormData(prevData => ({
        ...prevData,
        email: session.user.email || ''
      }));
    }

    // Load preferences from localStorage
    // Note: We don't need to load theme from localStorage since ThemeContext already does that
    // We just need to sync our form state with the ThemeContext
    const savedLanguage = localStorage.getItem('language') || 'en';
    const emailNotifications = localStorage.getItem('emailNotifications') !== 'false';
    const appNotifications = localStorage.getItem('appNotifications') !== 'false';
    const marketingEmails = localStorage.getItem('marketingEmails') === 'true';
    const twoFactorAuth = localStorage.getItem('twoFactorAuth') === 'true';

    setFormData(prevData => ({
      ...prevData,
      theme: theme, // Use the theme from ThemeContext
      language: savedLanguage,
      email_notifications: emailNotifications,
      app_notifications: appNotifications,
      marketing_emails: marketingEmails,
      two_factor_auth: twoFactorAuth
    }));
  }, [user, session, theme]); // Add theme as a dependency

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const updateProfile = async () => {
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      // Update profile in Supabase
      const { error: updateError } = await supabase
        .from('profiles')
        .update({
          full_name: formData.full_name,
          phone: formData.phone,
          company_name: formData.company_name
        })
        .eq('id', user?.id);

      if (updateError) throw updateError;

      // Save preferences to localStorage
      localStorage.setItem('theme', formData.theme);
      localStorage.setItem('language', formData.language);
      localStorage.setItem('emailNotifications', formData.email_notifications.toString());
      localStorage.setItem('appNotifications', formData.app_notifications.toString());
      localStorage.setItem('marketingEmails', formData.marketing_emails.toString());
      localStorage.setItem('twoFactorAuth', formData.two_factor_auth.toString());

      // Apply theme immediately (this is redundant since we're already setting it on click,
      // but it ensures consistency if the form is submitted without clicking the theme options)
      setTheme(formData.theme as 'light' | 'dark' | 'system');

      // Refresh user data in context
      await refreshUser();

      setSuccess('Settings updated successfully');
    } catch (err: Error | unknown) {
      console.error('Error updating profile:', err);
      setError(err instanceof Error ? err.message : 'Failed to update profile');
    } finally {
      setLoading(false);
    }
  };

  const updatePassword = async () => {
    setLoading(true);
    setError('');
    setSuccess('');

    // Validate passwords
    if (formData.new_password !== formData.confirm_password) {
      setError('New passwords do not match');
      setLoading(false);
      return;
    }

    if (formData.new_password.length < 8) {
      setError('Password must be at least 8 characters long');
      setLoading(false);
      return;
    }

    try {
      const { error } = await supabase.auth.updateUser({
        password: formData.new_password
      });

      if (error) throw error;

      setSuccess('Password updated successfully');
      // Password change is tracked through the success message
      setFormData(prev => ({
        ...prev,
        current_password: '',
        new_password: '',
        confirm_password: ''
      }));
    } catch (err: Error | unknown) {
      console.error('Error updating password:', err);
      setError(err instanceof Error ? err.message : 'Failed to update password');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (activeTab === 'security' && formData.new_password) {
      await updatePassword();
    } else {
      await updateProfile();
    }
  };

  // Handle case when supabase client is not available
  if (!supabase) {
    return (
      <div className="p-6 bg-red-900/40 border border-red-800 rounded-lg">
        <h2 className="text-xl font-bold text-white mb-2">Database Connection Error</h2>
        <p className="text-red-300">Unable to connect to the database. Please try again later.</p>
      </div>
    );
  }

  // Tabs configuration
  const tabs = [
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'wallet', label: 'Wallet', icon: Wallet },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'security', label: 'Security', icon: Shield },
    { id: 'connections', label: 'Connections', icon: LinkIcon },
    { id: 'appearance', label: 'Appearance', icon: Moon }
  ];

  return (
    <div className="max-w-6xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-white">Settings</h1>
        <p className="text-gray-400">Manage your account settings and preferences</p>
      </div>

      {/* Settings tabs */}
      <div className="flex flex-col md:flex-row gap-6">
        {/* Sidebar */}
        <div className="w-full md:w-64 bg-indigo-900/30 rounded-xl p-4">
          <nav className="space-y-1">
            {tabs.map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`w-full flex items-center px-4 py-3 rounded-lg transition-colors ${
                  activeTab === tab.id
                    ? 'bg-indigo-700/30 text-cyan-400 font-medium'
                    : 'text-gray-300 hover:bg-indigo-800/20 hover:text-white'
                }`}
              >
                <tab.icon size={20} className={`mr-3 ${activeTab === tab.id ? 'text-cyan-400' : 'text-gray-400'}`} />
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Main content */}
        <div className="flex-1 bg-indigo-900/30 rounded-xl p-6">
          {/* Success/Error messages */}
          {success && (
            <div className="mb-6 p-4 bg-green-900/40 border border-green-800 rounded-lg flex items-center">
              <Check size={20} className="text-green-400 mr-2" />
              <p className="text-green-300">{success}</p>
            </div>
          )}

          {error && (
            <div className="mb-6 p-4 bg-red-900/40 border border-red-800 rounded-lg flex items-center">
              <AlertTriangle size={20} className="text-red-400 mr-2" />
              <p className="text-red-300">{error}</p>
            </div>
          )}

          <form onSubmit={handleSubmit}>
            {/* Profile Settings */}
            {activeTab === 'profile' && (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-white mb-4">Profile Information</h2>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">Full Name</label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <User size={18} className="text-gray-500" />
                      </div>
                      <input
                        type="text"
                        name="full_name"
                        value={formData.full_name}
                        onChange={handleInputChange}
                        className="pl-10 pr-4 py-2 bg-indigo-900/50 border border-indigo-700 rounded-lg text-white w-full focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                        placeholder="Your full name"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">Email Address</label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Mail size={18} className="text-gray-500" />
                      </div>
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        disabled
                        className="pl-10 pr-4 py-2 bg-indigo-900/50 border border-indigo-700 rounded-lg text-white w-full opacity-70 cursor-not-allowed"
                        placeholder="Your email address"
                      />
                    </div>
                    <p className="text-xs text-gray-500 mt-1">Email cannot be changed. Contact support for assistance.</p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">Phone Number</label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Phone size={18} className="text-gray-500" />
                      </div>
                      <input
                        type="tel"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        className="pl-10 pr-4 py-2 bg-indigo-900/50 border border-indigo-700 rounded-lg text-white w-full focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                        placeholder="Your phone number"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">Company Name (Optional)</label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Building size={18} className="text-gray-500" />
                      </div>
                      <input
                        type="text"
                        name="company_name"
                        value={formData.company_name || ''}
                        onChange={handleInputChange}
                        className="pl-10 pr-4 py-2 bg-indigo-900/50 border border-indigo-700 rounded-lg text-white w-full focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                        placeholder="Your company name (if applicable)"
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Wallet Settings */}
            {activeTab === 'wallet' && (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-white mb-4">Wallet Settings</h2>

                <div className="bg-indigo-800/30 rounded-lg p-6 mb-6">
                  <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                    <div>
                      <h3 className="text-lg font-medium text-white mb-1">Connect Your Wallet</h3>
                      <p className="text-gray-400 text-sm">Connect your wallet to invest in properties or list your own properties</p>
                    </div>
                    <ConnectWalletButton variant="default" showBalance={true} />
                  </div>
                </div>

                {isConnected && (
                  <>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between py-3 border-b border-indigo-800">
                        <div>
                          <h4 className="text-white font-medium">Default Transaction Gas</h4>
                          <p className="text-sm text-gray-400">Set your preferred gas price for transactions</p>
                        </div>
                        <select
                          name="gas_preference"
                          className="bg-indigo-900/50 border border-indigo-700 rounded-lg text-white px-3 py-2 focus:ring-2 focus:ring-indigo-500"
                          defaultValue="standard"
                        >
                          <option value="slow">Slow (Cheaper)</option>
                          <option value="standard">Standard</option>
                          <option value="fast">Fast (More Expensive)</option>
                        </select>
                      </div>

                      <div className="flex items-center justify-between py-3 border-b border-indigo-800">
                        <div>
                          <h4 className="text-white font-medium">Auto-connect Wallet</h4>
                          <p className="text-sm text-gray-400">Automatically connect your wallet when you visit the site</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" defaultChecked />
                          <div className="w-11 h-6 bg-indigo-700/50 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                        </label>
                      </div>

                      <div className="flex items-center justify-between py-3 border-b border-indigo-800">
                        <div>
                          <h4 className="text-white font-medium">Transaction Notifications</h4>
                          <p className="text-sm text-gray-400">Receive notifications for wallet transactions</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" defaultChecked />
                          <div className="w-11 h-6 bg-indigo-700/50 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                        </label>
                      </div>
                    </div>
                  </>
                )}
              </div>
            )}

            {/* Notification Settings */}
            {activeTab === 'notifications' && (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-white mb-4">Notification Preferences</h2>

                <div className="space-y-4">
                  <div className="flex items-center justify-between py-3 border-b border-indigo-800">
                    <div>
                      <h4 className="text-white font-medium">Email Notifications</h4>
                      <p className="text-sm text-gray-400">Receive important updates via email</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        name="email_notifications"
                        checked={formData.email_notifications}
                        onChange={handleInputChange}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-indigo-700/50 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                    </label>
                  </div>

                  <div className="flex items-center justify-between py-3 border-b border-indigo-800">
                    <div>
                      <h4 className="text-white font-medium">In-App Notifications</h4>
                      <p className="text-sm text-gray-400">Receive notifications within the application</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        name="app_notifications"
                        checked={formData.app_notifications}
                        onChange={handleInputChange}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-indigo-700/50 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                    </label>
                  </div>

                  <div className="flex items-center justify-between py-3 border-b border-indigo-800">
                    <div>
                      <h4 className="text-white font-medium">Marketing Emails</h4>
                      <p className="text-sm text-gray-400">Receive promotional content and offers</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        name="marketing_emails"
                        checked={formData.marketing_emails}
                        onChange={handleInputChange}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-indigo-700/50 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                    </label>
                  </div>

                  <div className="mt-6">
                    <h4 className="text-white font-medium mb-2">Notification Types</h4>
                    <div className="space-y-3 bg-indigo-800/20 p-4 rounded-lg">
                      {[
                        { id: 'investment_updates', label: 'Investment Updates' },
                        { id: 'property_listings', label: 'New Property Listings' },
                        { id: 'account_security', label: 'Account Security Alerts' },
                        { id: 'platform_updates', label: 'Platform Updates' }
                      ].map(item => (
                        <div key={item.id} className="flex items-center">
                          <input
                            id={item.id}
                            type="checkbox"
                            defaultChecked
                            className="w-4 h-4 text-indigo-600 bg-indigo-900 border-indigo-700 rounded focus:ring-indigo-500"
                          />
                          <label htmlFor={item.id} className="ml-2 text-sm text-gray-300">
                            {item.label}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Security Settings */}
            {activeTab === 'security' && (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-white mb-4">Security Settings</h2>

                <div className="space-y-6">
                  <div className="bg-indigo-800/30 rounded-lg p-6">
                    <h3 className="text-lg font-medium text-white mb-4">Change Password</h3>

                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-1">Current Password</label>
                        <input
                          type="password"
                          name="current_password"
                          value={formData.current_password}
                          onChange={handleInputChange}
                          className="px-4 py-2 bg-indigo-900/50 border border-indigo-700 rounded-lg text-white w-full focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                          placeholder="Enter your current password"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-1">New Password</label>
                        <input
                          type="password"
                          name="new_password"
                          value={formData.new_password}
                          onChange={handleInputChange}
                          className="px-4 py-2 bg-indigo-900/50 border border-indigo-700 rounded-lg text-white w-full focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                          placeholder="Enter new password"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-1">Confirm New Password</label>
                        <input
                          type="password"
                          name="confirm_password"
                          value={formData.confirm_password}
                          onChange={handleInputChange}
                          className="px-4 py-2 bg-indigo-900/50 border border-indigo-700 rounded-lg text-white w-full focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                          placeholder="Confirm new password"
                        />
                      </div>

                      <button
                        type="button"
                        onClick={updatePassword}
                        disabled={loading || !formData.current_password || !formData.new_password || !formData.confirm_password}
                        className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 disabled:bg-indigo-800/50 disabled:text-gray-400 rounded-lg text-white font-medium transition-colors flex items-center justify-center"
                      >
                        {loading ? <Loader2 size={18} className="animate-spin mr-2" /> : <Lock size={18} className="mr-2" />}
                        Update Password
                      </button>
                    </div>
                  </div>

                  <div className="flex items-center justify-between py-3 border-b border-indigo-800">
                    <div>
                      <h4 className="text-white font-medium">Two-Factor Authentication</h4>
                      <p className="text-sm text-gray-400">Add an extra layer of security to your account</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        name="two_factor_auth"
                        checked={formData.two_factor_auth}
                        onChange={handleInputChange}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-indigo-700/50 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                    </label>
                  </div>

                  <div className="flex items-center justify-between py-3 border-b border-indigo-800">
                    <div>
                      <h4 className="text-white font-medium">Session Management</h4>
                      <p className="text-sm text-gray-400">Manage your active sessions</p>
                    </div>
                    <button className="px-3 py-1.5 bg-indigo-700/50 hover:bg-indigo-700 rounded-lg text-white text-sm transition-colors">
                      View Sessions
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Connected Accounts */}
            {activeTab === 'connections' && (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-white mb-4">Connected Accounts</h2>

                <div className="space-y-4">
                  {[
                    { name: 'Google', connected: true, icon: '🔵' },
                    { name: 'Twitter', connected: false, icon: '🐦' },
                    { name: 'Facebook', connected: false, icon: '📘' },
                    { name: 'Discord', connected: true, icon: '💬' }
                  ].map((account) => (
                    <div key={account.name} className="flex items-center justify-between py-3 border-b border-indigo-800">
                      <div className="flex items-center">
                        <span className="text-2xl mr-3">{account.icon}</span>
                        <div>
                          <h4 className="text-white font-medium">{account.name}</h4>
                          <p className="text-sm text-gray-400">
                            {account.connected ? 'Connected' : 'Not connected'}
                          </p>
                        </div>
                      </div>
                      <button
                        className={`px-3 py-1.5 rounded-lg text-sm transition-colors ${
                          account.connected
                            ? 'bg-red-700/30 hover:bg-red-700/50 text-red-300'
                            : 'bg-indigo-700/50 hover:bg-indigo-700 text-white'
                        }`}
                      >
                        {account.connected ? 'Disconnect' : 'Connect'}
                      </button>
                    </div>
                  ))}
                </div>

                <div className="mt-6">
                  <h3 className="text-lg font-medium text-white mb-4">API Access</h3>
                  <div className="bg-indigo-800/30 rounded-lg p-6">
                    <p className="text-gray-300 mb-4">Generate API keys to access BrickChain data programmatically</p>
                    <button className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 rounded-lg text-white font-medium transition-colors">
                      Manage API Keys
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Appearance Settings */}
            {activeTab === 'appearance' && (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-white mb-4">Appearance Settings</h2>

                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-3">Theme</label>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      {[
                        { id: 'system', label: 'System Default', icon: Moon },
                        { id: 'light', label: 'Light Mode', icon: Sun },
                        { id: 'dark', label: 'Dark Mode', icon: Moon }
                      ].map((themeOption) => (
                        <div
                          key={themeOption.id}
                          onClick={() => {
                            // Update both the form data and the actual theme
                            setFormData(prev => ({ ...prev, theme: themeOption.id }));
                            setTheme(themeOption.id as 'light' | 'dark' | 'system');
                          }}
                          className={`p-4 rounded-lg cursor-pointer transition-colors ${
                            theme === themeOption.id
                              ? 'bg-indigo-700/50 border-2 border-indigo-500'
                              : 'bg-indigo-900/30 border border-indigo-800 hover:bg-indigo-800/30'
                          }`}
                        >
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-white font-medium">{themeOption.label}</span>
                            <themeOption.icon size={20} className="text-gray-400" />
                          </div>
                          {themeOption.id === 'system' && (
                            <p className="text-xs text-gray-400">
                              Currently using {resolvedTheme} mode based on your system
                            </p>
                          )}
                        </div>
                      ))}
                    </div>

                    <div className="mt-6 p-4 bg-indigo-800/30 rounded-lg">
                      <h3 className="text-lg font-medium text-white mb-3">Quick Theme Toggle</h3>
                      <p className="text-gray-400 mb-4">Use the buttons below to quickly switch between themes:</p>
                      <div className="flex justify-center">
                        <ThemeToggle variant="button" className="scale-110" />
                      </div>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Language</label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Globe size={18} className="text-gray-500" />
                      </div>
                      <select
                        name="language"
                        value={formData.language}
                        onChange={handleInputChange}
                        className="pl-10 pr-4 py-2 bg-indigo-900/50 border border-indigo-700 rounded-lg text-white w-full focus:ring-2 focus:ring-indigo-500"
                      >
                        <option value="en">English</option>
                        <option value="es">Spanish</option>
                        <option value="fr">French</option>
                        <option value="de">German</option>
                        <option value="zh">Chinese</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Save Button (for all tabs except security which has its own button) */}
            {activeTab !== 'security' && (
              <div className="mt-8 flex justify-end">
                <button
                  type="submit"
                  disabled={loading}
                  className="px-6 py-2.5 bg-indigo-600 hover:bg-indigo-700 disabled:bg-indigo-800/50 disabled:text-gray-400 rounded-lg text-white font-medium transition-colors flex items-center"
                >
                  {loading ? <Loader2 size={18} className="animate-spin mr-2" /> : <Save size={18} className="mr-2" />}
                  Save Changes
                </button>
              </div>
            )}
          </form>
        </div>
      </div>
    </div>
  );
}



