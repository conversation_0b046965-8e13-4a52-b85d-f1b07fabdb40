-- Get all users with their activity summary
SELECT 
  u.id,
  u.wallet_address,
  u.email,
  u.role,
  u.kyc_status,
  u.created_at,
  CASE 
    WHEN u.role = 'property_owner' THEN (
      SELECT COUNT(*) FROM properties WHERE owner_id = u.id
    )
    ELSE 0
  END as properties_owned,
  CASE 
    WHEN u.role = 'investor' THEN (
      SELECT COUNT(*) FROM investments WHERE investor_id = u.id AND status = 'completed'
    )
    ELSE 0
  END as investments_made,
  CASE 
    WHEN u.role = 'investor' THEN (
      SELECT COALESCE(SUM(amount), 0) FROM investments WHERE investor_id = u.id AND status = 'completed'
    )
    ELSE 0
  END as total_invested
FROM users u
ORDER BY u.created_at DESC;
