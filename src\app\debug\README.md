# BrickChain Database and Wallet Connection Fixes

This document provides instructions for fixing two critical issues in the BrickChain application:

1. Database type mismatches and foreign key constraints
2. Multiple wallet connection instances

## Database Schema Fixes

### Problem

The database has type mismatches between tables:
- `properties.id` is of type UUID
- `investments.property_id` is of type BIGINT
- `investments.investor_id` is of type BIGINT (potentially)

This prevents proper foreign key constraints and causes relationship queries to fail.

### Solution

We've created a comprehensive SQL script (`fix-database-schema.sql`) that:

1. Creates backup tables for safety
2. Checks current data types
3. Converts `investments.property_id` to UUID type if needed
4. Converts `investments.investor_id` to UUID type if needed
5. Creates mapping tables to preserve relationships
6. Adds proper foreign key constraints
7. Verifies the changes

### How to Apply the Fix

#### Option 1: Comprehensive Fix (Recommended if you need all relationships fixed)

1. Open the Supabase SQL Editor
2. Copy the contents of `fix-database-schema.sql`
3. Run the script
4. Refresh the schema cache in the Supabase Dashboard

#### Option 2: Simple Fix (If you only need to fix the property_id relationship)

If you're encountering issues with the comprehensive fix or only need to fix the property_id relationship:

1. Open the Supabase SQL Editor
2. Copy the contents of `simple-fix.sql`
3. Run the script
4. Refresh the schema cache in the Supabase Dashboard

The simple fix focuses only on converting the `investments.property_id` column to UUID type and establishing the foreign key relationship with `properties.id`.

```sql
-- Example of the key part of the fix:
-- Change the column type to UUID
ALTER TABLE investments ALTER COLUMN property_id TYPE UUID USING NULL;

-- Create a mapping table to help with the conversion
CREATE TEMP TABLE property_id_mapping (
    bigint_id BIGINT PRIMARY KEY,
    uuid_id UUID
);

-- Insert mappings from properties table
INSERT INTO property_id_mapping (bigint_id, uuid_id)
SELECT
    ROW_NUMBER() OVER (ORDER BY id),
    id
FROM properties;

-- Update the property_id in investments using the mapping
UPDATE investments i
SET property_id = m.uuid_id
FROM property_id_mapping m
WHERE i.property_id_old = m.bigint_id;
```

### After Running the Script

After running the script, you should be able to:

1. Use relationship queries in Supabase
2. See proper foreign key constraints in the schema
3. Maintain data integrity between tables

## Wallet Connection Fix

### Problem

Multiple instances of the wallet connection button appear in the application:
- In the Navbar component
- In the DashboardWrapper component (sidebar)
- In the token page

This causes confusion and potential issues with wallet state management.

### Solution

We've implemented a centralized wallet state management system:

1. Created a `WalletContext` to manage wallet state across the application
2. Updated the `Web3Provider` to include the `WalletProvider`
3. Modified the `ConnectWalletButton` component to use the centralized context
4. Removed duplicate wallet buttons from the token page

### Key Components

#### WalletContext

The `WalletContext` provides a centralized state for wallet connection:

```tsx
// src/context/WalletContext.tsx
export const useWallet = () => useContext(WalletContext);

export function WalletProvider({ children }: { children: ReactNode }) {
  const { address, isConnected, chainId } = useAccount();
  const { chain } = useNetwork();
  const { data: balanceData } = useBalance({
    address,
  });

  // Local state for wallet modal
  const [isWalletModalOpen, setIsWalletModalOpen] = useState(false);

  // Format the address for display
  const displayAddress = address ? formatAddress(address) : '';

  // Get network name
  const networkName = chain ? chain.name : getNetworkName(chainId);

  // Context value
  const value = {
    isConnected,
    address,
    displayAddress,
    chainId,
    networkName,
    balance: balanceData?.formatted,
    isWalletModalOpen,
    openWalletModal: () => setIsWalletModalOpen(true),
    closeWalletModal: () => setIsWalletModalOpen(false),
  };

  return (
    <WalletContext.Provider value={value}>
      {children}
    </WalletContext.Provider>
  );
}
```

#### Web3Provider

The `Web3Provider` now includes the `WalletProvider`:

```tsx
export default function Web3Provider({ children }: Web3ProviderProps) {
  return (
    <WagmiProvider config={config}>
      <QueryClientProvider client={queryClient}>
        <RainbowKitProvider theme={darkTheme({...})}>
          <WalletProvider>
            {children}
          </WalletProvider>
        </RainbowKitProvider>
      </QueryClientProvider>
    </WagmiProvider>
  );
}
```

#### ConnectWalletButton

The `ConnectWalletButton` now uses the centralized context:

```tsx
export default function ConnectWalletButton({
  variant = 'default',
  showBalance = true
}: ConnectWalletButtonProps) {
  // Use the centralized wallet context instead of direct wagmi hooks
  const { isConnected, address, chainId, displayAddress, balance } = useWallet();

  // Rest of the component...
}
```

### How to Use the Wallet Context

In any component that needs wallet information:

```tsx
import { useWallet } from '@/context/WalletContext';

function MyComponent() {
  const { isConnected, address, chainId, displayAddress } = useWallet();

  // Use wallet information...
}
```

## Testing the Fixes

1. Run the database fix script in Supabase SQL Editor
2. Start the application with `npm run dev`
3. Test wallet connection in the navbar
4. Verify that wallet state is consistent across the application
5. Test database queries that use relationships
