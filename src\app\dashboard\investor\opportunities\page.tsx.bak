'use client';
import React, { useState, useEffect, useCallback } from 'react';
import { createClient } from '@supabase/supabase-js';
import {
  Building, Search, Filter, ArrowRight, DollarSign,
  MapPin, Calendar, Wallet, Loader2
} from 'lucide-react';
import { useUser } from '../../../../context/UserContext';
import { useWallet } from '../../../../context/WalletContext';
import ErrorBoundary from '../../../../components/ErrorBoundary';
import Image from 'next/image';

// Define logger for consistent logging
const logger = {
  info: (message: string, data?: unknown) => {
    console.log(`[Opportunities] INFO: ${message}`, data || '');
  },
  warn: (message: string, data?: unknown) => {
    console.warn(`[Opportunities] WARNING: ${message}`, data || '');
  },
  error: (message: string, data?: unknown) => {
    console.error(`[Opportunities] ERROR: ${message}`, data || '');
  }
};

// Define TypeScript interfaces
interface CoreProperty {
  id: string;
  name: string;
  location: string;
  price: number;
  image_url: string;
  return_rate: number;
  description: string;
  created_at: string;
  owner_id: string;
}

interface OwnerInfo {
  id: string;
  full_name: string;
}

interface InvestmentInfo {
  property_id: string;
  amount: number;
}

interface EnhancedProperty extends CoreProperty {
  owner_name: string;
  total_investments: number;
  funded_percentage?: number;
}

// Loading stages for better UX
type LoadingStage = 'idle' | 'core' | 'owners' | 'investments' | 'complete' | 'error';

// Database query options
interface QueryOptions {
  timeout?: number;
  retries?: number;
  currentRetry?: number;
}

// Export the component wrapped in ErrorBoundary
export default function InvestorOpportunitiesPage() {
  return (
    <ErrorBoundary>
      <InvestorOpportunitiesContent />
    </ErrorBoundary>
  );
}

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// This type is no longer needed as we're using the interfaces defined above

// Wrap the component with ErrorBoundary
function InvestorOpportunitiesContent() {
  const { user } = useUser();
  const { isConnected, openWalletModal } = useWallet();

  // Core state
  const [properties, setProperties] = useState<EnhancedProperty[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingStage, setLoadingStage] = useState<LoadingStage>('idle');
  const [loadingProgress, setLoadingProgress] = useState(0);

  // UI state
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'newest' | 'price' | 'return'>('return');
  const [selectedProperty, setSelectedProperty] = useState<EnhancedProperty | null>(null);
  const [investmentAmount, setInvestmentAmount] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState('');
  const [showWalletPrompt, setShowWalletPrompt] = useState(false);

  // Define utility functions for data fetching
  const executeQuery = useCallback(async <T,>(
    queryFn: () => any,
    options: QueryOptions = {}
  ): Promise<T | null> => {
    const { timeout = 10000, retries = 2, currentRetry = 0 } = options;

    // Create abort controller with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      logger.info(`Executing query (attempt ${currentRetry + 1}/${retries + 1}) with ${timeout}ms timeout`);

      // Execute the query with abort signal
      const query = queryFn();
      // Add abort signal to the query
      if (query && typeof query.abortSignal === 'function') {
        query.abortSignal(controller.signal);
      }

      const result = await query;

      clearTimeout(timeoutId);

      if (result.error) {
        throw result.error;
      }

      return result.data;
    } catch (error) {
      clearTimeout(timeoutId);

      const errorMessage = error instanceof Error ? error.message : String(error);
      const isAbortError = errorMessage.includes('abort') || errorMessage.includes('aborted');

      // Retry logic for timeouts and certain errors
      if ((isAbortError || errorMessage.includes('timeout')) && currentRetry < retries) {
        logger.warn(`Query timed out or aborted, retrying (${currentRetry + 1}/${retries})`, error);

        // Increase timeout for next retry
        const nextTimeout = timeout * 1.5;
        return executeQuery(queryFn, {
          timeout: nextTimeout,
          retries,
          currentRetry: currentRetry + 1
        });
      }

      // If we've exhausted retries or it's another type of error, rethrow
      throw error;
    }
  }, []);

  // Function to safely format properties with fallbacks
  const formatProperty = useCallback((property: CoreProperty, ownerName = 'Unknown Owner', totalInvestments = 0): EnhancedProperty => {
    return {
      id: property.id,
      name: property.name || 'Unnamed Property',
      location: property.location || 'Unknown Location',
      price: typeof property.price === 'number' ? property.price : 0,
      image_url: property.image_url || '',
      return_rate: typeof property.return_rate === 'number' ? property.return_rate : 0,
      total_investments: totalInvestments,
      owner_name: ownerName,
      owner_id: property.owner_id || '',
      created_at: property.created_at || new Date().toISOString(),
      description: property.description || '',
      funded_percentage: property.price > 0 ? (totalInvestments / property.price) * 100 : 0
    };
  }, []);

  useEffect(() => {
    // Main function to fetch data in stages
    const fetchPropertyData = async () => {
      if (!user) return;

      try {
        setLoading(true);
        setError(null);
        setLoadingStage('idle');
        setLoadingProgress(0);

        // STAGE 1: Fetch core property data
        setLoadingStage('core');
        logger.info('Stage 1: Fetching core property data');

        const coreProperties = await executeQuery<CoreProperty[]>(() => {
          return supabase
            .from('properties')
            .select('id, name, location, price, image_url, return_rate, description, created_at, owner_id')
            .eq('status', 'available')
            .limit(20);
        });

        if (!coreProperties || coreProperties.length === 0) {
          logger.info('No properties found');
          setProperties([]);
          setLoading(false);
          return;
        }

        // Create initial properties with minimal data
        const initialProperties = coreProperties.map(prop => formatProperty(prop));
        setProperties(initialProperties);
        setLoadingProgress(30);

        // STAGE 2: Fetch owner information
        setLoadingStage('owners');
        logger.info('Stage 2: Fetching owner information');

        // Extract owner IDs
        const propertyOwnerIds = coreProperties
          .filter(p => p.owner_id)
          .map(p => p.owner_id);

        let propertyOwnerMap: Record<string, string> = {};

        if (propertyOwnerIds.length > 0) {
          try {
            const owners = await executeQuery<Array<{ id: string, full_name: string }>>(() => {
              return supabase
                .from('profiles')
                .select('id, full_name')
                .in('id', propertyOwnerIds)
                .limit(50);
            }, { timeout: 5000 });

            if (owners) {
              // Create a map of owner IDs to names
              propertyOwnerMap = owners.reduce((map, owner) => {
                map[owner.id] = owner.full_name;
                return map;
              }, {} as Record<string, string>);

              // Update properties with owner names
              const propertiesWithOwners = initialProperties.map(prop => ({
                ...prop,
                owner_name: prop.owner_id && propertyOwnerMap[prop.owner_id]
                  ? propertyOwnerMap[prop.owner_id]
                  : prop.owner_name
              }));

              setProperties(propertiesWithOwners);
            }
          } catch (error) {
            logger.warn('Failed to fetch owner information, continuing with basic data', error);
          }
        }

        setLoadingProgress(60);

        // STAGE 3: Fetch investment information
        setLoadingStage('investments');
        logger.info('Stage 3: Fetching investment information');

        try {
          // Get property IDs for investment query
          const propertyIds = coreProperties.map(p => p.id);

          const investments = await executeQuery<Array<{ property_id: string, amount: number }>>(() => {
            return supabase
              .from('investments')
              .select('property_id, amount')
              .in('property_id', propertyIds)
              .limit(100);
          }, { timeout: 5000 });

          if (investments && investments.length > 0) {
            // Calculate total investments per property
            const investmentTotals: Record<string, number> = {};

            investments.forEach(inv => {
              if (inv.property_id) {
                const amount = typeof inv.amount === 'number' ? inv.amount :
                              typeof inv.amount === 'string' ? parseFloat(inv.amount) : 0;

                investmentTotals[inv.property_id] = (investmentTotals[inv.property_id] || 0) + amount;
              }
            });

            // Update properties with investment data
            const completeProperties = initialProperties.map(prop => ({
              ...prop,
              total_investments: investmentTotals[prop.id] || 0,
              owner_name: prop.owner_id && propertyOwnerMap[prop.owner_id]
                ? propertyOwnerMap[prop.owner_id]
                : prop.owner_name,
              funded_percentage: prop.price > 0
                ? ((investmentTotals[prop.id] || 0) / prop.price) * 100
                : 0
            }));

            setProperties(completeProperties);
          }
        } catch (investmentError) {
          logger.warn('Failed to fetch investment data, continuing with basic property data', investmentError);
        }

        setLoadingStage('complete');
        setLoadingProgress(100);

      } catch (error) {
        // Handle errors gracefully
        logger.error('Error fetching property data:', error);

        // Try to show a helpful error message
        const errorMessage = error instanceof Error ? error.message : String(error);

        if (errorMessage.includes('timeout') || errorMessage.includes('abort')) {
          setError('The request took too long to process. Please try again or check your connection.');
        } else if (errorMessage.includes('permission') || errorMessage.includes('auth')) {
          setError('You don\'t have permission to access these properties. Please log in again.');
        } else {
          setError('Failed to load properties. Please try again later.');
        }

        // Set empty properties array
        setProperties([]);
      } finally {
        // Always mark loading as complete
        setLoading(false);
        setLoadingStage('complete');
        setLoadingProgress(100);
      }
    };

    // Execute the fetch function when component mounts
    fetchPropertyData();
  }, [user]); // eslint-disable-line react-hooks/exhaustive-deps

  // Filter and sort properties
  const filteredProperties = properties
    .filter(property => {
      const matchesSearch = searchTerm === '' ||
        property.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        property.location.toLowerCase().includes(searchTerm.toLowerCase());
      return matchesSearch;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'return':
          return (b.return_rate || 0) - (a.return_rate || 0);
        case 'price':
          return a.price - b.price;
        case 'newest':
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        default:
          return 0;
      }
    });

  const handleInvest = async (propertyId: string) => {
    // Check if wallet is connected first
    if (!isConnected) {
      setShowWalletPrompt(true);
      return;
    }

    if (!investmentAmount || isNaN(Number(investmentAmount)) || Number(investmentAmount) <= 0) {
      setError('Please enter a valid investment amount');
      return;
    }

    try {
      const { error } = await supabase
        .from('investments')
        .insert({
          property_id: propertyId,
          investor_id: user?.id,
          amount: Number(investmentAmount),
          date_invested: new Date().toISOString()
        });

      if (error) throw error;

      setSuccess('Investment successful!');
      setSelectedProperty(null);
      setInvestmentAmount('');

      // Refresh properties list
      const { data: updatedProperty } = await supabase
        .from('properties')
        .select('*')
        .eq('id', propertyId)
        .single();

      if (updatedProperty) {
        setProperties(properties.map(p =>
          p.id === propertyId ? { ...p, total_investments: (p.total_investments || 0) + Number(investmentAmount) } : p
        ));
      }
    } catch (err) {
      console.error('Error making investment:', err);
      setError('Failed to process investment');
    }
  };

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold text-white mb-1">Investment Opportunities</h1>
      <p className="text-gray-400 mb-6">Browse and invest in available properties</p>

      {/* Error message */}
      {error && (
        <div className="bg-red-900/30 border border-red-800 text-red-200 px-4 py-3 rounded-lg mb-6">
          {error}
        </div>
      )}

      {/* Loading state */}
      {loading && (
        <div className="flex flex-col items-center justify-center py-12">
          <div className="w-16 h-16 border-4 border-indigo-600 border-t-transparent rounded-full animate-spin mb-4"></div>
          <p className="text-indigo-300">Loading investment opportunities...</p>
          <p className="text-gray-500 text-sm mt-2">Stage: {loadingStage}</p>
          <div className="w-64 h-2 bg-gray-800 rounded-full mt-4">
            <div
              className="h-full bg-indigo-600 rounded-full transition-all duration-300"
              style={{ width: `${loadingProgress}%` }}
            ></div>
          </div>
        </div>
      )}

      {/* Properties grid */}
      {!loading && properties.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {properties.map((property, index) => (
            <div
              key={property.id}
              className="bg-indigo-900/30 border border-indigo-800 rounded-xl overflow-hidden hover:border-indigo-700 transition-colors"
            >
              {/* Property image */}
              <div className="relative h-48 w-full">
                {property.image_url ? (
                  <img
                    src={property.image_url}
                    alt={property.name}
                    className="w-full h-full object-cover"
                    loading={index < 3 ? "eager" : "lazy"}
                  />
                ) : (
                  <div className="w-full h-full bg-indigo-800/20 flex items-center justify-center">
                    <Building size={48} className="text-indigo-600/40" />
                  </div>
                )}
                <div className="absolute top-4 right-4">
                  <span className="px-2.5 py-1 rounded-full text-xs font-medium bg-green-900/40 text-green-300">
                    {property.return_rate ? `${property.return_rate}% ROI` : 'No ROI set'}
                  </span>
                </div>
              </div>

              <div className="p-4 space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-white mb-1">{property.name}</h3>
                  <p className="text-sm text-gray-400 flex items-center">
                    <MapPin size={14} className="mr-1" />
                    {property.location}
                  </p>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-400">Total Price</p>
                    <p className="text-lg font-medium text-white">
                      ${property.price.toLocaleString()}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-400">Invested</p>
                    <p className="text-lg font-medium text-white">
                      ${property.total_investments?.toLocaleString()}
                    </p>
                  </div>
                </div>

                <div className="flex items-center justify-between text-sm text-gray-400">
                  <span className="flex items-center">
                    <Calendar size={14} className="mr-1" />
                    {new Date(property.created_at).toLocaleDateString()}
                  </span>
                  <span>by {property.owner_name}</span>
                </div>

                <button
                  onClick={() => {
                    if (!isConnected) {
                      setShowWalletPrompt(true);
                    } else {
                      setSelectedProperty(property);
                    }
                  }}
                  className="w-full px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg text-sm md:text-base flex items-center justify-center"
                >
                  <DollarSign size={16} className="mr-2" />
                  Invest Now
                </button>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Investment Modal */}
      {selectedProperty && (
        <div className="fixed inset-0 z-50 overflow-y-auto bg-black bg-opacity-75 flex items-center justify-center p-4">
          <div className="bg-indigo-900 rounded-xl p-4 md:p-6 max-w-lg w-full">
            <div className="flex justify-between items-start mb-4">
              <h3 className="text-lg md:text-xl font-bold text-white">Invest in {selectedProperty.name}</h3>
              <button
                onClick={() => {
                  setSelectedProperty(null);
                  setInvestmentAmount('');
                  setError('');
                }}
                className="text-gray-400 hover:text-white"
              >
                <ArrowRight size={24} className="rotate-45" />
              </button>
            </div>

            <div className="space-y-6">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm md:text-base text-gray-300">Investment Amount</label>
                  <span className="text-sm text-gray-400">
                    Available: ${(selectedProperty.price - (selectedProperty.total_investments || 0)).toLocaleString()}
                  </span>
                </div>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <DollarSign size={18} className="text-gray-500" />
                  </div>
                  <input
                    type="number"
                    value={investmentAmount}
                    onChange={(e) => setInvestmentAmount(e.target.value)}
                    placeholder="Enter amount"
                    className="pl-10 pr-4 py-2 bg-indigo-800/50 border border-indigo-700 rounded-lg text-white w-full text-sm md:text-base"
                    min="0"
                    max={selectedProperty.price - (selectedProperty.total_investments || 0)}
                  />
                </div>
              </div>

              <div className="bg-indigo-800/30 rounded-lg p-4 space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm md:text-base text-gray-400">Property Value:</span>
                  <span className="text-sm md:text-base text-white">${selectedProperty.price.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm md:text-base text-gray-400">Current Investments:</span>
                  <span className="text-sm md:text-base text-white">${selectedProperty.total_investments?.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm md:text-base text-gray-400">Expected Return:</span>
                  <span className="text-sm md:text-base text-green-400">
                    {selectedProperty.return_rate ? `${selectedProperty.return_rate}%` : 'Not set'}
                  </span>
                </div>
              </div>

              <div className="flex justify-end space-x-4">
                <button
                  onClick={() => {
                    setSelectedProperty(null);
                    setInvestmentAmount('');
                    setError('');
                  }}
                  className="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg text-white text-sm md:text-base"
                >
                  Cancel
                </button>
                <button
                  onClick={() => handleInvest(selectedProperty.id)}
                  className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 rounded-lg text-white text-sm md:text-base flex items-center"
                >
                  <DollarSign size={16} className="mr-2" />
                  Confirm Investment
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Wallet Connection Prompt Modal */}
      {showWalletPrompt && (
        <div className="fixed inset-0 z-50 overflow-y-auto bg-black bg-opacity-75 flex items-center justify-center p-4">
          <div className="bg-indigo-900 rounded-xl p-4 md:p-6 max-w-md w-full">
            <div className="flex justify-between items-start mb-4">
              <h3 className="text-lg md:text-xl font-bold text-white">Connect Your Wallet</h3>
              <button
                onClick={() => setShowWalletPrompt(false)}
                className="text-gray-400 hover:text-white"
              >
                <ArrowRight size={24} className="rotate-45" />
              </button>
            </div>

            <div className="space-y-6">
              <div className="bg-indigo-800/30 rounded-lg p-4">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="p-2 bg-indigo-700/50 rounded-full">
                    <Wallet size={24} className="text-indigo-300" />
                  </div>
                  <p className="text-white font-medium">Wallet Required</p>
                </div>
                <p className="text-gray-300 mb-2">
                  You need to connect your wallet before you can invest in properties.
                </p>
                <p className="text-gray-300">
                  Connecting your wallet allows you to securely invest in properties and manage your investments.
                </p>
              </div>

              <div className="flex justify-end space-x-4">
                <button
                  onClick={() => setShowWalletPrompt(false)}
                  className="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg text-white text-sm md:text-base"
                >
                  Cancel
                </button>
                <button
                  onClick={() => {
                    openWalletModal();
                    setShowWalletPrompt(false);
                  }}
                  className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 rounded-lg text-white text-sm md:text-base flex items-center"
                >
                  <Wallet size={16} className="mr-2" />
                  Connect Wallet
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}