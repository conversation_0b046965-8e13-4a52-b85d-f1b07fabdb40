// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol";
import "@openzeppelin/contracts/access/Ownable.sol";

/**
 * @title BrickToken
 * @dev ERC20 token for the BrickChain platform
 * Used for fee discounts and rewards
 */
contract BrickToken is ERC20, ERC20Burnable, Ownable {
    // Maximum supply of tokens
    uint256 private constant _maxSupply = 100_000_000 * 10**18; // 100 million tokens
    
    // Investment Manager contract address
    address private _investmentManager;
    
    // Marketplace contract address
    address private _marketplace;
    
    // Events
    event InvestmentManagerSet(address indexed investmentManager);
    event MarketplaceSet(address indexed marketplace);
    
    constructor() ERC20("BrickChain Token", "BCT") Ownable(msg.sender) {
        // Mint 10% of max supply to owner for initial distribution
        _mint(msg.sender, _maxSupply / 10);
    }
    
    /**
     * @dev Sets the investment manager contract address
     * @param investmentManager Address of the InvestmentManager contract
     */
    function setInvestmentManager(address investmentManager) external onlyOwner {
        _investmentManager = investmentManager;
        emit InvestmentManagerSet(investmentManager);
    }
    
    /**
     * @dev Sets the marketplace contract address
     * @param marketplace Address of the AssetMarketplace contract
     */
    function setMarketplace(address marketplace) external onlyOwner {
        _marketplace = marketplace;
        emit MarketplaceSet(marketplace);
    }
    
    /**
     * @dev Mints new tokens
     * @param to Address to mint tokens to
     * @param amount Amount of tokens to mint
     */
    function mint(address to, uint256 amount) external {
        require(
            msg.sender == owner() || 
            msg.sender == _investmentManager || 
            msg.sender == _marketplace,
            "Not authorized"
        );
        require(totalSupply() + amount <= _maxSupply, "Exceeds max supply");
        _mint(to, amount);
    }
    
    /**
     * @dev Returns the maximum supply of tokens
     */
    function maxSupply() external pure returns (uint256) {
        return _maxSupply;
    }
    
    /**
     * @dev Returns the remaining supply of tokens that can be minted
     */
    function remainingSupply() external view returns (uint256) {
        return _maxSupply - totalSupply();
    }
    
    /**
     * @dev Calculates the fee discount based on token balance
     * @param account Address to calculate discount for
     * @param fee Original fee amount
     */
    function calculateDiscount(address account, uint256 fee) external view returns (uint256) {
        uint256 balance = balanceOf(account);
        
        // No tokens, no discount
        if (balance == 0) {
            return fee;
        }
        
        // Discount tiers based on token balance
        // 1,000 BCT: 5% discount
        // 10,000 BCT: 10% discount
        // 100,000 BCT: 20% discount
        // 1,000,000 BCT: 30% discount
        
        uint256 discountPercentage;
        if (balance >= 1_000_000 * 10**18) {
            discountPercentage = 30;
        } else if (balance >= 100_000 * 10**18) {
            discountPercentage = 20;
        } else if (balance >= 10_000 * 10**18) {
            discountPercentage = 10;
        } else if (balance >= 1_000 * 10**18) {
            discountPercentage = 5;
        } else {
            discountPercentage = 0;
        }
        
        // Apply discount
        return fee * (100 - discountPercentage) / 100;
    }
}
