'use client';

import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';
const supabase = createClient(supabaseUrl, supabaseAnonKey);

export default function Profile() {
  const router = useRouter();
  const [authChecked, setAuthChecked] = useState(false);

  useEffect(() => {
    const checkAuth = async () => {
      const { data } = await supabase.auth.getUser();
      if (!data?.user) {
        router.replace('/');
      } else {
        setAuthChecked(true);
      }
    };
    checkAuth();
  }, [router]);

  if (!authChecked) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-r from-indigo-900 to-purple-900 text-white">
        <div className="text-2xl font-bold animate-pulse">Checking authentication...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-r from-indigo-900 to-purple-900 text-white">
      <div className="bg-indigo-800 bg-opacity-50 p-8 rounded-lg shadow-lg">
        <h1 className="text-3xl font-bold mb-4">Profile</h1>
        <p>Your profile details will appear here.</p>
      </div>
    </div>
  );
}