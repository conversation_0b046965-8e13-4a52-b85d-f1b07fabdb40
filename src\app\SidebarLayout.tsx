"use client";
import React, { useState, useEffect } from 'react';
import { Menu, X, LogOut, User as UserIcon } from 'lucide-react';
import { usePathname, useRouter } from 'next/navigation';
import { useUser } from '../context/UserContext';
import Link from 'next/link';
import RoleNavigation from '../components/RoleNavigation';

function Sidebar({ open, onClose, onLogout }: { open: boolean; onClose: () => void; onLogout: () => void }) {
  const { user, loading } = useUser();

  return (
    <div className={`fixed inset-0 z-40 transition-transform duration-300 mt-16 ${open ? 'translate-x-0' : '-translate-x-full'} md:relative md:translate-x-0 md:w-64 bg-indigo-900 bg-opacity-90 text-white flex flex-col h-[calc(100%-4rem)]`}>
      <div className="flex items-center justify-between p-4 md:hidden">
        <Link href="/" className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-gradient-to-r from-cyan-400 to-purple-600 rounded-lg flex items-center justify-center">
            <span className="text-white text-sm font-black">BC</span>
          </div>
          <span className="text-xl font-bold">BrickChain</span>
        </Link>
        <button onClick={onClose} className="text-white"><X size={28} /></button>
      </div>

      {/* Desktop logo */}
      <div className="hidden md:flex items-center space-x-3 px-6 py-6">
        <div className="w-10 h-10 bg-gradient-to-r from-cyan-400 to-purple-600 rounded-lg flex items-center justify-center">
          <span className="text-white text-lg font-black">BC</span>
        </div>
        <span className="text-xl font-bold">BrickChain</span>
      </div>

      {/* User profile summary if logged in */}
      {user && (
        <div className="px-6 py-4 border-b border-indigo-700">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-400 to-indigo-500 flex items-center justify-center">
              <UserIcon size={20} className="text-white" />
            </div>
            <div className="flex flex-col">
              <span className="font-medium text-white">{user.full_name || 'User'}</span>
              <span className="text-xs text-gray-300 capitalize">{user.role} Account</span>
            </div>
          </div>
        </div>
      )}

      {/* Role-based navigation */}
      <div className="flex-1 overflow-y-auto py-4">
        <RoleNavigation onItemClick={onClose} />
      </div>

      {/* Logout or login section */}
      <div className="mt-auto p-6 border-t border-indigo-800">
        {user ? (
          <button
            onClick={onLogout}
            className="w-full px-4 py-2.5 flex items-center justify-center space-x-2 bg-red-500/20 hover:bg-red-500/30 border border-red-500/30 rounded-lg text-red-200 font-medium transition-colors"
          >
            <LogOut size={18} />
            <span>Logout</span>
          </button>
        ) : loading ? (
          <div className="text-sm text-gray-300">Loading user data...</div>
        ) : (
          <div className="flex flex-col gap-2">
            <Link href="/" className="px-4 py-2.5 bg-indigo-600 hover:bg-indigo-700 rounded-lg text-white font-medium text-center transition-colors">
              Connect Wallet
            </Link>
          </div>
        )}
      </div>
    </div>
  );
}

export default function SidebarLayout({ children }: { children: React.ReactNode }) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { user, refreshUser, signOut } = useUser();
  const router = useRouter();
  const pathname = usePathname();

  // Close sidebar when route changes on mobile
  useEffect(() => {
    setSidebarOpen(false);
  }, [pathname]);

  const handleLogout = async () => {
    try {
      // Use the centralized signOut method from UserContext
      await signOut();
      router.replace('/');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  return (
    <div className="flex min-h-[calc(100vh-4rem)] mt-16">
      {/* Mobile sidebar - hidden by default, shown when sidebarOpen is true */}
      <div className={`fixed inset-0 z-40 md:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>
        <Sidebar open={sidebarOpen} onClose={() => setSidebarOpen(false)} onLogout={handleLogout} />
      </div>

      {/* Desktop sidebar - always visible on larger screens */}
      <div className="hidden md:block md:w-64 flex-shrink-0">
        <Sidebar open={true} onClose={() => {}} onLogout={handleLogout} />
      </div>

      {/* Overlay for mobile - only shown when sidebar is open */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-30 bg-black bg-opacity-50 md:hidden mt-16"
          onClick={() => setSidebarOpen(false)}
        ></div>
      )}

      {/* Main content */}
      <div className="flex-1 flex flex-col">
        {/* Mobile sidebar toggle button */}
        <div className="block md:hidden fixed top-[4.5rem] left-4 z-20">
          <button
            onClick={() => setSidebarOpen(true)}
            className="p-2 rounded-md bg-indigo-800 text-white"
            aria-label="Toggle sidebar"
          >
            <Menu size={24} />
          </button>
        </div>

        <main className="container mx-auto px-4 py-8 flex-1">
          {children}
        </main>

        {/* Footer moved to separate component or global layout */}
      </div>
    </div>
  );
}