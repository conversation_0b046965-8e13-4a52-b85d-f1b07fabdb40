// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC1155/ERC1155.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/token/ERC1155/extensions/ERC1155Supply.sol";
import "@openzeppelin/contracts/utils/Strings.sol";

/**
 * @title FractionalOwnership
 * @dev ERC1155 token for representing fractional property shares
 * Each token ID represents a different property
 * The amount represents the number of shares owned
 */
contract FractionalOwnership is ERC1155, ERC1155Supply, Ownable {
    using Strings for uint256;

    // Investment Manager contract address
    address private _investmentManager;

    // Mapping from token ID to property metadata URI
    mapping(uint256 => string) private _tokenURIs;

    // Mapping from token ID to property name
    mapping(uint256 => string) private _propertyNames;

    // Mapping from token ID to total shares
    mapping(uint256 => uint256) private _totalShares;

    // Events
    event SharesCreated(uint256 indexed propertyId, string name, uint256 totalShares);
    event SharesMinted(uint256 indexed propertyId, address indexed to, uint256 amount);
    event SharesBurned(uint256 indexed propertyId, address indexed from, uint256 amount);

    constructor() ERC1155("") Ownable(msg.sender) {}

    /**
     * @dev Sets the investment manager contract address
     * @param investmentManager Address of the InvestmentManager contract
     */
    function setInvestmentManager(address investmentManager) external onlyOwner {
        _investmentManager = investmentManager;
    }

    /**
     * @dev Creates shares for a property
     * @param propertyId ID of the property
     * @param name Name of the property
     * @param totalShares Total number of shares for the property
     * @param uri URI for the property metadata
     */
    function createShares(
        uint256 propertyId,
        string memory name,
        uint256 totalShares,
        string memory uri
    ) external {
        require(msg.sender == owner() || msg.sender == _investmentManager, "Not authorized");
        require(totalShares >= 100 && totalShares <= 100000, "Shares must be between 100 and 100,000");
        require(bytes(_propertyNames[propertyId]).length == 0, "Shares already exist for this property");

        _propertyNames[propertyId] = name;
        _totalShares[propertyId] = totalShares;
        _tokenURIs[propertyId] = uri;

        emit SharesCreated(propertyId, name, totalShares);
    }

    /**
     * @dev Mints shares to an address
     * @param to Address to mint shares to
     * @param propertyId ID of the property
     * @param amount Amount of shares to mint
     */
    function mintShares(
        address to,
        uint256 propertyId,
        uint256 amount
    ) external {
        require(msg.sender == owner() || msg.sender == _investmentManager, "Not authorized");
        require(bytes(_propertyNames[propertyId]).length > 0, "Shares do not exist for this property");
        require(totalSupply(propertyId) + amount <= _totalShares[propertyId], "Exceeds total shares");

        _mint(to, propertyId, amount, "");

        emit SharesMinted(propertyId, to, amount);
    }

    /**
     * @dev Burns shares from an address
     * @param from Address to burn shares from
     * @param propertyId ID of the property
     * @param amount Amount of shares to burn
     */
    function burnShares(
        address from,
        uint256 propertyId,
        uint256 amount
    ) external {
        require(msg.sender == owner() || msg.sender == _investmentManager, "Not authorized");
        require(bytes(_propertyNames[propertyId]).length > 0, "Shares do not exist for this property");

        _burn(from, propertyId, amount);

        emit SharesBurned(propertyId, from, amount);
    }

    /**
     * @dev Checks if an address owns all shares of a property
     * @param account Address to check
     * @param propertyId ID of the property
     */
    function ownsAllShares(address account, uint256 propertyId) external view returns (bool) {
        require(bytes(_propertyNames[propertyId]).length > 0, "Shares do not exist for this property");
        return balanceOf(account, propertyId) == _totalShares[propertyId];
    }

    /**
     * @dev Returns the URI for a token ID
     * @param propertyId ID of the property
     */
    function uri(uint256 propertyId) public view override returns (string memory) {
        require(bytes(_tokenURIs[propertyId]).length > 0, "URI not set for this property");
        return _tokenURIs[propertyId];
    }

    /**
     * @dev Returns the name of a property
     * @param propertyId ID of the property
     */
    function getPropertyName(uint256 propertyId) external view returns (string memory) {
        require(bytes(_propertyNames[propertyId]).length > 0, "Shares do not exist for this property");
        return _propertyNames[propertyId];
    }

    /**
     * @dev Returns the total shares of a property
     * @param propertyId ID of the property
     */
    function getTotalShares(uint256 propertyId) external view returns (uint256) {
        require(bytes(_propertyNames[propertyId]).length > 0, "Shares do not exist for this property");
        return _totalShares[propertyId];
    }

    /**
     * @dev Returns the available shares of a property
     * @param propertyId ID of the property
     */
    function getAvailableShares(uint256 propertyId) external view returns (uint256) {
        require(bytes(_propertyNames[propertyId]).length > 0, "Shares do not exist for this property");
        return _totalShares[propertyId] - totalSupply(propertyId);
    }

    /**
     * @dev Returns the percentage of shares owned by an address
     * @param account Address to check
     * @param propertyId ID of the property
     */
    function getSharePercentage(address account, uint256 propertyId) external view returns (uint256) {
        require(bytes(_propertyNames[propertyId]).length > 0, "Shares do not exist for this property");
        return (balanceOf(account, propertyId) * 10000) / _totalShares[propertyId];
    }

    // Override required function
    function _update(address from, address to, uint256[] memory ids, uint256[] memory values)
        internal
        override(ERC1155, ERC1155Supply)
    {
        super._update(from, to, ids, values);
    }
}
