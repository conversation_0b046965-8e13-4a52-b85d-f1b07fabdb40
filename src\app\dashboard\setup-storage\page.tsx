'use client';
import React, { useState } from 'react';
import { useSupabaseClient } from '@supabase/auth-helpers-react';
import { useUser } from '@/context/UserContext';
import { AlertCircle, CheckCircle, Database, HardDrive } from 'lucide-react';
import LoadingSpinner from '@/components/LoadingSpinner';

export default function SetupStoragePage() {
  const { user } = useUser();
  const supabase = useSupabaseClient();
  const [loading, setLoading] = useState<boolean>(false);
  const [dbSetupComplete, setDbSetupComplete] = useState<boolean>(false);
  const [storageSetupComplete, setStorageSetupComplete] = useState<boolean>(false);
  const [logs, setLogs] = useState<string[]>([]);
  const [error, setError] = useState<string>('');

  const addLog = (message: string) => {
    setLogs(prev => [...prev, message]);
  };

  const setupDatabase = async () => {
    try {
      setLoading(true);
      setError('');
      addLog('Starting database setup...');

      // Check if tables already exist using a safer approach
      addLog('Checking if tables already exist...');

      // Use execute_sql RPC to check if tables exist
      const { data: tablesExist, error: checkTablesError } = await supabase.rpc('execute_sql', {
        sql: `
          SELECT table_name
          FROM information_schema.tables
          WHERE table_schema = 'public'
          AND table_name IN ('property_images', 'kyc_documents');
        `
      });

      if (checkTablesError) {
        throw new Error(`Error checking tables: ${checkTablesError.message}`);
      }

      // Extract table names from the result
      const existingTables = tablesExist ? tablesExist.map((row: { table_name: string }) => row.table_name) : [];
      addLog(`Found existing tables: ${existingTables.join(', ') || 'none'}`);

      const propertyImagesExists = existingTables.includes('property_images');
      const kycDocumentsExists = existingTables.includes('kyc_documents');

      if (propertyImagesExists && kycDocumentsExists) {
        addLog('All required tables already exist. Skipping database setup.');
        setDbSetupComplete(true);
        return;
      }

      // Create property_images table
      if (!propertyImagesExists) {
        addLog('Creating property_images table...');

        const { error: createPropertyImagesError } = await supabase.rpc('execute_sql', {
          sql: `
            CREATE TABLE IF NOT EXISTS property_images (
              id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
              property_id UUID REFERENCES properties(id) ON DELETE CASCADE,
              image_url TEXT NOT NULL,
              image_cid TEXT,
              thumbnail_url TEXT,
              image_type VARCHAR(50) NOT NULL,
              file_name TEXT NOT NULL,
              file_size INTEGER NOT NULL,
              file_type TEXT NOT NULL,
              width INTEGER,
              height INTEGER,
              optimized BOOLEAN DEFAULT FALSE,
              sort_order INTEGER DEFAULT 0,
              created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
              updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
            );

            -- Add trigger for updated_at
            CREATE OR REPLACE FUNCTION update_modified_column()
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.updated_at = now();
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;

            DROP TRIGGER IF EXISTS set_property_images_updated_at ON property_images;
            CREATE TRIGGER set_property_images_updated_at
            BEFORE UPDATE ON property_images
            FOR EACH ROW
            EXECUTE FUNCTION update_modified_column();

            -- Add RLS policies for property_images
            ALTER TABLE property_images ENABLE ROW LEVEL SECURITY;

            -- Anyone can view property images
            CREATE POLICY "Anyone can view property images"
              ON property_images FOR SELECT
              USING (true);

            -- Only property owners can insert images
            CREATE POLICY "Only property owners can insert images"
              ON property_images FOR INSERT
              WITH CHECK (
                EXISTS (
                  SELECT 1 FROM properties p
                  WHERE p.id = property_id AND p.owner_id = auth.uid()
                )
              );

            -- Only property owners can update images
            CREATE POLICY "Only property owners can update images"
              ON property_images FOR UPDATE
              USING (
                EXISTS (
                  SELECT 1 FROM properties p
                  WHERE p.id = property_id AND p.owner_id = auth.uid()
                )
              );

            -- Only property owners can delete images
            CREATE POLICY "Only property owners can delete images"
              ON property_images FOR DELETE
              USING (
                EXISTS (
                  SELECT 1 FROM properties p
                  WHERE p.id = property_id AND p.owner_id = auth.uid()
                )
              );
          `
        });

        if (createPropertyImagesError) {
          throw new Error(`Error creating property_images table: ${createPropertyImagesError.message}`);
        }

        addLog('Successfully created property_images table');
      }

      // Create kyc_documents table
      if (!kycDocumentsExists) {
        addLog('Creating kyc_documents table...');

        const { error: createKycDocumentsError } = await supabase.rpc('execute_sql', {
          sql: `
            -- Create enum for document types if it doesn't exist
            DO $$
            BEGIN
              IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'kyc_document_type') THEN
                CREATE TYPE kyc_document_type AS ENUM (
                  'passport', 'national_id', 'drivers_license',
                  'proof_of_address', 'bank_statement', 'other'
                );
              END IF;
            END$$;

            -- Create enum for verification status if it doesn't exist
            DO $$
            BEGIN
              IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'verification_status') THEN
                CREATE TYPE verification_status AS ENUM (
                  'pending', 'verified', 'rejected', 'expired'
                );
              END IF;
            END$$;

            -- Create KYC documents table
            CREATE TABLE IF NOT EXISTS kyc_documents (
              id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
              user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
              document_type kyc_document_type NOT NULL,
              document_category VARCHAR(50) NOT NULL,
              file_path TEXT NOT NULL,
              file_name TEXT NOT NULL,
              file_size INTEGER NOT NULL,
              file_type TEXT NOT NULL,
              verification_status verification_status DEFAULT 'pending',
              verification_notes TEXT,
              verified_by UUID REFERENCES auth.users(id),
              verified_at TIMESTAMP WITH TIME ZONE,
              expires_at TIMESTAMP WITH TIME ZONE,
              created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
              updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
            );

            -- Create audit logs table for tracking document verification actions
            CREATE TABLE IF NOT EXISTS audit_logs (
              id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
              action TEXT NOT NULL,
              user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
              target_id UUID,
              target_type TEXT NOT NULL,
              details JSONB,
              ip_address TEXT,
              user_agent TEXT,
              created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
            );

            -- Add trigger for kyc_documents updated_at
            DROP TRIGGER IF EXISTS set_kyc_documents_updated_at ON kyc_documents;
            CREATE TRIGGER set_kyc_documents_updated_at
            BEFORE UPDATE ON kyc_documents
            FOR EACH ROW
            EXECUTE FUNCTION update_modified_column();

            -- Add RLS policies for kyc_documents
            ALTER TABLE kyc_documents ENABLE ROW LEVEL SECURITY;

            -- Users can only view their own documents
            CREATE POLICY "Users can view their own documents"
              ON kyc_documents FOR SELECT
              USING (auth.uid() = user_id);

            -- Users can only insert their own documents
            CREATE POLICY "Users can insert their own documents"
              ON kyc_documents FOR INSERT
              WITH CHECK (auth.uid() = user_id);

            -- Only admins can update verification status
            CREATE POLICY "Only admins can update verification status"
              ON kyc_documents FOR UPDATE
              USING (
                EXISTS (
                  SELECT 1 FROM profiles
                  WHERE id = auth.uid() AND role = 'admin'
                )
              );

            -- Set up RLS for audit_logs
            ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

            -- Only admins can view audit logs
            CREATE POLICY "Only admins can view audit logs"
              ON audit_logs FOR SELECT
              USING (
                EXISTS (
                  SELECT 1 FROM profiles
                  WHERE id = auth.uid() AND role = 'admin'
                )
              );

            -- Only admins can insert audit logs
            CREATE POLICY "Only admins can insert audit logs"
              ON audit_logs FOR INSERT
              WITH CHECK (
                EXISTS (
                  SELECT 1 FROM profiles
                  WHERE id = auth.uid() AND role = 'admin'
                )
              );
          `
        });

        if (createKycDocumentsError) {
          throw new Error(`Error creating kyc_documents table: ${createKycDocumentsError.message}`);
        }

        addLog('Successfully created kyc_documents table');
      }

      addLog('Database setup completed successfully!');
      setDbSetupComplete(true);
    } catch (err) {
      console.error('Error setting up database:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      addLog(`Error: ${err instanceof Error ? err.message : 'An unknown error occurred'}`);
    } finally {
      setLoading(false);
    }
  };

  const setupStorageBuckets = async () => {
    try {
      setLoading(true);
      setError('');
      addLog('Starting storage bucket setup...');

      // Get existing buckets
      const { data: existingBuckets, error: bucketsError } = await supabase.storage.listBuckets();

      if (bucketsError) {
        throw bucketsError;
      }

      const existingBucketNames = existingBuckets.map(bucket => bucket.name);
      addLog(`Existing buckets: ${existingBucketNames.join(', ') || 'none'}`);

      // Define buckets to create
      const bucketsToCreate = [
        {
          name: 'properties',
          public: true,
          fileSizeLimit: 10485760, // 10MB
          allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'application/pdf']
        },
        {
          name: 'kyc_documents',
          public: false,
          fileSizeLimit: 10485760, // 10MB
          allowedMimeTypes: [
            'image/jpeg',
            'image/png',
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/encrypted'
          ]
        }
      ];

      // First, try to create buckets using SQL to bypass RLS policies
      // This approach uses the execute_sql RPC function which should have admin privileges
      addLog('Attempting to create buckets using SQL...');

      try {
        const createBucketsSQL = `
          -- Create buckets if they don't exist
          DO $$
          DECLARE
            bucket_exists BOOLEAN;
          BEGIN
            -- Create properties bucket
            SELECT EXISTS(SELECT 1 FROM storage.buckets WHERE name = 'properties') INTO bucket_exists;
            IF NOT bucket_exists THEN
              INSERT INTO storage.buckets (id, name, owner, created_at, updated_at, public)
              VALUES ('properties', 'properties', auth.uid(), now(), now(), true);
              RAISE NOTICE 'Created properties bucket';
            ELSE
              RAISE NOTICE 'Properties bucket already exists';
            END IF;

            -- Create kyc_documents bucket
            SELECT EXISTS(SELECT 1 FROM storage.buckets WHERE name = 'kyc_documents') INTO bucket_exists;
            IF NOT bucket_exists THEN
              INSERT INTO storage.buckets (id, name, owner, created_at, updated_at, public)
              VALUES ('kyc_documents', 'kyc_documents', auth.uid(), now(), now(), false);
              RAISE NOTICE 'Created kyc_documents bucket';
            ELSE
              RAISE NOTICE 'KYC documents bucket already exists';
            END IF;
          END $$;
        `;

        const { error: sqlError } = await supabase.rpc('execute_sql', {
          sql: createBucketsSQL
        });

        if (sqlError) {
          addLog(`SQL approach failed: ${sqlError.message}`);
          throw sqlError; // Fall back to the API approach
        }

        addLog('Successfully created buckets using SQL');

        // Refresh the bucket list
        const { data: updatedBuckets } = await supabase.storage.listBuckets();
        const updatedBucketNames = updatedBuckets.map(bucket => bucket.name);
        addLog(`Updated bucket list: ${updatedBucketNames.join(', ')}`);

      } catch (error) {
        // Log the SQL error
        addLog(`SQL error details: ${error instanceof Error ? error.message : String(error)}`);
        // Fall back to the API approach if SQL fails
        addLog('Falling back to API approach for bucket creation...');

        // Create buckets if they don't exist using the API
        for (const bucket of bucketsToCreate) {
          if (!existingBucketNames.includes(bucket.name)) {
            addLog(`Creating bucket: ${bucket.name}`);

            try {
              const { error: createError } = await supabase.storage.createBucket(bucket.name, {
                public: bucket.public,
                fileSizeLimit: bucket.fileSizeLimit,
                allowedMimeTypes: bucket.allowedMimeTypes
              });

              if (createError) {
                addLog(`Warning: Error creating bucket ${bucket.name}: ${createError.message}`);
                // Continue anyway - we'll try to use the bucket even if creation failed
              } else {
                addLog(`Successfully created bucket: ${bucket.name}`);
              }
            } catch (err) {
              addLog(`Exception creating bucket ${bucket.name}: ${err instanceof Error ? err.message : String(err)}`);
              // Continue with the next bucket
            }
          } else {
            addLog(`Bucket already exists: ${bucket.name}`);

            // Try to update bucket configuration
            try {
              const { error: updateError } = await supabase.storage.updateBucket(bucket.name, {
                public: bucket.public,
                fileSizeLimit: bucket.fileSizeLimit,
                allowedMimeTypes: bucket.allowedMimeTypes
              });

              if (updateError) {
                addLog(`Warning: Error updating bucket ${bucket.name}: ${updateError.message}`);
                // Continue anyway
              } else {
                addLog(`Successfully updated bucket: ${bucket.name}`);
              }
            } catch (err) {
              addLog(`Exception updating bucket ${bucket.name}: ${err instanceof Error ? err.message : String(err)}`);
            }
          }
        }
      }

      // Set up RLS policies for the buckets
      addLog('Setting up RLS policies for storage buckets...');

      try {
        const rlsPoliciesSQL = `
          -- Enable RLS on storage.objects
          ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

          -- Properties bucket policies

          -- Allow anyone to read from public properties bucket
          DROP POLICY IF EXISTS "Allow public read access to properties bucket" ON storage.objects;
          CREATE POLICY "Allow public read access to properties bucket" ON storage.objects
            FOR SELECT
            USING (bucket_id = 'properties');

          -- Allow authenticated users to insert into properties bucket
          DROP POLICY IF EXISTS "Allow authenticated users to upload to properties bucket" ON storage.objects;
          CREATE POLICY "Allow authenticated users to upload to properties bucket" ON storage.objects
            FOR INSERT
            WITH CHECK (
              bucket_id = 'properties' AND
              auth.role() = 'authenticated'
            );

          -- Allow property owners to update their own property images
          DROP POLICY IF EXISTS "Allow property owners to update their property images" ON storage.objects;
          CREATE POLICY "Allow property owners to update their property images" ON storage.objects
            FOR UPDATE
            USING (
              bucket_id = 'properties' AND
              EXISTS (
                SELECT 1 FROM properties p
                JOIN property_images pi ON p.id = pi.property_id
                WHERE p.owner_id = auth.uid() AND pi.image_url LIKE '%' || name
              )
            );

          -- KYC documents bucket policies

          -- Users can only select their own documents
          DROP POLICY IF EXISTS "Users can select their own KYC documents" ON storage.objects;
          CREATE POLICY "Users can select their own KYC documents" ON storage.objects
            FOR SELECT
            USING (
              bucket_id = 'kyc_documents' AND
              (auth.uid()::text = SPLIT_PART(name, '/', 1) OR
               EXISTS (
                 SELECT 1 FROM profiles
                 WHERE id = auth.uid() AND role = 'admin'
               ))
            );

          -- Users can only insert their own documents
          DROP POLICY IF EXISTS "Users can insert their own KYC documents" ON storage.objects;
          CREATE POLICY "Users can insert their own KYC documents" ON storage.objects
            FOR INSERT
            WITH CHECK (
              bucket_id = 'kyc_documents' AND
              auth.uid()::text = SPLIT_PART(name, '/', 1)
            );

          -- Users can only update their own documents
          DROP POLICY IF EXISTS "Users can update their own KYC documents" ON storage.objects;
          CREATE POLICY "Users can update their own KYC documents" ON storage.objects
            FOR UPDATE
            USING (
              bucket_id = 'kyc_documents' AND
              (auth.uid()::text = SPLIT_PART(name, '/', 1) OR
               EXISTS (
                 SELECT 1 FROM profiles
                 WHERE id = auth.uid() AND role = 'admin'
               ))
            );

          -- Users can only delete their own documents
          DROP POLICY IF EXISTS "Users can delete their own KYC documents" ON storage.objects;
          CREATE POLICY "Users can delete their own KYC documents" ON storage.objects
            FOR DELETE
            USING (
              bucket_id = 'kyc_documents' AND
              (auth.uid()::text = SPLIT_PART(name, '/', 1) OR
               EXISTS (
                 SELECT 1 FROM profiles
                 WHERE id = auth.uid() AND role = 'admin'
               ))
            );
        `;

        const { error: rlsError } = await supabase.rpc('execute_sql', {
          sql: rlsPoliciesSQL
        });

        if (rlsError) {
          addLog(`Warning: Error setting up RLS policies: ${rlsError.message}`);
          // Continue anyway - the buckets might still be usable
        } else {
          addLog('Successfully set up RLS policies for storage buckets');
        }
      } catch (rlsErr) {
        addLog(`Exception setting up RLS policies: ${rlsErr instanceof Error ? rlsErr.message : String(rlsErr)}`);
        // Continue anyway
      }

      addLog('Storage bucket setup completed successfully!');
      setStorageSetupComplete(true);
    } catch (err) {
      console.error('Error setting up storage buckets:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      addLog(`Error: ${err instanceof Error ? err.message : 'An unknown error occurred'}`);
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-white rounded-lg shadow-md p-6 text-center">
          <p className="text-gray-700">Please log in to access the storage setup page.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Storage System Setup</h1>
        <p className="text-gray-600 mb-6">
          This page helps you set up the necessary database tables and storage buckets for the file storage system.
        </p>

        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg text-red-700">
            <div className="flex items-center">
              <AlertCircle className="h-5 w-5 mr-2" />
              <span>{error}</span>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          {/* Database Setup */}
          <div className="border border-gray-200 rounded-lg p-6">
            <div className="flex items-center mb-4">
              <Database className="h-6 w-6 text-indigo-500 mr-2" />
              <h2 className="text-xl font-semibold text-gray-800">Database Setup</h2>
            </div>
            <p className="text-gray-600 mb-4">
              Create the necessary database tables for storing file metadata.
            </p>
            <button
              onClick={setupDatabase}
              disabled={loading || dbSetupComplete}
              className={`w-full py-2 px-4 rounded-lg flex items-center justify-center ${
                dbSetupComplete
                  ? 'bg-green-500 text-white cursor-not-allowed'
                  : loading
                    ? 'bg-gray-400 text-white cursor-not-allowed'
                    : 'bg-indigo-600 hover:bg-indigo-700 text-white'
              }`}
            >
              {loading ? (
                <LoadingSpinner size="sm" />
              ) : dbSetupComplete ? (
                <>
                  <CheckCircle className="h-5 w-5 mr-2" />
                  Setup Complete
                </>
              ) : (
                'Set Up Database Tables'
              )}
            </button>
          </div>

          {/* Storage Buckets Setup */}
          <div className="border border-gray-200 rounded-lg p-6">
            <div className="flex items-center mb-4">
              <HardDrive className="h-6 w-6 text-indigo-500 mr-2" />
              <h2 className="text-xl font-semibold text-gray-800">Storage Buckets</h2>
            </div>
            <p className="text-gray-600 mb-4">
              Create the necessary storage buckets for storing files.
            </p>
            <button
              onClick={setupStorageBuckets}
              disabled={loading || storageSetupComplete}
              className={`w-full py-2 px-4 rounded-lg flex items-center justify-center ${
                storageSetupComplete
                  ? 'bg-green-500 text-white cursor-not-allowed'
                  : loading
                    ? 'bg-gray-400 text-white cursor-not-allowed'
                    : 'bg-indigo-600 hover:bg-indigo-700 text-white'
              }`}
            >
              {loading ? (
                <LoadingSpinner size="sm" />
              ) : storageSetupComplete ? (
                <>
                  <CheckCircle className="h-5 w-5 mr-2" />
                  Setup Complete
                </>
              ) : (
                'Set Up Storage Buckets'
              )}
            </button>
          </div>
        </div>

        {/* Logs */}
        <div className="border border-gray-200 rounded-lg p-4">
          <h3 className="text-lg font-medium text-gray-800 mb-2">Setup Logs</h3>
          <div className="bg-gray-50 p-3 rounded-lg h-64 overflow-y-auto font-mono text-sm">
            {logs.length === 0 ? (
              <p className="text-gray-500">No logs yet. Click one of the setup buttons to begin.</p>
            ) : (
              logs.map((log, index) => (
                <div key={index} className="mb-1">
                  <span className="text-gray-400">[{new Date().toLocaleTimeString()}]</span>{' '}
                  <span className={log.startsWith('Error') ? 'text-red-600' : 'text-gray-800'}>
                    {log}
                  </span>
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
