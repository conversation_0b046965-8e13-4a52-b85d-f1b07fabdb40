'use client';

import React, { forwardRef, useState } from 'react';
import { Eye, EyeOff, AlertCircle, CheckCircle } from 'lucide-react';
import { cn } from '@/utils/cn';

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  success?: string;
  hint?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  variant?: 'default' | 'filled' | 'outline';
  inputSize?: 'sm' | 'md' | 'lg';
  showPasswordToggle?: boolean;
  required?: boolean;
}

const Input = forwardRef<HTMLInputElement, InputProps>(
  ({
    className,
    label,
    error,
    success,
    hint,
    leftIcon,
    rightIcon,
    variant = 'default',
    inputSize = 'md',
    showPasswordToggle = false,
    required = false,
    type = 'text',
    id,
    ...props
  }, ref) => {
    const [showPassword, setShowPassword] = useState(false);
    const [isFocused, setIsFocused] = useState(false);
    
    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;
    const inputType = showPasswordToggle && type === 'password' 
      ? (showPassword ? 'text' : 'password') 
      : type;

    const baseStyles = [
      'w-full transition-all duration-200',
      'focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900',
      'disabled:opacity-50 disabled:cursor-not-allowed',
    ];

    const variants = {
      default: [
        'bg-indigo-800/50 border border-indigo-700 text-white placeholder-indigo-300',
        'focus:border-indigo-500 focus:ring-indigo-500',
        error && 'border-red-500 focus:border-red-500 focus:ring-red-500',
        success && 'border-green-500 focus:border-green-500 focus:ring-green-500',
      ],
      filled: [
        'bg-gray-800 border-0 text-white placeholder-gray-400',
        'focus:bg-gray-700 focus:ring-indigo-500',
        error && 'bg-red-900/20 focus:ring-red-500',
        success && 'bg-green-900/20 focus:ring-green-500',
      ],
      outline: [
        'bg-transparent border-2 border-gray-600 text-white placeholder-gray-400',
        'focus:border-indigo-500 focus:ring-indigo-500',
        error && 'border-red-500 focus:border-red-500 focus:ring-red-500',
        success && 'border-green-500 focus:border-green-500 focus:ring-green-500',
      ],
    };

    const sizes = {
      sm: 'px-3 py-2 text-sm rounded-md',
      md: 'px-4 py-3 text-base rounded-lg',
      lg: 'px-5 py-4 text-lg rounded-xl',
    };

    const iconSizes = {
      sm: 16,
      md: 20,
      lg: 24,
    };

    const paddingWithIcons = {
      sm: {
        left: leftIcon ? 'pl-10' : '',
        right: (rightIcon || showPasswordToggle) ? 'pr-10' : '',
      },
      md: {
        left: leftIcon ? 'pl-12' : '',
        right: (rightIcon || showPasswordToggle) ? 'pr-12' : '',
      },
      lg: {
        left: leftIcon ? 'pl-14' : '',
        right: (rightIcon || showPasswordToggle) ? 'pr-14' : '',
      },
    };

    return (
      <div className="space-y-2">
        {label && (
          <label 
            htmlFor={inputId}
            className={cn(
              'block text-sm font-medium text-gray-300',
              required && "after:content-['*'] after:ml-0.5 after:text-red-500"
            )}
          >
            {label}
          </label>
        )}
        
        <div className="relative">
          {leftIcon && (
            <div className={cn(
              'absolute left-0 top-0 h-full flex items-center justify-center text-gray-400',
              inputSize === 'sm' ? 'w-10' : inputSize === 'lg' ? 'w-14' : 'w-12'
            )}>
              {React.cloneElement(leftIcon as React.ReactElement, {
                size: iconSizes[inputSize],
                'aria-hidden': true,
              })}
            </div>
          )}
          
          <input
            ref={ref}
            id={inputId}
            type={inputType}
            className={cn(
              baseStyles,
              variants[variant],
              sizes[inputSize],
              paddingWithIcons[inputSize].left,
              paddingWithIcons[inputSize].right,
              isFocused && 'ring-2',
              className
            )}
            onFocus={(e) => {
              setIsFocused(true);
              props.onFocus?.(e);
            }}
            onBlur={(e) => {
              setIsFocused(false);
              props.onBlur?.(e);
            }}
            aria-invalid={error ? 'true' : 'false'}
            aria-describedby={cn(
              error && `${inputId}-error`,
              success && `${inputId}-success`,
              hint && `${inputId}-hint`
            )}
            {...props}
          />
          
          {(rightIcon || showPasswordToggle) && (
            <div className={cn(
              'absolute right-0 top-0 h-full flex items-center justify-center',
              inputSize === 'sm' ? 'w-10' : inputSize === 'lg' ? 'w-14' : 'w-12'
            )}>
              {showPasswordToggle && type === 'password' ? (
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="text-gray-400 hover:text-gray-300 focus:outline-none focus:text-gray-300"
                  aria-label={showPassword ? 'Hide password' : 'Show password'}
                >
                  {showPassword ? (
                    <EyeOff size={iconSizes[inputSize]} />
                  ) : (
                    <Eye size={iconSizes[inputSize]} />
                  )}
                </button>
              ) : rightIcon ? (
                React.cloneElement(rightIcon as React.ReactElement, {
                  size: iconSizes[inputSize],
                  'aria-hidden': true,
                  className: 'text-gray-400',
                })
              ) : null}
            </div>
          )}
          
          {(error || success) && (
            <div className={cn(
              'absolute right-0 top-0 h-full flex items-center justify-center',
              (rightIcon || showPasswordToggle) ? 'mr-10' : 'mr-3'
            )}>
              {error ? (
                <AlertCircle 
                  size={iconSizes[inputSize]} 
                  className="text-red-500" 
                  aria-hidden="true"
                />
              ) : success ? (
                <CheckCircle 
                  size={iconSizes[inputSize]} 
                  className="text-green-500" 
                  aria-hidden="true"
                />
              ) : null}
            </div>
          )}
        </div>
        
        {hint && !error && !success && (
          <p id={`${inputId}-hint`} className="text-sm text-gray-400">
            {hint}
          </p>
        )}
        
        {error && (
          <p 
            id={`${inputId}-error`} 
            className="text-sm text-red-400 flex items-center gap-1"
            role="alert"
          >
            <AlertCircle size={14} aria-hidden="true" />
            {error}
          </p>
        )}
        
        {success && (
          <p 
            id={`${inputId}-success`} 
            className="text-sm text-green-400 flex items-center gap-1"
          >
            <CheckCircle size={14} aria-hidden="true" />
            {success}
          </p>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input';

export default Input;
