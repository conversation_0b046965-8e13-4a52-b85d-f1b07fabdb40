'use client';
import React, { useState, useEffect } from 'react';
import { createClient } from '@supabase/supabase-js';
import {
  Building, Search, Filter, Wallet,
  MapPin, DollarSign, PercentCircle,
  ChevronDown, X, ExternalLink,
  AlertCircle, ShieldCheck, Loader2
} from 'lucide-react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import PropertyCard, { Property } from '../../components/PropertyCard';
import { mockProperties, getMockPropertyById } from '../../data/mockProperties';
import { useWalletAuth } from '@/context/WalletAuthContext';
import { useConnectModal } from '@rainbow-me/rainbowkit';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Selected property modal
const PropertyModal = ({
  property,
  onClose,
  onConnectWallet,
  onCompleteKYC
}: {
  property: Property;
  onClose: () => void;
  onConnectWallet: () => void;
  onCompleteKYC: () => void;
}) => {
  const totalShares = property.total_shares || 1000;
  const fundedPercentage = property.funded_percentage || 0;
  const investorsCount = property.investors_count || 0;
  const priceInEth = property.priceInEth || Math.round(property.price / 2000);

  // Get wallet and auth status
  const { isAuthenticated, isWalletConnected, kycStatus, loading } = useWalletAuth();

  // Determine button state
  const getButtonContent = () => {
    if (loading) {
      return (
        <>
          <Loader2 size={20} className="animate-spin mr-2" />
          <span>Loading...</span>
        </>
      );
    }

    if (!isWalletConnected) {
      return (
        <>
          <Wallet size={20} />
          <span>Connect Wallet to Invest</span>
        </>
      );
    }

    if (!isAuthenticated) {
      return (
        <>
          <Wallet size={20} />
          <span>Sign In with Wallet</span>
        </>
      );
    }

    if (kycStatus !== 'verified') {
      return (
        <>
          <ShieldCheck size={20} />
          <span>Complete KYC to Invest</span>
        </>
      );
    }

    return (
      <>
        <Wallet size={20} />
        <span>Invest in Property</span>
      </>
    );
  };

  // Determine action based on auth state
  const handleAction = () => {
    if (!isWalletConnected || !isAuthenticated) {
      onConnectWallet();
    } else if (kycStatus !== 'verified') {
      onCompleteKYC();
    } else {
      // Proceed with investment
      alert('Investment functionality will be available soon!');
    }
  };

  return (
    <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900 rounded-xl shadow-2xl overflow-hidden max-w-4xl w-full max-h-[90vh] flex flex-col">
        <div className="relative h-64 md:h-80 bg-gray-800">
          {property.image_url ? (
            <img
              src={property.image_url}
              alt={property.name}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <Building size={48} className="text-gray-400" />
            </div>
          )}

          <button
            onClick={onClose}
            className="absolute top-4 right-4 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors"
          >
            <X size={20} />
          </button>

          {property.image_cid && (
            <div className="absolute bottom-4 left-4 bg-indigo-100 text-indigo-800 px-3 py-1 rounded-full text-xs font-medium flex items-center">
              <ExternalLink size={12} className="mr-1" />
              Stored on IPFS
            </div>
          )}
        </div>

        <div className="flex-1 overflow-y-auto p-6">
          <div className="flex flex-col md:flex-row justify-between gap-4">
            <div className="flex-1">
              <h2 className="text-2xl font-bold text-white mb-2">{property.name}</h2>
              <div className="flex items-center text-gray-400 mb-4">
                <MapPin size={16} className="mr-1" />
                <span>{property.location}</span>
              </div>
            </div>

            <div className="flex flex-col items-end">
              <div className="text-2xl font-bold text-indigo-400">{priceInEth} ETH</div>
              <div className="text-green-500 font-medium">{property.return_rate}% Return</div>
            </div>
          </div>

          <div className="mt-6">
            <h3 className="text-lg font-semibold text-white mb-2">About this property</h3>
            <p className="text-gray-400">
              {property.description || 'No description provided for this property.'}
            </p>
          </div>

          <div className="mt-8 grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-gray-800 p-4 rounded-lg">
              <div className="text-gray-500 text-sm">Price</div>
              <div className="font-bold text-white">{priceInEth} ETH</div>
            </div>
            <div className="bg-gray-800 p-4 rounded-lg">
              <div className="text-gray-500 text-sm">Return Rate</div>
              <div className="font-bold text-green-500">{property.return_rate}%</div>
            </div>
            <div className="bg-gray-800 p-4 rounded-lg">
              <div className="text-gray-500 text-sm">Status</div>
              <div className="font-bold text-white capitalize">{property.status}</div>
            </div>
            <div className="bg-gray-800 p-4 rounded-lg">
              <div className="text-gray-500 text-sm">Shares</div>
              <div className="font-bold text-white">{totalShares.toLocaleString()}</div>
            </div>
          </div>

          <div className="mt-8 mb-6">
            <div className="flex justify-between text-sm mb-1">
              <span className="text-white">{fundedPercentage}% Funded</span>
              <span className="text-gray-400">{investorsCount} Investors</span>
            </div>
            <div className="h-2 bg-gray-800 rounded-full overflow-hidden">
              <div
                className="h-full bg-gradient-to-r from-purple-500 to-indigo-500"
                style={{ width: `${fundedPercentage}%` }}
              ></div>
            </div>
          </div>

          {/* KYC Status Warning */}
          {isAuthenticated && isWalletConnected && kycStatus !== 'verified' && (
            <div className="mt-4 bg-amber-900/30 border border-amber-800 rounded-lg p-4 flex items-start">
              <AlertCircle className="text-amber-500 h-5 w-5 mt-0.5 mr-3 flex-shrink-0" />
              <div>
                <h4 className="text-amber-300 font-medium">KYC Verification Required</h4>
                <p className="text-amber-200/70 text-sm mt-1">
                  You need to complete KYC verification before investing in properties.
                  {kycStatus === 'pending' && ' Your verification is currently pending review.'}
                </p>
              </div>
            </div>
          )}
        </div>

        <div className="border-t border-gray-800 p-6">
          <button
            onClick={handleAction}
            disabled={loading}
            className="w-full py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-lg font-semibold hover:from-indigo-700 hover:to-purple-700 transition-colors flex items-center justify-center gap-2 disabled:opacity-70 disabled:cursor-not-allowed"
          >
            {getButtonContent()}
          </button>
        </div>
      </div>
    </div>
  );
};

// Marketplace page
export default function Marketplace() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [properties, setProperties] = useState<Property[]>([]);
  const [filteredProperties, setFilteredProperties] = useState<Property[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedProperty, setSelectedProperty] = useState<Property | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  // Wallet and auth connection
  const { isAuthenticated, isWalletConnected, signInWithWallet, kycStatus } = useWalletAuth();
  const { openConnectModal } = useConnectModal();

  // Filter states
  const [filtersOpen, setFiltersOpen] = useState(false);
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 1000000]);
  const [returnRange, setReturnRange] = useState<[number, number]>([0, 20]);
  const [maxPriceValue, setMaxPriceValue] = useState(1000000);

  // Check for property ID in URL parameters
  useEffect(() => {
    const propertyId = searchParams.get('propertyId');
    if (propertyId && properties.length > 0) {
      const property = properties.find(p => p.id === propertyId);
      if (property) {
        setSelectedProperty(property);
      }
    }
  }, [searchParams, properties]);

  // Load properties
  useEffect(() => {
    const fetchProperties = async () => {
      try {
        // Try to fetch from Supabase first
        const { data, error } = await supabase
          .from('properties')
          .select('*')
          .eq('published', true)
          .eq('status', 'available');

        if (error) throw error;

        // If we got data from Supabase, use it
        if (data && data.length > 0) {
          setProperties(data);
          setFilteredProperties(data);

          // Find the maximum price for the filter
          const maxPrice = Math.max(...data.map(p => p.price)) || 1000000;
          setMaxPriceValue(maxPrice);
          setPriceRange([0, maxPrice]);
        } else {
          // Otherwise fall back to mock data
          console.log('Using mock data instead of Supabase data');
          setProperties(mockProperties);
          setFilteredProperties(mockProperties);

          const maxPrice = Math.max(...mockProperties.map(p => p.price)) || 1000000;
          setMaxPriceValue(maxPrice);
          setPriceRange([0, maxPrice]);
        }
      } catch (error) {
        console.error('Error fetching properties:', error);
        // Fall back to mock data on error
        setProperties(mockProperties);
        setFilteredProperties(mockProperties);

        const maxPrice = Math.max(...mockProperties.map(p => p.price)) || 1000000;
        setMaxPriceValue(maxPrice);
        setPriceRange([0, maxPrice]);
      } finally {
        setLoading(false);
      }
    };

    fetchProperties();
  }, []);

  // Apply filters and search
  useEffect(() => {
    let results = [...properties];

    // Apply search term
    if (searchTerm) {
      const lowerCaseSearch = searchTerm.toLowerCase();
      results = results.filter(
        p => p.name.toLowerCase().includes(lowerCaseSearch) ||
             p.location.toLowerCase().includes(lowerCaseSearch) ||
             p.description?.toLowerCase().includes(lowerCaseSearch)
      );
    }

    // Apply price filter
    results = results.filter(p =>
      p.price >= priceRange[0] && p.price <= priceRange[1]
    );

    // Apply return rate filter
    results = results.filter(p =>
      p.return_rate >= returnRange[0] && p.return_rate <= returnRange[1]
    );

    setFilteredProperties(results);
  }, [searchTerm, priceRange, returnRange, properties]);

  // Handle wallet connection and authentication
  const handleConnectWallet = () => {
    if (!isWalletConnected && openConnectModal) {
      // Connect wallet first
      openConnectModal();
    } else if (isWalletConnected && !isAuthenticated) {
      // If wallet is connected but not authenticated, sign in
      signInWithWallet();
    } else if (isAuthenticated) {
      // If already authenticated, show investment UI or redirect to investment page
      alert('Your wallet is connected and authenticated! Investment functionality will be available soon.');
    } else {
      // Fallback if RainbowKit modal is not available
      alert('Unable to open wallet connection. Please try again later.');
    }
  };

  // Handle KYC verification
  const handleCompleteKYC = () => {
    router.push('/kyc-verification');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-950 to-indigo-950">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">Property Marketplace</h1>
          <p className="text-gray-300 text-xl max-w-3xl mx-auto">
            Browse available properties and invest in tokenized real estate with BrickChain
          </p>
        </div>

        {/* Search and Filter */}
        <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-xl p-4 mb-12">
          <div className="flex flex-col md:flex-row gap-4">
            {/* Search input */}
            <div className="flex-1 relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search size={20} className="text-gray-400" />
              </div>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="block w-full pl-10 pr-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                placeholder="Search by property name, location..."
              />
            </div>

            {/* Filter button */}
            <button
              onClick={() => setFiltersOpen(!filtersOpen)}
              className="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg flex items-center justify-center gap-2 transition-colors"
            >
              <Filter size={20} />
              <span>Filters</span>
              <ChevronDown size={16} className={`transform transition-transform ${filtersOpen ? 'rotate-180' : ''}`} />
            </button>
          </div>

          {/* Filter panel */}
          {filtersOpen && (
            <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-6 pt-4 border-t border-gray-800">
              {/* Price range */}
              <div>
                <label className="block text-gray-300 text-sm mb-2 flex items-center">
                  <DollarSign size={16} className="mr-1" />
                  Price Range (USD)
                </label>
                <div className="flex items-center gap-4">
                  <input
                    type="number"
                    value={priceRange[0]}
                    onChange={(e) => setPriceRange([parseInt(e.target.value) || 0, priceRange[1]])}
                    className="w-24 bg-gray-800 border border-gray-700 rounded px-3 py-2 text-white"
                    min="0"
                    max={priceRange[1]}
                  />
                  <span className="text-gray-400">to</span>
                  <input
                    type="number"
                    value={priceRange[1]}
                    onChange={(e) => setPriceRange([priceRange[0], parseInt(e.target.value) || maxPriceValue])}
                    className="w-24 bg-gray-800 border border-gray-700 rounded px-3 py-2 text-white"
                    min={priceRange[0]}
                    max={maxPriceValue}
                  />
                </div>
                <input
                  type="range"
                  min="0"
                  max={maxPriceValue}
                  value={priceRange[1]}
                  onChange={(e) => setPriceRange([priceRange[0], parseInt(e.target.value)])}
                  className="w-full mt-2 accent-indigo-600"
                />
              </div>

              {/* Return rate range */}
              <div>
                <label className="block text-gray-300 text-sm mb-2 flex items-center">
                  <PercentCircle size={16} className="mr-1" />
                  Return Rate (%)
                </label>
                <div className="flex items-center gap-4">
                  <input
                    type="number"
                    value={returnRange[0]}
                    onChange={(e) => setReturnRange([parseInt(e.target.value) || 0, returnRange[1]])}
                    className="w-24 bg-gray-800 border border-gray-700 rounded px-3 py-2 text-white"
                    min="0"
                    max={returnRange[1]}
                  />
                  <span className="text-gray-400">to</span>
                  <input
                    type="number"
                    value={returnRange[1]}
                    onChange={(e) => setReturnRange([returnRange[0], parseInt(e.target.value) || 20])}
                    className="w-24 bg-gray-800 border border-gray-700 rounded px-3 py-2 text-white"
                    min={returnRange[0]}
                    max="20"
                  />
                </div>
                <input
                  type="range"
                  min="0"
                  max="20"
                  value={returnRange[1]}
                  onChange={(e) => setReturnRange([returnRange[0], parseInt(e.target.value)])}
                  className="w-full mt-2 accent-indigo-600"
                />
              </div>

              {/* Reset filters button */}
              <div className="md:col-span-2 flex justify-end">
                <button
                  onClick={() => {
                    setSearchTerm('');
                    setPriceRange([0, maxPriceValue]);
                    setReturnRange([0, 20]);
                  }}
                  className="text-indigo-400 hover:text-indigo-300 font-medium flex items-center"
                >
                  <X size={16} className="mr-1" />
                  Reset Filters
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Property Grid */}
        {loading ? (
          <div className="flex items-center justify-center min-h-[40vh]">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
          </div>
        ) : filteredProperties.length === 0 ? (
          <div className="text-center py-16 bg-gray-900/50 backdrop-blur-sm border border-gray-800 rounded-xl">
            <Building size={48} className="mx-auto text-gray-500 mb-4" />
            <h3 className="text-xl font-semibold text-white mb-2">No properties found</h3>
            <p className="text-gray-400">
              {searchTerm || priceRange[0] > 0 || returnRange[0] > 0 ? (
                'Try adjusting your filters or search terms'
              ) : (
                'There are no properties available for investment at this time'
              )}
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredProperties.map((property) => (
              <PropertyCard
                key={property.id}
                property={property}
                onView={setSelectedProperty}
                variant="marketplace"
              />
            ))}
          </div>
        )}
      </div>

      {/* Property Detail Modal */}
      {selectedProperty && (
        <PropertyModal
          property={selectedProperty}
          onClose={() => setSelectedProperty(null)}
          onConnectWallet={handleConnectWallet}
          onCompleteKYC={handleCompleteKYC}
        />
      )}
    </div>
  );
}