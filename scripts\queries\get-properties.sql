-- Get all properties with owner information
SELECT 
  p.id,
  p.name,
  p.description,
  p.price,
  p.location,
  p.property_type,
  p.bedrooms,
  p.bathrooms,
  p.square_feet,
  p.status,
  p.created_at,
  u.email as owner_email,
  u.wallet_address as owner_wallet,
  COUNT(i.id) as investment_count,
  COALESCE(SUM(i.amount), 0) as total_invested
FROM properties p
LEFT JOIN users u ON p.owner_id = u.id
LEFT JOIN investments i ON p.id = i.property_id AND i.status = 'completed'
GROUP BY p.id, u.email, u.wallet_address
ORDER BY p.created_at DESC;
