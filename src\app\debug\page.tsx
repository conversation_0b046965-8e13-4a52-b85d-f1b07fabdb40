'use client';
import React, { useState, useEffect } from 'react';
import { createClient } from '@supabase/supabase-js';
import Link from 'next/link';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';
const supabase = createClient(supabaseUrl, supabaseAnonKey);

export default function DebugPage() {
  const [email, setEmail] = useState('');
  const [userId, setUserId] = useState('');
  const [fullName, setFullName] = useState('');
  const [phone, setPhone] = useState('');
  const [role, setRole] = useState('investor');
  const [companyName, setCompanyName] = useState('');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');
  const [users, setUsers] = useState<any[]>([]);
  const [profiles, setProfiles] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState('users');

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch auth users
        const { data: userData, error: userError } = await supabase.auth.admin.listUsers();
        if (userError) {
          console.error('Error fetching users:', userError);
        } else if (userData?.users) {
          setUsers(userData.users);
        }

        // Fetch profiles
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('*');

        if (profileError) {
          console.error('Error fetching profiles:', profileError);
        } else {
          setProfiles(profileData || []);
        }
      } catch (err) {
        console.error('Error in data fetch:', err);
      }
    };

    fetchData();
  }, []);

  const findUserByEmail = async () => {
    if (!email) {
      setError('Please enter an email address');
      return;
    }

    setLoading(true);
    setMessage('');
    setError('');

    try {
      // First try a direct sign-in with OTP to verify the email exists
      // This doesn't create a new user and doesn't actually send an email
      const { error: userError } = await supabase.auth.signInWithOtp({
        email,
        options: {
          shouldCreateUser: false
        }
      });

      if (userError) {
        setError(`Error: ${userError.message}`);
        return;
      }

      // Since we don't have admin access, let's prompt user to manually enter the user ID
      setMessage(`Email verified. Please enter the user ID manually if you know it, or look it up in your Supabase dashboard.`);

      // For manual user ID entry
      const userIdInput = prompt('Please enter the user ID for this email:');
      if (!userIdInput) {
        setError('User ID is required to continue');
        return;
      }

      setUserId(userIdInput);

      // Check if profile exists for this ID
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userIdInput)
        .maybeSingle();

      if (profileError && profileError.code !== 'PGRST116') {
        setError(`Error checking profile: ${profileError.message}`);
      } else if (profile) {
        setMessage(prevMsg => `${prevMsg}\nProfile already exists for this user.`);
        setFullName(profile.full_name || '');
        setPhone(profile.phone || '');
        setRole(profile.role || 'investor');
        setCompanyName(profile.company_name || '');
      } else {
        setMessage(prevMsg => `${prevMsg}\nNo profile found for this user.`);
      }
    } catch (err) {
      setError(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const createOrUpdateProfile = async () => {
    if (!userId) {
      setError('No user ID found. Please search for a user first.');
      return;
    }

    if (!fullName || !phone || !role) {
      setError('Please fill in all required fields.');
      return;
    }

    setLoading(true);
    setMessage('');
    setError('');

    try {
      setMessage('Due to Row-Level Security (RLS), we cannot directly modify the profiles table from the browser.');
      setMessage(prev => prev + '\n\nPlease use the Supabase dashboard to manually create the profile with the following values:');
      setMessage(prev => prev + `\n\nUser ID: ${userId}`);
      setMessage(prev => prev + `\nFull Name: ${fullName}`);
      setMessage(prev => prev + `\nPhone: ${phone}`);
      setMessage(prev => prev + `\nRole: ${role}`);
      if (role === 'owner' && companyName) {
        setMessage(prev => prev + `\nCompany Name: ${companyName}`);
      }
      setMessage(prev => prev + `\nCreated At: ${new Date().toISOString()}`);

      // Provide guidance on bypassing RLS
      setMessage(prev => prev + '\n\nTo properly insert profiles, you need to either:');
      setMessage(prev => prev + '\n1. Use the Supabase dashboard to add the profile');
      setMessage(prev => prev + '\n2. Set up a server-side API endpoint using the service_role key');
      setMessage(prev => prev + '\n3. Modify your RLS policies to allow this operation');

    } catch (err) {
      setError(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-950 to-indigo-950 p-8 text-white">
      <div className="max-w-5xl mx-auto">
        <h1 className="text-3xl font-bold mb-2">BrickChain Debug Panel</h1>
        <p className="text-gray-400 mb-8">Admin tools for diagnostics and fixes</p>

        <div className="bg-gray-900/50 p-4 rounded-lg border border-gray-800 mb-8">
          <div className="flex border-b border-gray-700 mb-4">
            <button
              className={`px-4 py-2 ${activeTab === 'users' ? 'bg-purple-800/50 text-white border-b-2 border-purple-500' : 'text-gray-400'}`}
              onClick={() => setActiveTab('users')}
            >
              Manage Users
            </button>
            <button
              className={`px-4 py-2 ${activeTab === 'data' ? 'bg-purple-800/50 text-white border-b-2 border-purple-500' : 'text-gray-400'}`}
              onClick={() => setActiveTab('data')}
            >
              View Data
            </button>
          </div>

          {activeTab === 'users' && (
            <div>
              <div className="mb-6">
                <h2 className="text-xl font-semibold mb-4">Find User by Email</h2>
                <div className="flex space-x-2">
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="flex-1 p-2 bg-gray-800 border border-gray-700 rounded"
                    placeholder="<EMAIL>"
                  />
                  <button
                    onClick={findUserByEmail}
                    disabled={loading}
                    className="bg-blue-600 text-white px-4 py-2 rounded"
                  >
                    {loading ? 'Searching...' : 'Find User'}
                  </button>
                </div>
              </div>

              {userId && (
                <div className="border-t border-gray-700 pt-6 mb-6">
                  <h2 className="text-xl font-semibold mb-4">Create/Update Profile</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <label className="block text-gray-300 text-sm mb-1">User ID</label>
                      <input
                        type="text"
                        value={userId}
                        readOnly
                        className="w-full p-2 bg-gray-800 border border-gray-700 rounded opacity-70"
                      />
                    </div>
                    <div>
                      <label className="block text-gray-300 text-sm mb-1">Full Name</label>
                      <input
                        type="text"
                        value={fullName}
                        onChange={(e) => setFullName(e.target.value)}
                        className="w-full p-2 bg-gray-800 border border-gray-700 rounded"
                        placeholder="Full Name"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-gray-300 text-sm mb-1">Phone Number</label>
                      <input
                        type="tel"
                        value={phone}
                        onChange={(e) => setPhone(e.target.value)}
                        className="w-full p-2 bg-gray-800 border border-gray-700 rounded"
                        placeholder="Phone Number"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-gray-300 text-sm mb-1">Role</label>
                      <select
                        value={role}
                        onChange={(e) => setRole(e.target.value)}
                        className="w-full p-2 bg-gray-800 border border-gray-700 rounded"
                        required
                      >
                        <option value="investor">Investor</option>
                        <option value="owner">Property Owner</option>
                        <option value="admin">Admin</option>
                      </select>
                    </div>
                    {role === 'owner' && (
                      <div>
                        <label className="block text-gray-300 text-sm mb-1">Company Name</label>
                        <input
                          type="text"
                          value={companyName}
                          onChange={(e) => setCompanyName(e.target.value)}
                          className="w-full p-2 bg-gray-800 border border-gray-700 rounded"
                          placeholder="Company Name"
                          required
                        />
                      </div>
                    )}
                  </div>

                  <button
                    onClick={createOrUpdateProfile}
                    disabled={loading}
                    className="bg-green-600 text-white px-4 py-2 rounded"
                  >
                    {loading ? 'Processing...' : 'Create/Update Profile'}
                  </button>
                </div>
              )}

              {error && (
                <div className="mt-4 p-3 bg-red-900/40 border border-red-800 rounded-lg">
                  <p className="text-red-300 whitespace-pre-line">{error}</p>
                </div>
              )}

              {message && (
                <div className="mt-4 p-3 bg-green-900/40 border border-green-800 rounded-lg">
                  <p className="text-green-300 whitespace-pre-line">{message}</p>
                </div>
              )}
            </div>
          )}

          {activeTab === 'data' && (
            <div>
              <div className="mb-6">
                <h2 className="text-xl font-semibold mb-4">Auth Users ({users.length})</h2>
                <div className="overflow-x-auto">
                  <table className="min-w-full bg-gray-800/50 border border-gray-700 rounded">
                    <thead>
                      <tr className="bg-gray-700/50">
                        <th className="py-2 px-4 text-left">ID</th>
                        <th className="py-2 px-4 text-left">Email</th>
                        <th className="py-2 px-4 text-left">Created At</th>
                      </tr>
                    </thead>
                    <tbody>
                      {users.map((user) => (
                        <tr key={user.id} className="border-t border-gray-700">
                          <td className="py-2 px-4 font-mono text-sm">{user.id}</td>
                          <td className="py-2 px-4">{user.email}</td>
                          <td className="py-2 px-4">{new Date(user.created_at).toLocaleString()}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              <div className="mb-6">
                <h2 className="text-xl font-semibold mb-4">Profiles ({profiles.length})</h2>
                <div className="overflow-x-auto">
                  <table className="min-w-full bg-gray-800/50 border border-gray-700 rounded">
                    <thead>
                      <tr className="bg-gray-700/50">
                        <th className="py-2 px-4 text-left">ID</th>
                        <th className="py-2 px-4 text-left">Full Name</th>
                        <th className="py-2 px-4 text-left">Role</th>
                        <th className="py-2 px-4 text-left">Phone</th>
                        <th className="py-2 px-4 text-left">Company</th>
                      </tr>
                    </thead>
                    <tbody>
                      {profiles.map((profile) => (
                        <tr key={profile.id} className="border-t border-gray-700">
                          <td className="py-2 px-4 font-mono text-sm">{profile.id}</td>
                          <td className="py-2 px-4">{profile.full_name}</td>
                          <td className="py-2 px-4">{profile.role}</td>
                          <td className="py-2 px-4">{profile.phone}</td>
                          <td className="py-2 px-4">{profile.company_name || '-'}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="flex justify-between">
          <Link href="/" className="text-purple-400 hover:text-purple-300">
            Back to Home
          </Link>
          <div className="space-x-4">
            <Link href="/debug/test-returns" className="text-purple-400 hover:text-purple-300">
              Test IPFS Configuration
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}