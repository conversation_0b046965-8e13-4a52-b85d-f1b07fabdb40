'use client';

import { useState, useEffect } from 'react';
import { 
  AlertCircle, 
  X, 
  RefreshCw, 
  ExternalLink, 
  Wallet, 
  ShieldAlert, 
  Clock, 
  Wifi, 
  HelpCircle 
} from 'lucide-react';
import { type WalletError, type WalletErrorType } from '@/utils/walletErrorHandling';

interface WalletErrorModalProps {
  isOpen: boolean;
  onClose: () => void;
  error: WalletError | null;
  onRetry?: () => void;
}

export default function WalletErrorModal({
  isOpen,
  onClose,
  error,
  onRetry
}: WalletErrorModalProps) {
  const [isVisible, setIsVisible] = useState(false);
  
  // Handle animation when opening/closing
  useEffect(() => {
    if (isOpen) {
      setIsVisible(true);
    } else {
      const timer = setTimeout(() => {
        setIsVisible(false);
      }, 300);
      return () => clearTimeout(timer);
    }
  }, [isOpen]);
  
  if (!isOpen && !isVisible) return null;
  
  // Get icon based on error type
  const getErrorIcon = (type: WalletErrorType) => {
    switch (type) {
      case 'user_rejected':
      case 'signature_rejected':
        return <ShieldAlert className="h-12 w-12 text-amber-500" />;
      case 'timeout':
        return <Clock className="h-12 w-12 text-amber-500" />;
      case 'disconnected':
      case 'chain_disconnected':
        return <Wifi className="h-12 w-12 text-amber-500" />;
      case 'wallet_not_found':
        return <Wallet className="h-12 w-12 text-red-500" />;
      case 'unsupported_chain':
        return <AlertCircle className="h-12 w-12 text-red-500" />;
      default:
        return <AlertCircle className="h-12 w-12 text-red-500" />;
    }
  };
  
  // Get troubleshooting steps based on error type
  const getTroubleshootingSteps = (type: WalletErrorType) => {
    switch (type) {
      case 'user_rejected':
        return [
          'Check your wallet for any pending requests',
          'Make sure your wallet is unlocked',
          'Try connecting again'
        ];
      case 'signature_rejected':
        return [
          'The signature is required for security purposes',
          'The signature does not cost any gas fees',
          'Try connecting again and approve the signature'
        ];
      case 'wallet_not_found':
        return [
          'Install a compatible wallet like MetaMask',
          'Refresh the page after installation',
          'Make sure your wallet extension is enabled'
        ];
      case 'unsupported_chain':
        return [
          'Open your wallet and switch to a supported network',
          'Supported networks include Ethereum, Polygon, and Optimism',
          'Click the network name in your wallet to change networks'
        ];
      case 'timeout':
        return [
          'Check your internet connection',
          'Make sure your wallet is responsive',
          'Try refreshing the page'
        ];
      case 'disconnected':
        return [
          'Check your internet connection',
          'Make sure your wallet is unlocked',
          'Try refreshing the page'
        ];
      default:
        return [
          'Refresh the page and try again',
          'Make sure your wallet is unlocked and up to date',
          'Try using a different browser or wallet'
        ];
    }
  };
  
  // Get helpful links based on error type
  const getHelpfulLinks = (type: WalletErrorType) => {
    const links = [
      {
        text: 'Wallet Connection Troubleshooting',
        url: 'https://support.metamask.io/hc/en-us/articles/360058961911-How-to-fix-the-error-Cannot-connect-to-the-MetaMask-wallet-'
      }
    ];
    
    switch (type) {
      case 'wallet_not_found':
        links.push({
          text: 'Install MetaMask',
          url: 'https://metamask.io/download/'
        });
        break;
      case 'unsupported_chain':
        links.push({
          text: 'How to Add Networks',
          url: 'https://support.metamask.io/hc/en-us/articles/360043227612-How-to-add-a-custom-network-RPC'
        });
        break;
      case 'signature_rejected':
        links.push({
          text: 'Why Sign Messages?',
          url: 'https://support.metamask.io/hc/en-us/articles/360015488751-How-to-sign-data'
        });
        break;
    }
    
    return links;
  };
  
  if (!error) return null;
  
  return (
    <div 
      className={`fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4 transition-opacity duration-300 ${
        isVisible ? 'opacity-100' : 'opacity-0'
      }`}
    >
      <div className="bg-gray-900 rounded-xl shadow-2xl overflow-hidden max-w-md w-full">
        {/* Header */}
        <div className="bg-gray-800 px-6 py-4 flex justify-between items-center">
          <h2 className="text-xl font-bold text-white">Wallet Connection Error</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white"
          >
            <X size={20} />
          </button>
        </div>
        
        {/* Content */}
        <div className="p-6">
          <div className="flex flex-col items-center text-center mb-6">
            {getErrorIcon(error.type)}
            <h3 className="text-xl font-semibold text-white mt-4 mb-2">{error.message}</h3>
            {error.solution && (
              <p className="text-gray-400">{error.solution}</p>
            )}
          </div>
          
          {/* Troubleshooting steps */}
          <div className="bg-gray-800 rounded-lg p-4 mb-6">
            <h4 className="text-white font-medium mb-3 flex items-center">
              <HelpCircle size={16} className="mr-2 text-indigo-400" />
              Troubleshooting Steps
            </h4>
            <ul className="space-y-2">
              {getTroubleshootingSteps(error.type).map((step, index) => (
                <li key={index} className="text-gray-300 text-sm flex">
                  <span className="mr-2">{index + 1}.</span>
                  <span>{step}</span>
                </li>
              ))}
            </ul>
          </div>
          
          {/* Helpful links */}
          <div className="space-y-2 mb-6">
            <h4 className="text-white font-medium mb-2">Helpful Resources</h4>
            {getHelpfulLinks(error.type).map((link, index) => (
              <a
                key={index}
                href={link.url}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center text-indigo-400 hover:text-indigo-300 text-sm"
              >
                <ExternalLink size={14} className="mr-2" />
                <span>{link.text}</span>
              </a>
            ))}
          </div>
          
          {/* Technical details (collapsible) */}
          {error.details && (
            <details className="text-xs text-gray-500 mt-4">
              <summary className="cursor-pointer">Technical Details</summary>
              <div className="mt-2 p-2 bg-gray-800 rounded overflow-x-auto">
                <code>{error.details}</code>
                {error.code && <div className="mt-1">Error code: {error.code}</div>}
              </div>
            </details>
          )}
        </div>
        
        {/* Footer */}
        <div className="border-t border-gray-800 px-6 py-4 flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-800 text-white rounded-lg mr-3 hover:bg-gray-700"
          >
            Close
          </button>
          {error.retry && onRetry && (
            <button
              onClick={onRetry}
              className="px-4 py-2 bg-indigo-600 text-white rounded-lg flex items-center hover:bg-indigo-700"
            >
              <RefreshCw size={16} className="mr-2" />
              Try Again
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
