/**
 * File Storage Utilities
 *
 * This file contains helper functions for handling file uploads, storage, and retrieval
 * for both property images and KYC documents.
 *
 * Features:
 * - Property image upload with IPFS and Supabase fallback
 * - Image validation, optimization and thumbnail generation
 * - Secure KYC document storage with client-side encryption
 * - Document verification status tracking
 */

import { createClient } from '@supabase/supabase-js';
import { uploadToIPFS, type IPFSUploadResponse } from './ipfs';
import CryptoJS from 'crypto-js';
import { optimizeImage, generateThumbnail, extractImageMetadata } from './imageProcessor';

// Types for property images
export type PropertyImageType = 'primary' | 'additional' | 'floorplan' | 'document';

export interface PropertyImage {
  id?: string;
  property_id: string;
  image_url: string;
  image_cid?: string;
  thumbnail_url?: string;
  image_type: PropertyImageType;
  file_name: string;
  file_size: number;
  file_type: string;
  width?: number;
  height?: number;
  sort_order?: number;
  created_at?: string;
  updated_at?: string;
  optimized?: boolean;
}

// Types for KYC documents
export type KYCDocumentType = 'passport' | 'national_id' | 'drivers_license' | 'proof_of_address' | 'bank_statement' | 'other';
export type KYCDocumentCategory = 'identity' | 'address' | 'financial';
export type VerificationStatus = 'pending' | 'verified' | 'rejected' | 'expired';

export interface KYCDocument {
  id?: string;
  user_id: string;
  document_type: KYCDocumentType;
  document_category: KYCDocumentCategory;
  file_path: string;
  file_name: string;
  file_size: number;
  file_type: string;
  verification_status?: VerificationStatus;
  verification_notes?: string;
  verified_by?: string;
  verified_at?: string;
  expires_at?: string;
  created_at?: string;
  updated_at?: string;
}

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

/**
 * Upload a property image to storage
 *
 * This function uploads a property image to IPFS with Supabase as fallback,
 * and records the image metadata in the database. It also optimizes the image
 * and generates a thumbnail for faster loading.
 *
 * @param file File to upload
 * @param propertyId Property ID
 * @param imageType Type of image (primary, additional, etc.)
 * @param sortOrder Optional sort order for display
 * @param optimize Whether to optimize the image (default: true)
 * @param generateThumbnails Whether to generate thumbnails (default: true)
 * @returns Promise with upload result
 */
export async function uploadPropertyImage(
  file: File,
  propertyId: string,
  imageType: PropertyImageType = 'additional',
  sortOrder: number = 0,
  optimize: boolean = true,
  generateThumbnails: boolean = true
): Promise<PropertyImage> {
  try {
    // Validate file type
    if (!file.type.startsWith('image/') && file.type !== 'application/pdf') {
      throw new Error('Invalid file type. Only images and PDFs are allowed.');
    }

    // Validate file size (10MB max)
    const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
    if (file.size > MAX_FILE_SIZE) {
      throw new Error('File size exceeds the maximum allowed (10MB).');
    }

    let fileToUpload = file;
    let thumbnailBuffer: Buffer | null = null;
    let imageMetadata: any = {};
    let optimized = false;

    // Only process image files (not PDFs)
    if (file.type.startsWith('image/') && optimize) {
      try {
        // Extract metadata
        imageMetadata = await extractImageMetadata(file);

        // Optimize image based on type
        const optimizationOptions = {
          maxWidth: 1920,
          maxHeight: 1080,
          quality: 80,
          format: 'webp' as 'webp'
        };

        // For primary images, use higher quality
        if (imageType === 'primary') {
          optimizationOptions.quality = 90;
        }

        // Optimize the image
        const optimizedResult = await optimizeImage(file, optimizationOptions);

        // Create a new File from the optimized buffer
        fileToUpload = new File(
          [optimizedResult.buffer],
          file.name.replace(/\.[^/.]+$/, '.webp'),
          { type: 'image/webp' }
        );

        // Update metadata
        imageMetadata = optimizedResult.metadata;
        optimized = true;

        // Generate thumbnail if requested
        if (generateThumbnails) {
          thumbnailBuffer = await generateThumbnail(file);
        }
      } catch (optimizationError) {
        console.warn('Image optimization failed, using original image:', optimizationError);
        // Continue with original file if optimization fails
      }
    }

    // 1. Upload to IPFS
    const ipfsResult = await uploadToIPFS(fileToUpload);

    if (!ipfsResult.success) {
      throw new Error(`Failed to upload image: ${ipfsResult.error || 'Unknown error'}`);
    }

    // 2. Create Supabase client
    const supabase = createClient(supabaseUrl, supabaseAnonKey);

    // 3. Upload thumbnail if available
    let thumbnailUrl = '';
    if (thumbnailBuffer) {
      const timestamp = Date.now();
      const thumbnailFileName = `property-${propertyId}/thumbnails/${timestamp}-${file.name.replace(/\.[^/.]+$/, '.webp')}`;

      try {
        const { error: thumbnailError } = await supabase.storage
          .from('properties')
          .upload(thumbnailFileName, thumbnailBuffer, {
            contentType: 'image/webp',
            cacheControl: '3600',
            upsert: true
          });

        if (!thumbnailError) {
          const { data: urlData } = supabase.storage
            .from('properties')
            .getPublicUrl(thumbnailFileName);

          thumbnailUrl = urlData.publicUrl;
        }
      } catch (thumbnailError) {
        console.warn('Thumbnail upload failed:', thumbnailError);
        // Continue without thumbnail if upload fails
      }
    }

    // 4. Record in database
    const imageData: PropertyImage = {
      property_id: propertyId,
      image_url: ipfsResult.url!,
      image_cid: ipfsResult.cid,
      thumbnail_url: thumbnailUrl || undefined,
      image_type: imageType,
      file_name: fileToUpload.name,
      file_size: fileToUpload.size,
      file_type: fileToUpload.type,
      width: imageMetadata.width,
      height: imageMetadata.height,
      sort_order: sortOrder,
      optimized: optimized
    };

    const { data, error } = await supabase
      .from('property_images')
      .insert(imageData)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error uploading property image:', error);
    throw error;
  }
}

/**
 * Get all images for a property
 *
 * @param propertyId Property ID
 * @returns Promise with array of property images
 */
export async function getPropertyImages(propertyId: string): Promise<PropertyImage[]> {
  try {
    const supabase = createClient(supabaseUrl, supabaseAnonKey);

    const { data, error } = await supabase
      .from('property_images')
      .select('*')
      .eq('property_id', propertyId)
      .order('sort_order', { ascending: true });

    if (error) {
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error getting property images:', error);
    throw error;
  }
}

/**
 * Delete a property image
 *
 * @param imageId Image ID
 * @returns Promise with success status
 */
export async function deletePropertyImage(imageId: string): Promise<boolean> {
  try {
    const supabase = createClient(supabaseUrl, supabaseAnonKey);

    const { error } = await supabase
      .from('property_images')
      .delete()
      .eq('id', imageId);

    if (error) {
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Error deleting property image:', error);
    throw error;
  }
}

/**
 * Encrypt a file before upload (for sensitive KYC documents)
 *
 * This function uses AES encryption with a user-specific key to encrypt
 * sensitive documents before storing them. The encryption is done client-side
 * to ensure the unencrypted data never leaves the user's browser.
 *
 * @param file File to encrypt
 * @param encryptionKey Encryption key (user-specific)
 * @param saltLength Length of the random salt to use (default: 16)
 * @returns Promise with encrypted file
 */
export async function encryptFile(
  file: File,
  encryptionKey: string,
  saltLength: number = 16
): Promise<File> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = (event) => {
      try {
        if (!event.target || !event.target.result) {
          reject(new Error('Failed to read file'));
          return;
        }

        // Generate a random salt
        const salt = CryptoJS.lib.WordArray.random(saltLength);

        // Derive a stronger key using PBKDF2
        const key = CryptoJS.PBKDF2(
          encryptionKey,
          salt,
          { keySize: 256/32, iterations: 1000 }
        );

        // Generate a random IV
        const iv = CryptoJS.lib.WordArray.random(16);

        // Encrypt the file content
        const encrypted = CryptoJS.AES.encrypt(
          event.target.result.toString(),
          key,
          {
            iv: iv,
            padding: CryptoJS.pad.Pkcs7,
            mode: CryptoJS.mode.CBC
          }
        );

        // Combine salt + iv + encrypted data
        const encryptedWithMetadata = salt.toString() +
                                     iv.toString() +
                                     encrypted.toString();

        // Create a new file with encrypted content
        const encryptedFile = new File(
          [encryptedWithMetadata],
          `encrypted-${file.name}`,
          { type: 'application/encrypted' }
        );

        resolve(encryptedFile);
      } catch (error) {
        reject(error);
      }
    };

    reader.onerror = () => {
      reject(new Error('Failed to read file'));
    };

    reader.readAsDataURL(file);
  });
}

/**
 * Upload a KYC document to secure storage
 *
 * This function handles the secure upload of KYC documents with client-side
 * encryption for sensitive data. It validates the document, encrypts it if
 * requested, and stores it in a private storage bucket with appropriate
 * access controls.
 *
 * @param file File to upload
 * @param userId User ID
 * @param documentType Type of document
 * @param documentCategory Category of document
 * @param encrypt Whether to encrypt the document (default: true)
 * @param expirationDays Number of days until document expires (default: 365)
 * @returns Promise with upload result
 */
export async function uploadKYCDocument(
  file: File,
  userId: string,
  documentType: KYCDocumentType,
  documentCategory: KYCDocumentCategory,
  encrypt: boolean = true,
  expirationDays: number = 365
): Promise<KYCDocument> {
  try {
    // Validate file type
    const validTypes = [
      'image/jpeg',
      'image/png',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];

    if (!validTypes.includes(file.type)) {
      throw new Error(`Invalid file type. Allowed types: JPEG, PNG, PDF, DOC, DOCX`);
    }

    // Validate file size (10MB max)
    const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
    if (file.size > MAX_FILE_SIZE) {
      throw new Error(`File too large. Maximum size is 10MB`);
    }

    const supabase = createClient(supabaseUrl, supabaseAnonKey);

    // 1. Process the file (encrypt if needed)
    let fileToUpload = file;
    let isEncrypted = false;

    if (encrypt) {
      // Use user ID as part of the encryption key for added security
      // Add a timestamp to make the key more unique
      const encryptionKey = `${userId}-${Date.now()}-${process.env.NEXT_PUBLIC_ENCRYPTION_KEY || 'default-key'}`;
      fileToUpload = await encryptFile(file, encryptionKey);
      isEncrypted = true;
    }

    // 2. Create a unique file path with better organization
    const timestamp = Date.now();
    const safeFileName = file.name.replace(/[^a-zA-Z0-9.]/g, '_');
    const filePath = `kyc_documents/${userId}/${documentCategory}/${documentType}/${timestamp}-${safeFileName}`;

    // 3. Upload to Supabase Storage with retry logic
    let uploadAttempts = 0;
    const maxAttempts = 3;
    let uploadError = null;

    while (uploadAttempts < maxAttempts) {
      try {
        const { error } = await supabase.storage
          .from('kyc_documents')
          .upload(filePath, fileToUpload, {
            cacheControl: '0', // No caching for sensitive documents
            upsert: false, // Don't overwrite existing files
            contentType: isEncrypted ? 'application/encrypted' : file.type
          });

        if (!error) {
          uploadError = null;
          break; // Upload successful
        }

        uploadError = error;
        uploadAttempts++;

        // Wait before retrying (exponential backoff)
        if (uploadAttempts < maxAttempts) {
          await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, uploadAttempts - 1)));
        }
      } catch (err) {
        uploadError = err;
        uploadAttempts++;

        // Wait before retrying
        if (uploadAttempts < maxAttempts) {
          await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, uploadAttempts - 1)));
        }
      }
    }

    if (uploadError) {
      throw uploadError;
    }

    // 4. Calculate expiration date if provided
    let expiresAt = undefined;
    if (expirationDays > 0) {
      const expDate = new Date();
      expDate.setDate(expDate.getDate() + expirationDays);
      expiresAt = expDate.toISOString();
    }

    // 5. Record in database
    const documentData: KYCDocument = {
      user_id: userId,
      document_type: documentType,
      document_category: documentCategory,
      file_path: filePath,
      file_name: file.name,
      file_size: file.size,
      file_type: isEncrypted ? 'application/encrypted' : file.type,
      verification_status: 'pending',
      expires_at: expiresAt
    };

    const { data, error } = await supabase
      .from('kyc_documents')
      .insert(documentData)
      .select()
      .single();

    if (error) {
      // If database insert fails, try to clean up the uploaded file
      try {
        await supabase.storage
          .from('kyc_documents')
          .remove([filePath]);
      } catch (cleanupError) {
        console.warn('Failed to clean up uploaded file after database error:', cleanupError);
      }

      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error uploading KYC document:', error);
    throw error;
  }
}

/**
 * Get all KYC documents for a user
 *
 * @param userId User ID
 * @returns Promise with array of KYC documents
 */
export async function getUserKYCDocuments(userId: string): Promise<KYCDocument[]> {
  try {
    const supabase = createClient(supabaseUrl, supabaseAnonKey);

    const { data, error } = await supabase
      .from('kyc_documents')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      throw error;
    }

    // Check for expired documents and update their status
    const now = new Date();
    const updatedDocuments = data?.map(doc => {
      if (doc.expires_at && new Date(doc.expires_at) < now && doc.verification_status !== 'expired') {
        // Document has expired, update in database
        supabase
          .from('kyc_documents')
          .update({ verification_status: 'expired' })
          .eq('id', doc.id)
          .then(() => {
            console.log(`Document ${doc.id} marked as expired`);
          })
          .catch(err => {
            console.error(`Failed to mark document ${doc.id} as expired:`, err);
          });

        // Return updated document
        return { ...doc, verification_status: 'expired' };
      }
      return doc;
    });

    return updatedDocuments || [];
  } catch (error) {
    console.error('Error getting user KYC documents:', error);
    throw error;
  }
}

/**
 * Download and decrypt a KYC document
 *
 * This function downloads an encrypted KYC document and decrypts it for
 * authorized users (document owner or admin).
 *
 * @param documentId Document ID
 * @param userId User ID of the requester
 * @param encryptionKey Encryption key to use for decryption
 * @returns Promise with decrypted file as Blob
 */
export async function downloadKYCDocument(
  documentId: string,
  userId: string,
  encryptionKey: string
): Promise<Blob> {
  try {
    const supabase = createClient(supabaseUrl, supabaseAnonKey);

    // 1. Get document metadata
    const { data: document, error: docError } = await supabase
      .from('kyc_documents')
      .select('*')
      .eq('id', documentId)
      .single();

    if (docError) {
      throw docError;
    }

    // 2. Check authorization (only document owner or admin can download)
    if (document.user_id !== userId) {
      // Check if user is admin
      const { data: userProfile, error: profileError } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', userId)
        .single();

      if (profileError || userProfile.role !== 'admin') {
        throw new Error('Unauthorized access to document');
      }
    }

    // 3. Download encrypted file
    const { data: fileData, error: downloadError } = await supabase.storage
      .from('kyc_documents')
      .download(document.file_path);

    if (downloadError) {
      throw downloadError;
    }

    // 4. Check if file is encrypted
    if (document.file_type !== 'application/encrypted') {
      // Not encrypted, return as is
      return fileData;
    }

    // 5. Decrypt the file
    const encryptedText = await fileData.text();

    // Extract salt and IV from the encrypted data
    // First 32 chars are the salt (16 bytes in hex)
    // Next 32 chars are the IV (16 bytes in hex)
    const salt = CryptoJS.enc.Hex.parse(encryptedText.substring(0, 32));
    const iv = CryptoJS.enc.Hex.parse(encryptedText.substring(32, 64));
    const encryptedData = encryptedText.substring(64);

    // Derive the key using PBKDF2
    const key = CryptoJS.PBKDF2(
      encryptionKey,
      salt,
      { keySize: 256/32, iterations: 1000 }
    );

    // Decrypt the data
    const decrypted = CryptoJS.AES.decrypt(
      encryptedData,
      key,
      {
        iv: iv,
        padding: CryptoJS.pad.Pkcs7,
        mode: CryptoJS.mode.CBC
      }
    );

    // Convert to original format (was stored as base64 data URL)
    const decryptedText = decrypted.toString(CryptoJS.enc.Utf8);

    // Convert data URL back to Blob
    const dataUrlParts = decryptedText.split(',');
    const mimeType = dataUrlParts[0].match(/:(.*?);/)?.[1] || document.file_type;
    const base64Data = dataUrlParts[1];
    const binaryString = atob(base64Data);
    const bytes = new Uint8Array(binaryString.length);

    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }

    return new Blob([bytes], { type: mimeType });
  } catch (error) {
    console.error('Error downloading KYC document:', error);
    throw error;
  }
}

/**
 * Update the verification status of a KYC document
 *
 * This function allows admins to verify, reject, or mark documents as expired.
 *
 * @param documentId Document ID
 * @param adminId Admin user ID
 * @param status New verification status
 * @param notes Optional verification notes
 * @param expirationDays Optional days until expiration (from now)
 * @returns Promise with updated document
 */
export async function updateKYCDocumentVerification(
  documentId: string,
  adminId: string,
  status: VerificationStatus,
  notes?: string,
  expirationDays?: number
): Promise<KYCDocument> {
  try {
    const supabase = createClient(supabaseUrl, supabaseAnonKey);

    // 1. Check if user is admin
    const { data: adminProfile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', adminId)
      .single();

    if (profileError || adminProfile.role !== 'admin') {
      throw new Error('Unauthorized: Only admins can update verification status');
    }

    // 2. Calculate expiration date if provided
    let expiresAt = undefined;
    if (expirationDays && expirationDays > 0) {
      const expDate = new Date();
      expDate.setDate(expDate.getDate() + expirationDays);
      expiresAt = expDate.toISOString();
    }

    // 3. Update document verification status
    const updateData: Partial<KYCDocument> = {
      verification_status: status,
      verified_by: adminId,
      verified_at: new Date().toISOString()
    };

    if (notes) {
      updateData.verification_notes = notes;
    }

    if (expiresAt) {
      updateData.expires_at = expiresAt;
    }

    const { data, error } = await supabase
      .from('kyc_documents')
      .update(updateData)
      .eq('id', documentId)
      .select()
      .single();

    if (error) {
      throw error;
    }

    // 4. Log the verification action for audit purposes
    try {
      await supabase
        .from('audit_logs')
        .insert({
          action: `kyc_document_${status}`,
          user_id: adminId,
          target_id: documentId,
          target_type: 'kyc_document',
          details: {
            document_id: documentId,
            status,
            notes,
            expires_at: expiresAt
          }
        });
    } catch (logError) {
      console.warn('Failed to log verification action:', logError);
      // Continue even if logging fails
    }

    return data;
  } catch (error) {
    console.error('Error updating KYC document verification:', error);
    throw error;
  }
}

/**
 * Get pending KYC documents for admin verification
 *
 * @param adminId Admin user ID
 * @param limit Maximum number of documents to return
 * @param offset Pagination offset
 * @returns Promise with array of pending documents with user info
 */
export async function getPendingKYCDocuments(
  adminId: string,
  limit: number = 20,
  offset: number = 0
): Promise<any[]> {
  try {
    const supabase = createClient(supabaseUrl, supabaseAnonKey);

    // 1. Check if user is admin
    const { data: adminProfile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', adminId)
      .single();

    if (profileError || adminProfile.role !== 'admin') {
      throw new Error('Unauthorized: Only admins can view pending documents');
    }

    // 2. Get pending documents with user info
    const { data, error } = await supabase
      .from('kyc_documents')
      .select(`
        *,
        profiles:user_id (
          id,
          full_name,
          email
        )
      `)
      .eq('verification_status', 'pending')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error getting pending KYC documents:', error);
    throw error;
  }
}
