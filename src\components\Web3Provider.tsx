'use client';

import { ReactNode, useState, useEffect } from 'react';
import { WagmiProvider } from 'wagmi';
import { QueryClientProvider, QueryClient } from '@tanstack/react-query';
import { RainbowKitProvider, darkTheme } from '@rainbow-me/rainbowkit';
import { config } from '@/utils/web3';
import { WalletProvider } from '@/context/WalletContext';

// Import RainbowKit styles
import '@rainbow-me/rainbowkit/styles.css';

interface Web3ProviderProps {
  children: ReactNode;
}

/**
 * Web3Provider wraps the application with the necessary providers for wagmi,
 * RainbowKit, and React Query. This enables wallet connection and blockchain interactions.
 * It also includes the WalletProvider for centralized wallet state management.
 */
export default function Web3Provider({ children }: Web3ProviderProps) {
  // Create client only on the client side to avoid SSR issues
  const [queryClient] = useState(() => new QueryClient());
  const [mounted, setMounted] = useState(false);

  // Wait until after client-side hydration to mount
  useEffect(() => {
    setMounted(true);
  }, []);

  // Show loading state during SSR to avoid layout shift
  if (!mounted) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-white">Loading...</div>
      </div>
    );
  }

  return (
    <WagmiProvider config={config}>
      <QueryClientProvider client={queryClient}>
        <RainbowKitProvider
          theme={darkTheme({
            accentColor: '#6366f1', // indigo-500
            accentColorForeground: 'white',
            borderRadius: 'medium',
            fontStack: 'system',
            overlayBlur: 'small'
          })}
          modalSize="compact"
        >
          <WalletProvider>
            {children}
          </WalletProvider>
        </RainbowKitProvider>
      </QueryClientProvider>
    </WagmiProvider>
  );
}