# BrickChain File Storage System

This document provides an overview of the file storage system implemented in the BrickChain application.

## Overview

The file storage system consists of two main components:

1. **Property Image Storage**: For storing and managing property images
2. **Secure KYC Document Storage**: For storing and managing sensitive user verification documents

## Setup Instructions

### 1. Access the Setup Page

Navigate to the setup page in your dashboard:

```
/dashboard/setup-storage
```

This page provides a user-friendly interface to set up the necessary database tables and storage buckets.

### 2. Set Up Database Tables

Click the "Set Up Database Tables" button to create the following tables:

- `property_images`: For storing property image metadata
- `kyc_documents`: For storing KYC document metadata

### 3. Set Up Storage Buckets

Click the "Set Up Storage Buckets" button to create the following Supabase Storage buckets:

- `properties`: Public bucket for property images
- `kyc_documents`: Private bucket for KYC documents with row-level security

## Property Image Storage

### Features

- Multiple images per property
- Image categorization (primary, additional, floorplan, document)
- IPFS storage with Supabase fallback
- Image validation and optimization

### Usage

1. When editing a property, use the "Additional Property Images" section to upload multiple images
2. Select the image type (primary, additional, floorplan, document)
3. Images are automatically uploaded to IPFS with Supabase as fallback
4. Images are stored in the database with metadata for easy retrieval

### File Organization

```
properties/
  ├── {property_id}/
  │   ├── primary/
  │   │   ├── {timestamp}-{filename}.jpg  # Main property image
  │   ├── additional/
  │   │   ├── {timestamp}-{filename}.jpg  # Additional images
  │   ├── floorplans/
  │   │   ├── {timestamp}-{filename}.jpg  # Floor plans
  │   ├── documents/
  │   │   ├── {timestamp}-{filename}.pdf  # Public property documents
```

## KYC Document Storage

### Features

- Secure document upload with client-side encryption
- Document categorization (identity, address, financial)
- Document verification workflow
- Compliance with data protection regulations

### Usage

1. Navigate to the KYC page in your dashboard:
   ```
   /dashboard/kyc
   ```

2. Upload the required documents:
   - Identity documents (passport, national ID, driver's license)
   - Proof of address
   - Financial information

3. Documents are encrypted and securely stored
4. Admins can verify documents through the verification dashboard

### File Organization

```
kyc_documents/
  ├── {user_id}/
  │   ├── identity/
  │   │   ├── {timestamp}-{document_type}.{ext}  # ID cards, passports
  │   ├── address/
  │   │   ├── {timestamp}-{document_type}.{ext}  # Proof of address
  │   ├── financial/
  │   │   ├── {timestamp}-{document_type}.{ext}  # Financial documents
```

## Security Measures

### Property Images

- Public access for marketplace visibility
- Owner-only modification permissions
- Content validation to prevent malicious uploads

### KYC Documents

- Client-side encryption for sensitive documents
- Private storage bucket with row-level security
- User-specific access controls
- Admin-only verification permissions

## Technical Implementation

### Components

- `PropertyImageUploader`: Reusable component for multi-image upload
- `KYCDocumentUploader`: Secure component for document upload
- API routes for secure server-side operations

### Database Schema

#### Property Images Table

```sql
CREATE TABLE property_images (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  property_id UUID REFERENCES properties(id) ON DELETE CASCADE,
  image_url TEXT NOT NULL,
  image_cid TEXT,
  image_type VARCHAR(50) NOT NULL,
  file_name TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  file_type TEXT NOT NULL,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
```

#### KYC Documents Table

```sql
CREATE TABLE kyc_documents (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  document_type kyc_document_type NOT NULL,
  document_category VARCHAR(50) NOT NULL,
  file_path TEXT NOT NULL,
  file_name TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  file_type TEXT NOT NULL,
  verification_status verification_status DEFAULT 'pending',
  verification_notes TEXT,
  verified_by UUID REFERENCES auth.users(id),
  verified_at TIMESTAMP WITH TIME ZONE,
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
```

## Admin Verification

Admins can verify KYC documents through the admin verification dashboard:

```
/dashboard/admin/kyc-verification
```

This dashboard allows admins to:

1. View pending documents
2. Review document details
3. Approve or reject documents
4. Add verification notes

## Troubleshooting

### Common Issues

1. **Storage Bucket Creation Fails**
   - Ensure you have the necessary permissions in Supabase
   - Check if the buckets already exist

2. **Database Table Creation Fails**
   - Ensure the SQL execution function is available
   - Check if the tables already exist

3. **Image Upload Fails**
   - Check your IPFS configuration
   - Ensure the Supabase storage bucket exists
   - Verify file size and type restrictions

4. **KYC Document Upload Fails**
   - Check encryption settings
   - Ensure the user is authenticated
   - Verify file size and type restrictions

For additional help, please contact the BrickChain support team.
