'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import { useUser } from './UserContext';

// Define common routes that should be preloaded
const commonRoutes = [
  '/',
  '/marketplace',
  '/settings',
  '/how-it-works',
  '/about',
];

// Define role-specific routes
const roleRoutes = {
  owner: [
    '/dashboard/owner',
    '/dashboard/owner/properties',
    '/dashboard/owner/investments',
    '/dashboard/owner/analytics',
  ],
  investor: [
    '/dashboard/investor',
    '/dashboard/investor/portfolio',
    '/dashboard/investor/opportunities',
    '/dashboard/investor/history',
  ],
  admin: [
    '/dashboard/admin',
    '/dashboard/admin/users',
    '/dashboard/admin/properties',
    '/dashboard/admin/analytics',
  ],
};

interface NavigationContextType {
  preloadRoute: (route: string) => Promise<void>;
  preloadedRoutes: Set<string>;
}

const NavigationContext = createContext<NavigationContextType>({
  preloadRoute: async () => {},
  preloadedRoutes: new Set(),
});

export const useNavigation = () => useContext(NavigationContext);

export function NavigationProvider({ children }: { children: ReactNode }) {
  const router = useRouter();
  const { user } = useUser();
  const [preloadedRoutes, setPreloadedRoutes] = useState<Set<string>>(new Set());

  // Preload a specific route
  const preloadRoute = async (route: string) => {
    if (preloadedRoutes.has(route)) return;

    try {
      await router.prefetch(route);
      setPreloadedRoutes(prev => {
        const newSet = new Set(prev);
        newSet.add(route);
        return newSet;
      });
    } catch (error) {
      console.error(`Failed to preload route: ${route}`, error);
    }
  };

  // Preload common routes when the component mounts
  useEffect(() => {
    const preloadCommonRoutes = async () => {
      for (const route of commonRoutes) {
        await preloadRoute(route);
      }
    };

    preloadCommonRoutes();
  }, []);

  // Preload role-specific routes when the user changes
  useEffect(() => {
    if (!user || !user.role) return;

    const preloadRoleRoutes = async () => {
      const routes = roleRoutes[user.role] || [];
      for (const route of routes) {
        await preloadRoute(route);
      }
    };

    preloadRoleRoutes();
  }, [user]);

  return (
    <NavigationContext.Provider value={{ preloadRoute, preloadedRoutes }}>
      {children}
    </NavigationContext.Provider>
  );
}
