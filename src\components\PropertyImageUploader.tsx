import React, { useState, useEffect, useCallback } from 'react';
import { useSupabaseClient } from '@supabase/auth-helpers-react';
import { Building, Upload, X, Loader2, AlertCircle, Cloud, Image as ImageIcon, FileText } from 'lucide-react';
import { uploadPropertyImage, getPropertyImages, deletePropertyImage, type PropertyImage, type PropertyImageType } from '@/utils/fileStorage';

interface PropertyImageUploaderProps {
  propertyId: string;
  onImagesChange?: (images: PropertyImage[]) => void;
  maxImages?: number;
  allowedTypes?: PropertyImageType[];
  theme?: 'light' | 'dark';
}

export default function PropertyImageUploader({
  propertyId,
  onImagesChange,
  maxImages = 10,
  allowedTypes = ['primary', 'additional', 'floorplan', 'document'],
  theme = 'light'
}: PropertyImageUploaderProps) {
  const [images, setImages] = useState<PropertyImage[]>([]);
  const [uploading, setUploading] = useState<boolean>(false);
  const [error, setError] = useState<string>('');
  const [uploadType, setUploadType] = useState<PropertyImageType>('additional');
  const [dragActive, setDragActive] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);

  // Fetch existing images on component mount
  useEffect(() => {
    const fetchImages = async () => {
      try {
        setLoading(true);
        const fetchedImages = await getPropertyImages(propertyId);
        setImages(fetchedImages);
        if (onImagesChange) {
          onImagesChange(fetchedImages);
        }
      } catch (err) {
        console.error('Error fetching property images:', err);
        setError('Failed to load existing images');
      } finally {
        setLoading(false);
      }
    };

    if (propertyId) {
      fetchImages();
    }
  }, [propertyId, onImagesChange]);

  // Handle file selection
  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;
    
    await uploadFiles(Array.from(files));
    
    // Clear the input value so the same file can be uploaded again if needed
    e.target.value = '';
  };

  // Handle drag events
  const handleDrag = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  // Handle drop event
  const handleDrop = useCallback(async (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      await uploadFiles(Array.from(e.dataTransfer.files));
    }
  }, []);

  // Upload files
  const uploadFiles = async (files: File[]) => {
    // Check if adding these files would exceed the maximum
    if (images.length + files.length > maxImages) {
      setError(`You can only upload a maximum of ${maxImages} images`);
      return;
    }
    
    // Validate file types
    const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'application/pdf'];
    const invalidFiles = files.filter(file => !validTypes.includes(file.type));
    
    if (invalidFiles.length > 0) {
      setError(`Invalid file type(s). Allowed types: JPEG, PNG, GIF, WEBP, PDF`);
      return;
    }
    
    // Validate file sizes (10MB max)
    const maxSize = 10 * 1024 * 1024; // 10MB
    const oversizedFiles = files.filter(file => file.size > maxSize);
    
    if (oversizedFiles.length > 0) {
      setError(`File(s) too large. Maximum size is 10MB per file`);
      return;
    }
    
    setUploading(true);
    setError('');
    
    try {
      // Upload each file
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        
        // Determine if this should be the primary image
        const isPrimary = images.length === 0 && i === 0 && !images.some(img => img.image_type === 'primary');
        const imageType = isPrimary ? 'primary' : uploadType;
        
        // Calculate sort order (primary = 0, others are incremented)
        const sortOrder = isPrimary ? 0 : Math.max(0, ...images.map(img => img.sort_order || 0)) + 1;
        
        // Upload the image
        const uploadedImage = await uploadPropertyImage(file, propertyId, imageType, sortOrder);
        
        // Update state
        setImages(prev => [...prev, uploadedImage]);
        if (onImagesChange) {
          onImagesChange([...images, uploadedImage]);
        }
      }
    } catch (err) {
      console.error('Error uploading images:', err);
      setError('Failed to upload one or more images');
    } finally {
      setUploading(false);
    }
  };

  // Delete an image
  const handleDeleteImage = async (imageId: string) => {
    if (!confirm('Are you sure you want to delete this image?')) return;
    
    try {
      await deletePropertyImage(imageId);
      const updatedImages = images.filter(img => img.id !== imageId);
      setImages(updatedImages);
      if (onImagesChange) {
        onImagesChange(updatedImages);
      }
    } catch (err) {
      console.error('Error deleting image:', err);
      setError('Failed to delete image');
    }
  };

  // Get icon based on image type
  const getImageTypeIcon = (type: PropertyImageType) => {
    switch (type) {
      case 'primary':
        return <Building size={16} />;
      case 'floorplan':
        return <FileText size={16} />;
      case 'document':
        return <FileText size={16} />;
      default:
        return <ImageIcon size={16} />;
    }
  };

  return (
    <div className={`w-full ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>
      {/* Error message */}
      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded flex items-center">
          <AlertCircle size={16} className="mr-2" />
          <span>{error}</span>
          <button 
            onClick={() => setError('')}
            className="ml-auto text-red-700 hover:text-red-900"
          >
            <X size={16} />
          </button>
        </div>
      )}
      
      {/* Image type selector */}
      <div className="mb-4">
        <label className={`block text-sm font-medium ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'} mb-1`}>
          Image Type
        </label>
        <select
          value={uploadType}
          onChange={(e) => setUploadType(e.target.value as PropertyImageType)}
          className={`w-full p-2 border rounded ${theme === 'dark' ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-300'}`}
          disabled={uploading}
        >
          {allowedTypes.map(type => (
            <option key={type} value={type}>
              {type.charAt(0).toUpperCase() + type.slice(1)}
            </option>
          ))}
        </select>
      </div>
      
      {/* Upload area */}
      <div
        className={`border-2 border-dashed rounded-lg p-6 text-center ${
          dragActive 
            ? 'border-indigo-500 bg-indigo-50' 
            : theme === 'dark' 
              ? 'border-gray-700 bg-gray-800' 
              : 'border-gray-300 bg-gray-50'
        }`}
        onDragEnter={handleDrag}
        onDragOver={handleDrag}
        onDragLeave={handleDrag}
        onDrop={handleDrop}
      >
        <div className="space-y-2 flex flex-col items-center justify-center">
          <Upload size={36} className={theme === 'dark' ? 'text-gray-400' : 'text-gray-500'} />
          <div className="text-sm">
            <label
              htmlFor="property-image-upload"
              className={`cursor-pointer font-medium ${theme === 'dark' ? 'text-indigo-400' : 'text-indigo-600'} hover:underline`}
            >
              <span>Click to upload</span>
              <input
                id="property-image-upload"
                name="property-image-upload"
                type="file"
                className="sr-only"
                accept="image/*,application/pdf"
                onChange={handleFileChange}
                multiple
                disabled={uploading}
              />
            </label>
            <span className={theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}> or drag and drop</span>
          </div>
          <p className={`text-xs ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
            PNG, JPG, GIF, WEBP, PDF up to 10MB
          </p>
          {uploading && (
            <div className="flex items-center mt-2">
              <Loader2 className="animate-spin h-4 w-4 mr-2 text-indigo-500" />
              <span className={`text-sm ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
                Uploading...
              </span>
            </div>
          )}
        </div>
      </div>
      
      {/* Image preview grid */}
      {loading ? (
        <div className="mt-4 flex justify-center">
          <Loader2 className="animate-spin h-6 w-6 text-indigo-500" />
        </div>
      ) : images.length > 0 ? (
        <div className="mt-4 grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
          {images.map((image) => (
            <div 
              key={image.id} 
              className={`relative group rounded-lg overflow-hidden border ${
                theme === 'dark' ? 'border-gray-700' : 'border-gray-200'
              }`}
            >
              {/* Image preview */}
              {image.file_type.startsWith('image/') ? (
                <img
                  src={image.image_url}
                  alt={image.file_name}
                  className="w-full h-32 object-cover"
                />
              ) : (
                <div className="w-full h-32 flex items-center justify-center bg-gray-100">
                  <FileText size={32} className="text-gray-400" />
                </div>
              )}
              
              {/* Image info overlay */}
              <div className={`absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-opacity flex flex-col justify-between p-2`}>
                <div className="invisible group-hover:visible">
                  <span className={`inline-flex items-center px-2 py-1 rounded text-xs ${
                    theme === 'dark' ? 'bg-gray-800 text-white' : 'bg-white text-gray-800'
                  }`}>
                    {getImageTypeIcon(image.image_type)}
                    <span className="ml-1">{image.image_type}</span>
                  </span>
                </div>
                <div className="invisible group-hover:visible flex justify-between">
                  <button
                    onClick={() => window.open(image.image_url, '_blank')}
                    className="p-1 rounded bg-blue-500 text-white hover:bg-blue-600"
                  >
                    <ImageIcon size={16} />
                  </button>
                  <button
                    onClick={() => handleDeleteImage(image.id!)}
                    className="p-1 rounded bg-red-500 text-white hover:bg-red-600"
                  >
                    <X size={16} />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <p className={`mt-4 text-center ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
          No images uploaded yet
        </p>
      )}
    </div>
  );
}
