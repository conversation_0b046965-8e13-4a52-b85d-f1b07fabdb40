const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("BrickChain", function () {
  let propertyToken;
  let propertyRegistry;
  let investmentManager;
  let marketplace;
  let owner;
  let investor1;
  let investor2;
  let propertyId;

  beforeEach(async function () {
    // Get signers
    [owner, investor1, investor2] = await ethers.getSigners();

    // Deploy PropertyToken
    const PropertyToken = await ethers.getContractFactory("PropertyToken");
    propertyToken = await PropertyToken.deploy();

    // Deploy PropertyRegistry
    const PropertyRegistry = await ethers.getContractFactory("PropertyRegistry");
    propertyRegistry = await PropertyRegistry.deploy(await propertyToken.getAddress());

    // Deploy InvestmentManager
    const InvestmentManager = await ethers.getContractFactory("InvestmentManager");
    investmentManager = await InvestmentManager.deploy(
      await propertyToken.getAddress(),
      await propertyRegistry.getAddress()
    );

    // Deploy Marketplace
    const Marketplace = await ethers.getContractFactory("Marketplace");
    marketplace = await Marketplace.deploy(
      await propertyToken.getAddress(),
      await propertyRegistry.getAddress()
    );

    // Set registry address in PropertyToken
    await propertyToken.setRegistryAddress(await propertyRegistry.getAddress());

    // Register a property
    const tx = await propertyRegistry.registerProperty(
      "Luxury Apartment",
      "New York, NY",
      500000, // $5,000.00
      800, // 8.00%
      1000, // 1000 shares
      "ipfs://QmXyZ123..."
    );
    
    const receipt = await tx.wait();
    const event = receipt.logs.find(log => 
      log.fragment && log.fragment.name === 'PropertyRegistered'
    );
    
    if (event) {
      propertyId = event.args[0];
    } else {
      propertyId = 1; // Fallback if event not found
    }
  });

  describe("Property Registration", function () {
    it("Should register a property correctly", async function () {
      const property = await propertyRegistry.getProperty(propertyId);
      expect(property.name).to.equal("Luxury Apartment");
      expect(property.location).to.equal("New York, NY");
      expect(property.price).to.equal(500000);
      expect(property.returnRate).to.equal(800);
      expect(property.totalShares).to.equal(1000);
      expect(property.status).to.equal("available");
    });

    it("Should create property tokens when registering", async function () {
      const totalShares = await propertyToken.getTotalShares(propertyId);
      expect(totalShares).to.equal(1000);
    });
  });

  describe("Investment", function () {
    it("Should allow investment in a property", async function () {
      // Calculate investment amount (price per share * shares)
      const sharePrice = 500000 / 1000; // $500 per share
      const shares = 10;
      const investmentAmount = sharePrice * shares;

      // Invest in the property
      await investmentManager.connect(investor1).invest(propertyId, shares, {
        value: ethers.parseEther((investmentAmount / 100).toString()) // Convert to ETH
      });

      // Check investor's token balance
      const balance = await propertyToken.balanceOf(investor1.address, propertyId);
      expect(balance).to.equal(shares);
    });
  });

  describe("Marketplace", function () {
    it("Should allow creating and purchasing a listing", async function () {
      // First, invest to get some tokens
      const sharePrice = 500000 / 1000; // $500 per share
      const shares = 20;
      const investmentAmount = sharePrice * shares;

      await investmentManager.connect(investor1).invest(propertyId, shares, {
        value: ethers.parseEther((investmentAmount / 100).toString())
      });

      // Approve marketplace to transfer tokens
      await propertyToken.connect(investor1).setApprovalForAll(
        await marketplace.getAddress(),
        true
      );

      // Create a listing
      const listingShares = 10;
      const listingPricePerShare = ethers.parseEther("0.01"); // 0.01 ETH per share
      
      const tx = await marketplace.connect(investor1).createListing(
        propertyId,
        listingShares,
        listingPricePerShare
      );
      
      const receipt = await tx.wait();
      const event = receipt.logs.find(log => 
        log.fragment && log.fragment.name === 'ListingCreated'
      );
      
      let listingId;
      if (event) {
        listingId = event.args[0];
      } else {
        listingId = 1; // Fallback if event not found
      }

      // Purchase the listing
      const purchaseShares = 5;
      await marketplace.connect(investor2).purchaseListing(
        listingId,
        purchaseShares,
        {
          value: listingPricePerShare * BigInt(purchaseShares)
        }
      );

      // Check balances
      const investor1Balance = await propertyToken.balanceOf(investor1.address, propertyId);
      const investor2Balance = await propertyToken.balanceOf(investor2.address, propertyId);
      
      expect(investor1Balance).to.equal(shares - listingShares);
      expect(investor2Balance).to.equal(purchaseShares);
    });
  });
});
