'use client';
import React, { useState, useEffect } from 'react';
import { createClient } from '@supabase/supabase-js';
import { Building, Search, Filter, Plus, Edit, Trash2, Eye, AlertTriangle, Check, X } from 'lucide-react';
// No longer need to import DashboardWrapper
import { useUser } from '../../../../context/UserContext';
import LoadingSpinner from '@/components/LoadingSpinner';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';
const supabase = createClient(supabaseUrl, supabaseAnonKey);

type Property = {
  id: string;
  name: string;
  location: string;
  price: number;
  status: 'available' | 'funded' | 'sold' | 'pending';
  image_url?: string;
  owner_id: string;
  created_at: string;
  owner_name?: string;
  total_investments?: number;
  return_rate?: number;
};

export default function AdminPropertiesPage() {
  const { user } = useUser();
  const [properties, setProperties] = useState<Property[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [error, setError] = useState('');
  const [selectedProperty, setSelectedProperty] = useState<Property | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Define secondary nav for admin dashboard
  const secondaryNav = [
    { label: 'Dashboard', href: '/dashboard/admin' },
    { label: 'Users', href: '/dashboard/admin/users' },
    { label: 'Properties', href: '/dashboard/admin/properties' },
    { label: 'Transactions', href: '/dashboard/admin/transactions' },
  ];

  useEffect(() => {
    const fetchProperties = async () => {
      if (!user || user.role !== 'admin') return;

      try {
        setLoading(true);

        // Get properties with owner information and investment totals
        const { data, error } = await supabase
          .from('properties')
          .select(`
            *,
            profiles:owner_id(full_name),
            investments(amount)
          `);

        if (error) throw error;

        // Format property data
        const formattedProperties = (data || []).map((property: any) => ({
          id: property.id,
          name: property.name,
          location: property.location,
          price: property.price,
          status: property.status,
          image_url: property.image_url,
          owner_id: property.owner_id,
          created_at: property.created_at,
          owner_name: property.profiles?.full_name || 'Unknown Owner',
          total_investments: property.investments?.reduce((sum: number, inv: any) => sum + (inv.amount || 0), 0) || 0,
          return_rate: property.return_rate
        }));

        setProperties(formattedProperties);
      } catch (err) {
        console.error('Error fetching properties:', err);
        setError('Failed to load properties');
      } finally {
        setLoading(false);
      }
    };

    fetchProperties();
  }, [user]);

  // Filter properties based on search term and status filter
  const filteredProperties = properties.filter(property => {
    const matchesSearch = searchTerm === '' ||
      property.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      property.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
      property.owner_name?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' || property.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  const handleDeleteProperty = async (propertyId: string) => {
    if (!confirm('Are you sure you want to delete this property? This action cannot be undone.')) return;

    try {
      const { error } = await supabase
        .from('properties')
        .delete()
        .eq('id', propertyId);

      if (error) throw error;

      // Update local state
      setProperties(properties.filter(p => p.id !== propertyId));
    } catch (err) {
      console.error('Error deleting property:', err);
      setError('Failed to delete property');
    }
  };

  const handleUpdateStatus = async (propertyId: string, newStatus: 'available' | 'funded' | 'sold' | 'pending') => {
    try {
      const { error } = await supabase
        .from('properties')
        .update({ status: newStatus })
        .eq('id', propertyId);

      if (error) throw error;

      // Update local state
      setProperties(properties.map(p =>
        p.id === propertyId ? { ...p, status: newStatus } : p
      ));
    } catch (err) {
      console.error('Error updating property status:', err);
      setError('Failed to update property status');
    }
  };

  if (!user || user.role !== 'admin') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="bg-red-900/40 border border-red-800 rounded-lg p-6 max-w-md">
          <div className="flex items-center space-x-3 mb-4">
            <AlertTriangle size={24} className="text-red-400" />
            <h2 className="text-xl font-bold text-white">Access Denied</h2>
          </div>
          <p className="text-red-300">You do not have permission to access the admin properties management page.</p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-red-500">{error}</div>
      </div>
    );
  }

  return (
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex-1">
            <h2 className="text-xl md:text-2xl font-bold text-white">Property Management</h2>
            <p className="text-sm md:text-base text-gray-400">Manage all properties listed on the platform</p>
          </div>

          {/* Search and Filter */}
          <div className="flex flex-col sm:flex-row gap-3">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search size={18} className="text-gray-500" />
              </div>
              <input
                type="text"
                placeholder="Search properties..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 bg-indigo-900/30 border border-indigo-800 rounded-lg text-white w-full text-sm md:text-base focus:ring-2 focus:ring-indigo-500"
              />
            </div>

            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Filter size={18} className="text-gray-500" />
              </div>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="pl-10 pr-4 py-2 bg-indigo-900/30 border border-indigo-800 rounded-lg text-white text-sm md:text-base focus:ring-2 focus:ring-indigo-500 appearance-none"
              >
                <option value="all">All Status</option>
                <option value="available">Available</option>
                <option value="funded">Funded</option>
                <option value="sold">Sold</option>
                <option value="pending">Pending</option>
              </select>
            </div>
          </div>
        </div>

        {/* Properties Table */}
        <div className="bg-indigo-900/30 border border-indigo-800 rounded-xl overflow-hidden">
          {loading ? (
            <div className="p-4 md:p-6 space-y-4">
              {[1, 2, 3, 4].map((_, index) => (
                <div key={index} className="animate-pulse flex items-center space-x-4">
                  <div className="w-16 h-16 bg-indigo-800/40 rounded-lg"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-indigo-800/40 rounded w-1/4"></div>
                    <div className="h-3 bg-indigo-800/40 rounded w-1/3"></div>
                  </div>
                  <div className="h-6 bg-indigo-800/40 rounded w-16"></div>
                </div>
              ))}
            </div>
          ) : filteredProperties.length === 0 ? (
            <div className="p-8 text-center">
              <Building size={48} className="mx-auto text-indigo-600/40 mb-4" />
              <h3 className="text-lg md:text-xl font-semibold text-white mb-2">No Properties Found</h3>
              <p className="text-sm md:text-base text-gray-400">
                {searchTerm || statusFilter !== 'all'
                  ? 'Try adjusting your search or filter criteria'
                  : 'There are no properties listed on the platform yet'}
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <div className="min-w-full divide-y divide-indigo-800/30">
                {filteredProperties.map((property) => (
                  <div key={property.id} className="p-4 md:p-6 hover:bg-indigo-800/10">
                    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                      <div className="flex items-center space-x-4">
                        <div className="w-16 h-16 rounded-lg bg-indigo-800/20 overflow-hidden flex-shrink-0">
                          {property.image_url ? (
                            <img
                              src={property.image_url}
                              alt={property.name}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <Building size={24} className="m-auto text-indigo-400" />
                          )}
                        </div>
                        <div>
                          <h4 className="font-medium text-white">{property.name}</h4>
                          <p className="text-sm text-gray-400">{property.location}</p>
                        </div>
                      </div>
                      <div className="flex flex-col sm:items-end gap-2">
                        <div className="flex flex-col sm:items-end gap-1">
                          <span className="font-medium text-white">
                            ${property.price.toLocaleString()}
                          </span>
                          <span className="text-sm text-gray-400">{property.owner_name}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="relative group">
                            <span className={`px-2.5 py-1 rounded-full text-xs font-medium inline-flex items-center
                              ${property.status === 'available' ? 'bg-green-900/40 text-green-300' :
                                property.status === 'funded' ? 'bg-blue-900/40 text-blue-300' :
                                property.status === 'sold' ? 'bg-red-900/40 text-red-300' :
                                'bg-purple-900/40 text-purple-300'}`}
                            >
                              {property.status.charAt(0).toUpperCase() + property.status.slice(1)}
                            </span>

                            {/* Status change dropdown */}
                            <div className="absolute left-0 mt-2 w-36 bg-indigo-950 border border-indigo-800 rounded-lg shadow-lg z-10 hidden group-hover:block">
                              <div className="p-1">
                                <button
                                  onClick={() => handleUpdateStatus(property.id, 'available')}
                                  className="w-full text-left px-3 py-2 text-sm rounded-md hover:bg-indigo-800/50 text-white flex items-center space-x-2"
                                >
                                  <span>Available</span>
                                  {property.status === 'available' && <Check size={14} className="ml-auto text-green-400" />}
                                </button>
                                <button
                                  onClick={() => handleUpdateStatus(property.id, 'funded')}
                                  className="w-full text-left px-3 py-2 text-sm rounded-md hover:bg-indigo-800/50 text-white flex items-center space-x-2"
                                >
                                  <span>Funded</span>
                                  {property.status === 'funded' && <Check size={14} className="ml-auto text-green-400" />}
                                </button>
                                <button
                                  onClick={() => handleUpdateStatus(property.id, 'sold')}
                                  className="w-full text-left px-3 py-2 text-sm rounded-md hover:bg-indigo-800/50 text-white flex items-center space-x-2"
                                >
                                  <span>Sold</span>
                                  {property.status === 'sold' && <Check size={14} className="ml-auto text-green-400" />}
                                </button>
                              </div>
                            </div>
                          </div>
                          <div className="flex space-x-2">
                            <button
                              onClick={() => setSelectedProperty(property)}
                              className="p-1.5 bg-blue-800/40 hover:bg-blue-700/60 rounded text-blue-300"
                              title="View Details"
                            >
                              <Eye size={16} />
                            </button>
                            <button
                              className="p-1.5 bg-amber-800/40 hover:bg-amber-700/60 rounded text-amber-300"
                              title="Edit Property"
                            >
                              <Edit size={16} />
                            </button>
                            <button
                              onClick={() => handleDeleteProperty(property.id)}
                              className="p-1.5 bg-red-800/40 hover:bg-red-700/60 rounded text-red-300"
                              title="Delete Property"
                            >
                              <Trash2 size={16} />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Pagination controls */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 text-sm text-gray-400">
          <div>
            Showing {filteredProperties.length} of {properties.length} properties
          </div>
          <div className="flex space-x-2">
            <button className="px-3 py-1 border border-indigo-800 rounded-md hover:bg-indigo-800/50">
              Previous
            </button>
            <button className="px-3 py-1 border border-indigo-800 rounded-md bg-indigo-700 text-white">
              1
            </button>
            <button className="px-3 py-1 border border-indigo-800 rounded-md hover:bg-indigo-800/50">
              Next
            </button>
          </div>
        </div>
      </div>

      {/* Property Details Modal */}
      {selectedProperty && (
        <div className="fixed inset-0 z-50 overflow-y-auto bg-black bg-opacity-75 flex items-center justify-center p-4">
          <div className="bg-indigo-900 rounded-xl p-4 md:p-6 max-w-2xl w-full">
            <div className="flex justify-between items-start mb-4">
              <h3 className="text-lg md:text-xl font-bold text-white">{selectedProperty.name}</h3>
              <button
                onClick={() => setSelectedProperty(null)}
                className="text-gray-400 hover:text-white"
              >
                <X size={24} />
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
              <div>
                {selectedProperty.image_url ? (
                  <img
                    src={selectedProperty.image_url}
                    alt={selectedProperty.name}
                    className="w-full rounded-lg object-cover h-48"
                  />
                ) : (
                  <div className="w-full h-48 bg-indigo-800/20 flex items-center justify-center rounded-lg">
                    <Building size={48} className="text-indigo-600/40" />
                  </div>
                )}

                <div className="mt-4 space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm md:text-base text-gray-400">Location:</span>
                    <span className="text-sm md:text-base text-white">{selectedProperty.location}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm md:text-base text-gray-400">Price:</span>
                    <span className="text-sm md:text-base text-white">${selectedProperty.price.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm md:text-base text-gray-400">Status:</span>
                    <span className={`text-sm md:text-base ${
                      selectedProperty.status === 'available' ? 'text-green-400' :
                      selectedProperty.status === 'funded' ? 'text-blue-400' :
                      selectedProperty.status === 'sold' ? 'text-red-400' :
                      'text-purple-400'
                    }`}>
                      {selectedProperty.status.charAt(0).toUpperCase() + selectedProperty.status.slice(1)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm md:text-base text-gray-400">Owner:</span>
                    <span className="text-sm md:text-base text-white">{selectedProperty.owner_name}</span>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="text-base md:text-lg font-medium text-white mb-4">Investment Summary</h4>
                <div className="bg-indigo-800/30 rounded-lg p-4 space-y-4">
                  <div className="flex justify-between">
                    <span className="text-sm md:text-base text-gray-400">Total Investments:</span>
                    <span className="text-sm md:text-base text-white">
                      ${selectedProperty.total_investments?.toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm md:text-base text-gray-400">Return Rate:</span>
                    <span className="text-sm md:text-base text-white">
                      {selectedProperty.return_rate ? `${selectedProperty.return_rate}%` : 'Not set'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm md:text-base text-gray-400">Listed On:</span>
                    <span className="text-sm md:text-base text-white">
                      {new Date(selectedProperty.created_at).toLocaleDateString()}
                    </span>
                  </div>
                </div>

                <div className="mt-6 flex justify-end space-x-4">
                  <button
                    className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 rounded-lg text-white text-sm md:text-base"
                  >
                    Edit Property
                  </button>
                  <button
                    onClick={() => setSelectedProperty(null)}
                    className="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg text-white text-sm md:text-base"
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
  );
}