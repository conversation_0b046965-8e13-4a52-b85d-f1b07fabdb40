'use client';

import { useState, useEffect } from 'react';
import { useBalance } from 'wagmi';
import { Wallet, Building, Grid3X3, AlertCircle, Loader2, ArrowUpRight, Package, BarChart3 } from 'lucide-react';
import ConnectWalletButton from '@/components/ConnectWalletButton';
import Link from 'next/link';
import { getBlockExplorerUrl } from '@/utils/web3';
import { useWallet } from '@/context/WalletContext';

// Token type definition
interface TokenBalance {
  type: 'ERC20' | 'ERC721' | 'ERC1155';
  name: string;
  symbol?: string;
  decimals?: number;
  balance: string;
  formattedBalance?: string;
  address: string;
  icon?: string;
  lastUpdated?: number;
}

// Property token type (used for both ERC721 full ownership and ERC1155 fractional)
interface PropertyToken {
  id: string;
  type: 'full' | 'fractional';
  tokenId: string;
  propertyName: string;
  location: string;
  imageUrl: string;
  value: number;
  shares?: number;
  totalShares?: number;
  ownershipPercentage?: number;
  contractAddress: string;
}

export default function TokenDashboard() {
  // Use the centralized wallet context instead of direct wagmi hooks
  const { address, isConnected, chainId } = useWallet();
  const [activeTab, setActiveTab] = useState<'overview' | 'properties' | 'transactions'>('overview');
  const [loading, setLoading] = useState(true);
  const [tokens, setTokens] = useState<TokenBalance[]>([]);
  const [properties, setProperties] = useState<PropertyToken[]>([]);

  // Fetch native token balance using wagmi hook
  const { data: ethBalance } = useBalance({
    address,
  });

  // Mock data loading
  useEffect(() => {
    if (isConnected) {
      // Simulate loading data from blockchain/backend
      setTimeout(() => {
        // Mock tokens data
        const mockTokens: TokenBalance[] = [
          {
            type: 'ERC20',
            name: 'BrickChain Token',
            symbol: 'BCT',
            decimals: 18,
            balance: '1000000000000000000000', // 1000 BCT (with 18 decimals)
            formattedBalance: '1,000 BCT',
            address: '******************************************',
            icon: '/assets/bct-logo.png',
            lastUpdated: Date.now(),
          },
          {
            type: 'ERC20',
            name: 'USD Coin',
            symbol: 'USDC',
            decimals: 6,
            balance: '25000000', // 25 USDC (with 6 decimals)
            formattedBalance: '25 USDC',
            address: '******************************************',
            lastUpdated: Date.now(),
          }
        ];

        // Mock property tokens
        const mockProperties: PropertyToken[] = [
          {
            id: '1',
            type: 'full',
            tokenId: '42',
            propertyName: 'Downtown Duplex',
            location: 'Accra, Ghana',
            imageUrl: '/assets/House1.jpg',
            value: 120000,
            contractAddress: '0x2345678901234567890123456789012345678901',
          },
          {
            id: '2',
            type: 'fractional',
            tokenId: '17',
            propertyName: 'Beachfront Villa',
            location: 'Lagos, Nigeria',
            imageUrl: '/assets/House2.jpg',
            value: 500000,
            shares: 250,
            totalShares: 1000,
            ownershipPercentage: 25,
            contractAddress: '0x3456789012345678901234567890123456789012',
          }
        ];

        setTokens(mockTokens);
        setProperties(mockProperties);
        setLoading(false);
      }, 1500);
    } else {
      setLoading(false);
    }
  }, [isConnected, address]);

  if (!isConnected) {
    return (
      <div className="min-h-screen bg-gray-950 text-white pt-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center py-16">
            <h1 className="text-3xl font-bold mb-6">Token Dashboard</h1>
            <div className="bg-gray-900 rounded-xl p-8 max-w-md mx-auto shadow-xl border border-gray-800">
              <div className="mb-6 flex justify-center">
                <div className="w-16 h-16 bg-indigo-900/50 rounded-full flex items-center justify-center">
                  <Wallet size={32} className="text-indigo-400" />
                </div>
              </div>
              <h2 className="text-xl font-semibold mb-2">Connect Your Wallet</h2>
              <p className="text-gray-400 mb-6">
                Connect your wallet to view your tokens and property investments
              </p>
              <div className="flex justify-center">
                <ConnectWalletButton />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-950 text-white pt-16 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
          <h1 className="text-3xl font-bold mb-4 md:mb-0">Token Dashboard</h1>
          {/* Wallet button is already in the navbar, no need to duplicate it here */}
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-800 mb-8">
          <button
            className={`py-3 px-5 font-medium border-b-2 ${
              activeTab === 'overview'
                ? 'border-indigo-500 text-indigo-400'
                : 'border-transparent text-gray-400 hover:text-gray-300'
            }`}
            onClick={() => setActiveTab('overview')}
          >
            <div className="flex items-center space-x-2">
              <BarChart3 size={18} />
              <span>Overview</span>
            </div>
          </button>
          <button
            className={`py-3 px-5 font-medium border-b-2 ${
              activeTab === 'properties'
                ? 'border-indigo-500 text-indigo-400'
                : 'border-transparent text-gray-400 hover:text-gray-300'
            }`}
            onClick={() => setActiveTab('properties')}
          >
            <div className="flex items-center space-x-2">
              <Building size={18} />
              <span>Properties</span>
            </div>
          </button>
          <button
            className={`py-3 px-5 font-medium border-b-2 ${
              activeTab === 'transactions'
                ? 'border-indigo-500 text-indigo-400'
                : 'border-transparent text-gray-400 hover:text-gray-300'
            }`}
            onClick={() => setActiveTab('transactions')}
          >
            <div className="flex items-center space-x-2">
              <Package size={18} />
              <span>Transactions</span>
            </div>
          </button>
        </div>

        {/* Loading indicator */}
        {loading && (
          <div className="flex flex-col items-center justify-center py-16">
            <Loader2 size={48} className="text-indigo-500 animate-spin mb-4" />
            <p className="text-gray-400">Loading your blockchain assets...</p>
          </div>
        )}

        {/* Tab content */}
        {!loading && activeTab === 'overview' && (
          <div className="space-y-8">
            {/* Token Balances */}
            <div className="bg-gray-900 rounded-xl p-6 border border-gray-800">
              <h2 className="text-xl font-semibold mb-4">Token Balances</h2>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {/* Native ETH */}
                <div className="bg-gray-800 rounded-lg p-4 flex items-center">
                  <div className="w-10 h-10 rounded-full bg-indigo-800 flex items-center justify-center mr-4">
                    <Wallet size={20} className="text-indigo-400" />
                  </div>
                  <div>
                    <div className="text-sm text-gray-400">Ethereum</div>
                    <div className="font-semibold">
                      {ethBalance ? ethBalance.formatted : '0'} {ethBalance ? ethBalance.symbol : 'ETH'}
                    </div>
                  </div>
                  <a
                    href={address ? getBlockExplorerUrl(chainId ?? 1, address) : '#'}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="ml-auto text-indigo-400 hover:text-indigo-300"
                  >
                    <ArrowUpRight size={16} />
                  </a>
                </div>

                {/* ERC20 Tokens */}
                {tokens.filter(t => t.type === 'ERC20').map((token, index) => (
                  <div key={`${token.address}-${index}`} className="bg-gray-800 rounded-lg p-4 flex items-center">
                    <div className="w-10 h-10 rounded-full bg-indigo-800 flex items-center justify-center mr-4">
                      {token.symbol === 'BCT' ? (
                        <Building size={20} className="text-indigo-400" />
                      ) : (
                        <div className="text-sm font-semibold text-indigo-400">{token.symbol}</div>
                      )}
                    </div>
                    <div>
                      <div className="text-sm text-gray-400">{token.name}</div>
                      <div className="font-semibold">{token.formattedBalance}</div>
                    </div>
                    <a
                      href={getBlockExplorerUrl(chainId ?? 1, token.address, 'address')}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="ml-auto text-indigo-400 hover:text-indigo-300"
                    >
                      <ArrowUpRight size={16} />
                    </a>
                  </div>
                ))}
              </div>

              {tokens.length === 0 && !loading && (
                <div className="bg-gray-800/50 rounded-lg p-6 flex items-center">
                  <AlertCircle size={24} className="text-gray-500 mr-4" />
                  <div>
                    <p className="text-gray-400">No tokens found in your wallet</p>
                  </div>
                </div>
              )}
            </div>

            {/* Property Assets Summary */}
            <div className="bg-gray-900 rounded-xl p-6 border border-gray-800">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">Your Properties</h2>
                <button
                  onClick={() => setActiveTab('properties')}
                  className="text-sm text-indigo-400 hover:text-indigo-300 flex items-center"
                >
                  <span>View All</span>
                  <ArrowUpRight size={14} className="ml-1" />
                </button>
              </div>

              {properties.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {properties.slice(0, 2).map((property) => (
                    <div key={property.id} className="bg-gray-800 rounded-lg overflow-hidden flex flex-col">
                      <div className="h-40 overflow-hidden">
                        <img
                          src={property.imageUrl || '/assets/property-placeholder.jpg'}
                          alt={property.propertyName}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div className="p-4">
                        <div className="flex items-center mb-2">
                          <div className={`px-2 py-0.5 rounded text-xs font-medium ${
                            property.type === 'full' ? 'bg-purple-900/50 text-purple-300' : 'bg-cyan-900/50 text-cyan-300'
                          }`}>
                            {property.type === 'full' ? 'FULL OWNERSHIP' : 'FRACTIONAL'}
                          </div>
                          {property.ownershipPercentage && (
                            <div className="ml-2 px-2 py-0.5 rounded text-xs font-medium bg-indigo-900/50 text-indigo-300">
                              {property.ownershipPercentage}% OWNED
                            </div>
                          )}
                        </div>
                        <h3 className="font-semibold">{property.propertyName}</h3>
                        <p className="text-sm text-gray-400 mb-3">{property.location}</p>
                        <div className="flex justify-between items-center">
                          <div>
                            <div className="text-xs text-gray-500">Estimated Value</div>
                            <div className="font-semibold">
                              ${property.value.toLocaleString()}
                              {property.ownershipPercentage && (
                                <span className="text-xs text-gray-400 ml-1">
                                  (${Math.round(property.value * (property.ownershipPercentage / 100)).toLocaleString()} yours)
                                </span>
                              )}
                            </div>
                          </div>
                          <a
                            href={getBlockExplorerUrl(chainId ?? 1, property.contractAddress, 'address')}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-indigo-400 hover:text-indigo-300"
                          >
                            <ArrowUpRight size={16} />
                          </a>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="bg-gray-800/50 rounded-lg p-6 flex items-center">
                  <AlertCircle size={24} className="text-gray-500 mr-4" />
                  <div>
                    <p className="text-gray-400">You don't own any property tokens yet</p>
                    <Link
                      href="/marketplace"
                      className="text-indigo-400 hover:text-indigo-300 text-sm flex items-center mt-1"
                    >
                      <span>Browse Marketplace</span>
                      <ArrowUpRight size={14} className="ml-1" />
                    </Link>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {!loading && activeTab === 'properties' && (
          <div className="space-y-6">
            <div className="bg-gray-900 rounded-xl p-6 border border-gray-800">
              <h2 className="text-xl font-semibold mb-4">Your Property Portfolio</h2>

              {properties.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {properties.map((property) => (
                    <div key={property.id} className="bg-gray-800 rounded-lg overflow-hidden flex flex-col">
                      <div className="h-40 overflow-hidden">
                        <img
                          src={property.imageUrl || '/assets/property-placeholder.jpg'}
                          alt={property.propertyName}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div className="p-4">
                        <div className="flex items-center mb-2">
                          <div className={`px-2 py-0.5 rounded text-xs font-medium ${
                            property.type === 'full' ? 'bg-purple-900/50 text-purple-300' : 'bg-cyan-900/50 text-cyan-300'
                          }`}>
                            {property.type === 'full' ? 'FULL OWNERSHIP' : 'FRACTIONAL'}
                          </div>
                          {property.ownershipPercentage && (
                            <div className="ml-2 px-2 py-0.5 rounded text-xs font-medium bg-indigo-900/50 text-indigo-300">
                              {property.ownershipPercentage}% OWNED
                            </div>
                          )}
                        </div>
                        <h3 className="font-semibold">{property.propertyName}</h3>
                        <p className="text-sm text-gray-400 mb-3">{property.location}</p>

                        {property.type === 'fractional' && (
                          <div className="mb-3">
                            <div className="text-xs text-gray-500 mb-1">Your Shares</div>
                            <div className="w-full bg-gray-700 rounded-full h-2.5">
                              <div
                                className="bg-gradient-to-r from-indigo-600 to-purple-600 h-2.5 rounded-full"
                                style={{ width: `${property.ownershipPercentage}%` }}
                              ></div>
                            </div>
                            <div className="flex justify-between text-xs mt-1">
                              <span className="text-gray-400">{property.shares} / {property.totalShares} shares</span>
                              <span className="text-indigo-400">{property.ownershipPercentage}%</span>
                            </div>
                          </div>
                        )}

                        <div className="flex justify-between items-center">
                          <div>
                            <div className="text-xs text-gray-500">Estimated Value</div>
                            <div className="font-semibold">
                              ${property.value.toLocaleString()}
                              {property.ownershipPercentage && (
                                <span className="text-xs text-gray-400 ml-1">
                                  (${Math.round(property.value * (property.ownershipPercentage / 100)).toLocaleString()} yours)
                                </span>
                              )}
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <a
                              href={getBlockExplorerUrl(chainId ?? 1, property.contractAddress, 'address')}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="p-2 rounded-full hover:bg-gray-700 text-indigo-400 hover:text-indigo-300"
                            >
                              <ArrowUpRight size={16} />
                            </a>
                            <Link
                              href={`/property/${property.id}`}
                              className="bg-indigo-600 hover:bg-indigo-700 rounded-md px-3 py-1.5 text-sm font-medium"
                            >
                              Details
                            </Link>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="bg-gray-800/50 rounded-lg p-6 flex items-center">
                  <AlertCircle size={24} className="text-gray-500 mr-4" />
                  <div>
                    <p className="text-gray-400">You don't own any property tokens yet</p>
                    <Link
                      href="/marketplace"
                      className="text-indigo-400 hover:text-indigo-300 text-sm flex items-center mt-1"
                    >
                      <span>Browse Marketplace</span>
                      <ArrowUpRight size={14} className="ml-1" />
                    </Link>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {!loading && activeTab === 'transactions' && (
          <div className="space-y-6">
            <div className="bg-gray-900 rounded-xl p-6 border border-gray-800">
              <h2 className="text-xl font-semibold mb-4">Transaction History</h2>

              <div className="bg-gray-800/50 rounded-lg p-6 flex items-center justify-center text-center">
                <div>
                  <p className="text-gray-400 mb-4">Transaction history will be available once smart contracts are integrated</p>
                  <p className="text-sm text-gray-500">This section will show your property purchases, sales, transfers, and other blockchain activities</p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}