import React, { useState, useEffect, useCallback } from 'react';
import { useSupabaseClient } from '@supabase/auth-helpers-react';
import { Upload, X, Loader2, AlertCircle, Shield, FileText, CheckCircle, XCircle, Clock } from 'lucide-react';
import { 
  uploadKYCDocument, 
  getUserKYCDocuments, 
  type KYCDocument, 
  type KYCDocumentType, 
  type KYCDocumentCategory,
  type VerificationStatus
} from '@/utils/fileStorage';
import { useUser } from '@/context/UserContext';

interface KYCDocumentUploaderProps {
  documentType: KYCDocumentType;
  documentCategory: KYCDocumentCategory;
  onDocumentChange?: (document: KYCDocument | null) => void;
  encrypt?: boolean;
  theme?: 'light' | 'dark';
}

export default function KYCDocumentUploader({
  documentType,
  documentCategory,
  onDocumentChange,
  encrypt = true,
  theme = 'light'
}: KYCDocumentUploaderProps) {
  const { user } = useUser();
  const [document, setDocument] = useState<KYCDocument | null>(null);
  const [uploading, setUploading] = useState<boolean>(false);
  const [error, setError] = useState<string>('');
  const [dragActive, setDragActive] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);

  // Fetch existing document on component mount
  useEffect(() => {
    const fetchDocument = async () => {
      if (!user?.id) return;
      
      try {
        setLoading(true);
        const documents = await getUserKYCDocuments(user.id);
        const existingDocument = documents.find(
          doc => doc.document_type === documentType && doc.document_category === documentCategory
        );
        
        if (existingDocument) {
          setDocument(existingDocument);
          if (onDocumentChange) {
            onDocumentChange(existingDocument);
          }
        }
      } catch (err) {
        console.error('Error fetching KYC document:', err);
        setError('Failed to load existing document');
      } finally {
        setLoading(false);
      }
    };

    if (user?.id) {
      fetchDocument();
    } else {
      setLoading(false);
    }
  }, [user?.id, documentType, documentCategory, onDocumentChange]);

  // Handle file selection
  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;
    
    await uploadFile(files[0]);
    
    // Clear the input value so the same file can be uploaded again if needed
    e.target.value = '';
  };

  // Handle drag events
  const handleDrag = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  // Handle drop event
  const handleDrop = useCallback(async (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      await uploadFile(e.dataTransfer.files[0]);
    }
  }, []);

  // Upload file
  const uploadFile = async (file: File) => {
    if (!user?.id) {
      setError('You must be logged in to upload documents');
      return;
    }
    
    // Validate file type
    const validTypes = [
      'image/jpeg', 
      'image/png', 
      'application/pdf', 
      'application/msword', 
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];
    
    if (!validTypes.includes(file.type)) {
      setError(`Invalid file type. Allowed types: JPEG, PNG, PDF, DOC, DOCX`);
      return;
    }
    
    // Validate file size (10MB max)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      setError(`File too large. Maximum size is 10MB`);
      return;
    }
    
    setUploading(true);
    setError('');
    
    try {
      // Upload the document
      const uploadedDocument = await uploadKYCDocument(
        file,
        user.id,
        documentType,
        documentCategory,
        encrypt
      );
      
      // Update state
      setDocument(uploadedDocument);
      if (onDocumentChange) {
        onDocumentChange(uploadedDocument);
      }
    } catch (err) {
      console.error('Error uploading document:', err);
      setError('Failed to upload document');
    } finally {
      setUploading(false);
    }
  };

  // Get status badge based on verification status
  const getStatusBadge = (status: VerificationStatus) => {
    switch (status) {
      case 'verified':
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
            <CheckCircle size={12} className="mr-1" />
            Verified
          </span>
        );
      case 'rejected':
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-red-100 text-red-800">
            <XCircle size={12} className="mr-1" />
            Rejected
          </span>
        );
      case 'expired':
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-yellow-100 text-yellow-800">
            <Clock size={12} className="mr-1" />
            Expired
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
            <Clock size={12} className="mr-1" />
            Pending
          </span>
        );
    }
  };

  // Format document type for display
  const formatDocumentType = (type: KYCDocumentType) => {
    return type
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  if (!user?.id) {
    return (
      <div className={`w-full p-4 border rounded ${theme === 'dark' ? 'bg-gray-800 border-gray-700 text-white' : 'bg-gray-50 border-gray-200 text-gray-800'}`}>
        <p className="text-center">Please log in to upload documents</p>
      </div>
    );
  }

  return (
    <div className={`w-full ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>
      {/* Error message */}
      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded flex items-center">
          <AlertCircle size={16} className="mr-2" />
          <span>{error}</span>
          <button 
            onClick={() => setError('')}
            className="ml-auto text-red-700 hover:text-red-900"
          >
            <X size={16} />
          </button>
        </div>
      )}
      
      {/* Document type header */}
      <div className="mb-4">
        <h3 className={`text-lg font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
          {formatDocumentType(documentType)}
        </h3>
        <p className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
          Upload your {formatDocumentType(documentType)} for verification
        </p>
      </div>
      
      {/* Document preview or upload area */}
      {loading ? (
        <div className="flex justify-center p-6">
          <Loader2 className="animate-spin h-6 w-6 text-indigo-500" />
        </div>
      ) : document ? (
        <div className={`p-4 border rounded ${theme === 'dark' ? 'bg-gray-800 border-gray-700' : 'bg-gray-50 border-gray-200'}`}>
          <div className="flex items-start justify-between">
            <div className="flex items-center">
              <FileText className={`h-8 w-8 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'} mr-3`} />
              <div>
                <p className={`font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                  {document.file_name}
                </p>
                <p className={`text-xs ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                  Uploaded on {new Date(document.created_at!).toLocaleDateString()}
                </p>
              </div>
            </div>
            <div>
              {getStatusBadge(document.verification_status || 'pending')}
            </div>
          </div>
          
          {document.verification_notes && (
            <div className={`mt-3 p-2 text-sm rounded ${
              theme === 'dark' ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-700'
            }`}>
              <p className="font-medium">Verification Notes:</p>
              <p>{document.verification_notes}</p>
            </div>
          )}
          
          <div className="mt-4 flex justify-end">
            <button
              onClick={() => setDocument(null)}
              className={`px-3 py-1 text-sm rounded ${
                theme === 'dark' 
                  ? 'bg-indigo-600 text-white hover:bg-indigo-700' 
                  : 'bg-indigo-500 text-white hover:bg-indigo-600'
              }`}
            >
              Upload New Document
            </button>
          </div>
        </div>
      ) : (
        <div
          className={`border-2 border-dashed rounded-lg p-6 text-center ${
            dragActive 
              ? 'border-indigo-500 bg-indigo-50' 
              : theme === 'dark' 
                ? 'border-gray-700 bg-gray-800' 
                : 'border-gray-300 bg-gray-50'
          }`}
          onDragEnter={handleDrag}
          onDragOver={handleDrag}
          onDragLeave={handleDrag}
          onDrop={handleDrop}
        >
          <div className="space-y-2 flex flex-col items-center justify-center">
            {encrypt ? (
              <Shield size={36} className={theme === 'dark' ? 'text-gray-400' : 'text-gray-500'} />
            ) : (
              <Upload size={36} className={theme === 'dark' ? 'text-gray-400' : 'text-gray-500'} />
            )}
            
            <div className="text-sm">
              <label
                htmlFor={`kyc-document-upload-${documentType}`}
                className={`cursor-pointer font-medium ${theme === 'dark' ? 'text-indigo-400' : 'text-indigo-600'} hover:underline`}
              >
                <span>Click to upload</span>
                <input
                  id={`kyc-document-upload-${documentType}`}
                  name={`kyc-document-upload-${documentType}`}
                  type="file"
                  className="sr-only"
                  accept="image/jpeg,image/png,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                  onChange={handleFileChange}
                  disabled={uploading}
                />
              </label>
              <span className={theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}> or drag and drop</span>
            </div>
            
            <p className={`text-xs ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
              JPEG, PNG, PDF, DOC, DOCX up to 10MB
            </p>
            
            {encrypt && (
              <div className={`flex items-center mt-1 text-xs ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                <Shield size={12} className="mr-1" />
                <span>Your document will be encrypted for security</span>
              </div>
            )}
            
            {uploading && (
              <div className="flex items-center mt-2">
                <Loader2 className="animate-spin h-4 w-4 mr-2 text-indigo-500" />
                <span className={`text-sm ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
                  Uploading...
                </span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
