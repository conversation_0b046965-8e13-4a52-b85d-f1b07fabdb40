import React from 'react';
export default function Resources() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-r from-indigo-900 to-purple-900 text-white">
      <div className="bg-indigo-800 bg-opacity-50 p-8 rounded-lg shadow-lg max-w-2xl w-full">
        <h1 className="text-3xl font-bold mb-4">Resources</h1>
        <p className="mb-6">Find guides, FAQs, and documentation to help you get started with BrickChain.</p>
        <h2 className="text-xl font-semibold mb-2">Guides</h2>
        <ul className="list-disc list-inside mb-6 space-y-1">
          <li><a href="#" className="text-cyan-400 hover:underline">Getting Started with BrickChain</a></li>
          <li><a href="#" className="text-cyan-400 hover:underline">How to List a Property</a></li>
          <li><a href="#" className="text-cyan-400 hover:underline">Investing in Tokenized Real Estate</a></li>
          <li><a href="#" className="text-cyan-400 hover:underline">Understanding BCT Token Utility</a></li>
        </ul>
        <h2 className="text-xl font-semibold mb-2">FAQs</h2>
        <ul className="list-disc list-inside mb-6 space-y-1">
          <li>What is BrickChain and how does it work?</li>
          <li>How do I invest in a property?</li>
          <li>What is fractional ownership?</li>
          <li>How are returns distributed?</li>
          <li>Is my investment secure?</li>
        </ul>
        <h2 className="text-xl font-semibold mb-2">Documentation</h2>
        <ul className="list-disc list-inside space-y-1">
          <li><a href="#" className="text-cyan-400 hover:underline">Platform Whitepaper (PDF)</a></li>
          <li><a href="#" className="text-cyan-400 hover:underline">Smart Contract Documentation</a></li>
          <li><a href="#" className="text-cyan-400 hover:underline">API Reference</a></li>
        </ul>
      </div>
    </div>
  );
} 