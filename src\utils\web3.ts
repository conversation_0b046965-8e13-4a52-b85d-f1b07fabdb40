import { sepolia, mainnet, polygon, optimism, arbitrum, base } from 'wagmi/chains';
import { getDefaultConfig } from '@rainbow-me/rainbowkit';

// Use an environment variable for the project ID
const walletConnectProjectId = process.env.NEXT_PUBLIC_WALLET_CONNECT_PROJECT_ID || 'default-project-id';

/**
 * Create the wagmi config with RainbowKit's getDefaultConfig
 * This uses the simplified configuration approach with default wallet support
 */
export const config = getDefaultConfig({
  appName: 'BrickChain',
  projectId: walletConnectProjectId,
  chains: [sepolia, mainnet, polygon, optimism, arbitrum, base],
  ssr: false, // Disable SSR for wagmi to avoid hydration issues
});

/**
 * Get the current network name based on chain ID
 */
export function getNetworkName(chainId?: number): string {
  if (!chainId) return 'Not Connected';

  switch (chainId) {
    case 1: // mainnet
      return 'Ethereum';
    case 11155111: // sepolia
      return 'Sepolia';
    case 57054: // sonicBlazeTestnet
      return 'Sonic Blaze';
    case 137: // polygon
      return 'Polygon';
    case 10: // optimism
      return 'Optimism';
    case 42161: // arbitrum
      return 'Arbitrum';
    case 8453: // base
      return 'Base';
    default:
      return 'Unknown Network';
  }
}

/**
 * Format an Ethereum address for display (0x1234...5678)
 */
export function formatAddress(address?: string): string {
  if (!address) return '';

  return `${address.substring(0, 6)}...${address.substring(address.length - 4)}`;
}

/**
 * Build a block explorer URL for the given address or transaction
 */
export function getBlockExplorerUrl(chainId: number, data: string, type: 'address' | 'tx' = 'address'): string {
  let baseUrl = '';
  switch (chainId) {
    case 1: // mainnet
      baseUrl = 'https://etherscan.io';
      break;
    case 11155111: // sepolia
      baseUrl = 'https://sepolia.etherscan.io';
      break;
    case 57054: // sonicBlazeTestnet
      baseUrl = 'https://testnet.sonicscan.org';
      break;
    case 137: // polygon
      baseUrl = 'https://polygonscan.com';
      break;
    case 10: // optimism
      baseUrl = 'https://optimistic.etherscan.io';
      break;
    case 42161: // arbitrum
      baseUrl = 'https://arbiscan.io';
      break;
    case 8453: // base
      baseUrl = 'https://basescan.org';
      break;
    default:
      return '#'; // No explorer for unknown network
  }
  return `${baseUrl}/${type}/${data}`;
}