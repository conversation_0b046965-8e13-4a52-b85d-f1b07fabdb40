'use client';
import React, { ReactNode, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useUser } from '../context/UserContext';
import {
  Home, Building, Wallet, Settings, Users,
  ChartBar, AlertTriangle, Loader2, LogOut,
  Link as LinkIcon
} from 'lucide-react';
import Link from 'next/link';
import ConnectWalletButton from './ConnectWalletButton';

type RoleConfig = {
  title: string;
  description: string;
  icon: React.ElementType;
  color: string;
  actions: {
    label: string;
    href: string;
    icon: React.ElementType;
  }[];
};

const roleConfigs: Record<string, RoleConfig> = {
  owner: {
    title: 'Owner Dashboard',
    description: 'Manage your properties and track investments',
    icon: Building,
    color: 'from-blue-600 to-indigo-700',
    actions: [
      { label: 'Add Property', href: '/dashboard/owner/add-property', icon: Building },
      { label: 'View Investments', href: '/dashboard/owner/investments', icon: Wallet },
      { label: 'Account Settings', href: '/dashboard/owner/settings', icon: Settings },
    ]
  },
  investor: {
    title: 'Investor Dashboard',
    description: 'Track your investment portfolio and find new opportunities',
    icon: Wallet,
    color: 'from-purple-600 to-indigo-700',
    actions: [
      { label: 'Browse Properties', href: '/dashboard/investor/opportunities', icon: Building },
      { label: 'My Portfolio', href: '/dashboard/investor/portfolio', icon: Wallet },
      { label: 'Account Settings', href: '/dashboard/investor/settings', icon: Settings },
    ]
  },
  admin: {
    title: 'Admin Dashboard',
    description: 'Manage the BrickChain platform and monitor activities',
    icon: Users,
    color: 'from-cyan-600 to-blue-700',
    actions: [
      { label: 'User Management', href: '/dashboard/admin/users', icon: Users },
      { label: 'All Properties', href: '/dashboard/admin/properties', icon: Building },
      { label: 'Platform Analytics', href: '/dashboard/admin/analytics', icon: ChartBar },
    ]
  }
};

// Wallet connection section using the centralized wallet context
const WalletSection = () => {
  return (
    <div className="relative">
      <ConnectWalletButton variant="sidebar" />
    </div>
  );
};

export default function DashboardWrapper({
  children,
  activeTab,
  secondaryNav
}: {
  children: ReactNode;
  activeTab?: string;
  secondaryNav?: { label: string; href: string }[];
}) {
  const { user, loading, signOut } = useUser();
  const router = useRouter();

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[70vh]">
        <div className="text-center">
          <Loader2 size={40} className="mx-auto animate-spin text-indigo-400 mb-4" />
          <p className="text-gray-400">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-[70vh]">
        <div className="bg-red-900/40 border border-red-800 rounded-lg p-6 max-w-md">
          <div className="flex items-center space-x-3 mb-4">
            <AlertTriangle size={24} className="text-red-400" />
            <h2 className="text-xl font-bold text-white">Authentication Required</h2>
          </div>
          <p className="text-red-300 mb-6">You need to be signed in to access this dashboard.</p>
          <div className="flex space-x-4">
            <Link href="/" className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 rounded-lg text-white font-medium text-center transition-colors flex-1">
              Connect Wallet
            </Link>
          </div>
        </div>
      </div>
    );
  }

  // Get role-specific configuration
  const roleConfig = roleConfigs[user.role] || {
    title: 'Dashboard',
    description: 'Welcome to your BrickChain dashboard',
    icon: Home,
    color: 'from-gray-600 to-gray-700',
    actions: [
      { label: 'Marketplace', href: '/marketplace', icon: Building },
      { label: 'Account Settings', href: '/dashboard/settings', icon: Settings },
    ]
  };

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-gray-950 to-indigo-950">
      <div className="flex min-h-screen w-full">
        {/* Sidebar */}
        <div className="w-64 bg-gray-900/50 border-r border-gray-800 hidden md:flex flex-col justify-between">
          <div>
            <div className="p-6">
              <h1 className="text-2xl font-bold text-white">BrickChain</h1>
            </div>
            <nav className="mt-6">
              {/* ... existing nav items ... */}
            </nav>
          </div>
          {/* Wallet connection button */}
          <div className="mt-6 px-4">
            <WalletSection />
          </div>
          {/* Logout button at the bottom */}
          <div className="p-6 border-t border-gray-800">
            <button
              onClick={async () => {
                await signOut();
                router.push('/');
              }}
              className="w-full flex items-center gap-2 px-4 py-2 bg-red-700/80 hover:bg-red-800 text-white rounded-lg transition-colors"
            >
              <LogOut size={18} />
              <span>Logout</span>
            </button>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 w-full overflow-x-hidden">
          {/* Mobile Header - Remove WalletSection from here */}
          <div className="md:hidden bg-gray-900/50 border-b border-gray-800 p-4 flex justify-between items-center">
            <h1 className="text-xl font-bold text-white">BrickChain</h1>
          </div>

          {/* Desktop Header - Remove WalletSection from here, user info can remain */}
          <div className="hidden md:flex justify-end items-center p-4 bg-gray-900/30 border-b border-gray-800">
            <div className="flex items-center gap-4">
              {/* <WalletSection /> NO LONGER HERE */}

              {user && (
                <div className="flex items-center gap-2">
                  <span className="text-gray-300 text-sm">{user.full_name}</span>
                  <div className="w-9 h-9 rounded-full bg-indigo-600/50 border border-indigo-500/30 flex items-center justify-center">
                    <Users size={18} className="text-indigo-300" />
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Secondary Navigation */}
          {secondaryNav && (
            <div className="bg-gray-900/50 border-b border-gray-800 w-full">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex overflow-x-auto space-x-4 py-4">
                  {secondaryNav.map((item) => (
                    <Link
                      key={item.href}
                      href={item.href}
                      className={`px-3 py-2 rounded-md text-sm font-medium whitespace-nowrap ${
                        activeTab === item.href.split('/').pop()
                          ? 'bg-indigo-600 text-white'
                          : 'text-gray-300 hover:bg-gray-800'
                      }`}
                    >
                      {item.label}
                    </Link>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Page Content */}
          <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 w-full">
            {children}
          </main>
        </div>
      </div>
    </div>
  );
}