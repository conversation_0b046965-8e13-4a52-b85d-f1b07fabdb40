import { Property } from '../components/PropertyCard';

// Mock property data for development and testing
export const mockProperties: Property[] = [
  {
    id: '1',
    name: 'Beachfront Villa',
    location: 'Cape Town, South Africa',
    price: 300000,
    priceInEth: 150,
    return_rate: 8.2,
    description: 'Luxurious 5-bedroom beachfront villa with panoramic ocean views, infinity pool, and direct beach access. Features include a chef\'s kitchen, home theater, and smart home automation. Perfect for vacation rentals with high occupancy rates year-round.',
    status: 'available',
    image_url: '/properties/beachfront-villa.jpg',
    image_cid: 'bafybeihcyruaeza7uyjd6ugicbcrqumejf6uf353e5etdkhotqffwtguva',
    published: true,
    total_shares: 1000,
    funded_percentage: 78,
    investors_count: 780
  },
  {
    id: '2',
    name: 'Manhattan Penthouse',
    location: 'New York City, USA',
    price: 4200000,
    priceInEth: 2100,
    return_rate: 6.8,
    description: 'Exclusive 3-bedroom penthouse in the heart of Manhattan with floor-to-ceiling windows offering stunning city skyline views. Features include a private terrace, custom Italian cabinetry, marble bathrooms, and 24/7 concierge service.',
    status: 'available',
    image_url: '/properties/manhattan-penthouse.jpg',
    published: true,
    total_shares: 2000,
    funded_percentage: 42,
    investors_count: 301
  },
  {
    id: '3',
    name: 'Dubai Marina Apartment',
    location: 'Dubai, UAE',
    price: 850000,
    priceInEth: 425,
    return_rate: 9.5,
    description: 'Modern 2-bedroom apartment in the prestigious Dubai Marina with panoramic waterfront views. The property features smart home technology, premium finishes, and access to world-class amenities including infinity pool, fitness center, and 24-hour security.',
    status: 'available',
    image_url: '/properties/dubai-marina.jpg',
    image_cid: 'bafybeihwel5qzsu2rakl6zrse5nshuc23cn4fvlqxdjrpncrjyn6fh3s5m',
    published: true,
    total_shares: 1250,
    funded_percentage: 93,
    investors_count: 876
  },
  {
    id: '4',
    name: 'Bali Eco Resort',
    location: 'Ubud, Bali, Indonesia',
    price: 1200000,
    priceInEth: 600,
    return_rate: 11.2,
    description: 'Sustainable eco-resort featuring 12 private villas constructed with bamboo and reclaimed materials. Set among rice terraces with an organic garden, infinity pool, yoga pavilion, and spa. Attracts high-end travelers seeking wellness experiences.',
    status: 'available',
    image_url: '/properties/bali-resort.jpg',
    published: true,
    total_shares: 1800,
    funded_percentage: 32,
    investors_count: 184
  },
  {
    id: '5',
    name: 'Alpine Ski Chalet',
    location: 'Chamonix, France',
    price: 1850000,
    priceInEth: 925,
    return_rate: 7.4,
    description: 'Luxury 4-bedroom ski-in/ski-out chalet in the heart of the French Alps. Features include a hot tub, sauna, home cinema, and wine cellar. Large windows frame spectacular mountain views. High rental yield during winter season with growing summer tourism.',
    status: 'available',
    image_url: '/properties/alpine-chalet.jpg',
    published: true,
    total_shares: 1500,
    funded_percentage: 56,
    investors_count: 437
  },
  {
    id: '6',
    name: 'Sydney Harbour Apartment',
    location: 'Sydney, Australia',
    price: 1680000,
    priceInEth: 840,
    return_rate: 6.5,
    description: 'Premium 2-bedroom apartment with unobstructed views of Sydney Opera House and Harbour Bridge. Featuring contemporary design, high-end appliances, and access to a rooftop infinity pool. Located minutes from CBD with excellent rental prospects.',
    status: 'available',
    image_url: '/properties/sydney-apartment.jpg',
    published: true,
    total_shares: 1200,
    funded_percentage: 67,
    investors_count: 522
  },
  {
    id: '7',
    name: 'Santorini Villa',
    location: 'Oia, Santorini, Greece',
    price: 960000,
    priceInEth: 480,
    return_rate: 9.8,
    description: 'Authentic cave-style villa with breathtaking caldera views. Features include a private plunge pool, traditional Cycladic architecture, and tasteful modern amenities. Consistently booked for luxury vacation rentals with exceptional returns.',
    status: 'available',
    image_url: '/properties/santorini-villa.jpg',
    image_cid: 'bafybeifw5qpvxu7qp6rnl5meblt5qgslmdc66nwlaakav3xirbqdau36zy',
    published: true,
    total_shares: 1000,
    funded_percentage: 100,
    investors_count: 1000
  },
  {
    id: '8',
    name: 'Bangkok Luxury Condo',
    location: 'Bangkok, Thailand',
    price: 520000,
    priceInEth: 260,
    return_rate: 8.7,
    description: 'High-floor 1-bedroom luxury condominium in Bangkok\'s most exclusive area. Features include floor-to-ceiling windows, smart home system, and high-end finishes. Building amenities include infinity edge pool, sky garden, and concierge service.',
    status: 'available',
    image_url: '/properties/bangkok-condo.jpg',
    published: true,
    total_shares: 750,
    funded_percentage: 48,
    investors_count: 183
  },
  {
    id: '9',
    name: 'Lisbon Historic Apartment',
    location: 'Lisbon, Portugal',
    price: 490000,
    priceInEth: 245,
    return_rate: 7.9,
    description: 'Beautifully renovated 19th-century apartment in Lisbon\'s historic Alfama district. Combining original architectural features with modern comfort. Ideal for short-term rentals with Portugal\'s popular Golden Visa program for investors.',
    status: 'available',
    image_url: '/properties/lisbon-apartment.jpg',
    published: true,
    total_shares: 800,
    funded_percentage: 82,
    investors_count: 410
  },
  {
    id: '10',
    name: 'Tulum Beachfront Resort',
    location: 'Tulum, Mexico',
    price: 1350000,
    priceInEth: 675,
    return_rate: 10.3,
    description: 'Eco-friendly beachfront resort with 8 private bungalows in trendy Tulum. Sustainable design with solar power and filtered rainwater systems. Offers yoga retreats and wellness programs with strong booking history and growing tourism market.',
    status: 'available',
    image_url: '/properties/tulum-resort.jpg',
    image_cid: 'bafybeih5ke7a62ankix5hgyypxs2dxtasfpfrvpir4h6xn56gvsbz5hfhi',
    published: true,
    total_shares: 1800,
    funded_percentage: 61,
    investors_count: 704
  },
  {
    id: '11',
    name: 'Kyoto Traditional Machiya',
    location: 'Kyoto, Japan',
    price: 780000,
    priceInEth: 390,
    return_rate: 6.4,
    description: 'Meticulously restored traditional Japanese townhouse (machiya) in the heart of Kyoto. Features tatami rooms, Japanese garden, and modern amenities while preserving authentic architectural elements. Popular with cultural tourists seeking authentic experiences.',
    status: 'available',
    image_url: '/properties/kyoto-machiya.jpg',
    published: true,
    total_shares: 900,
    funded_percentage: 29,
    investors_count: 145
  },
  {
    id: '12',
    name: 'Costa Rica Jungle Lodge',
    location: 'Manuel Antonio, Costa Rica',
    price: 1120000,
    priceInEth: 560,
    return_rate: 9.1,
    description: 'Boutique eco-lodge set in a private rainforest reserve with ocean views. Features 6 luxury villas, infinity pool, and on-site restaurant serving farm-to-table cuisine. Strong booking history with eco-tourism and wellness retreat market.',
    status: 'available',
    image_url: '/properties/costa-rica-lodge.jpg',
    published: true,
    total_shares: 1400,
    funded_percentage: 76,
    investors_count: 684
  }
];

// Function to get a single mock property by ID
export const getMockPropertyById = (id: string): Property | undefined => {
  return mockProperties.find(property => property.id === id);
};

// Export a function that returns a subset of properties
export const getRandomMockProperties = (count: number = 3): Property[] => {
  const shuffled = [...mockProperties].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
};

export default mockProperties; 