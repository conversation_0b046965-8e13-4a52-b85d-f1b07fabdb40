// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "./PropertyRegistry.sol";
import "./FractionalOwnership.sol";
import "./BrickToken.sol";

/**
 * @title InvestmentManager
 * @dev Orchestrates property fractionalization, share sales, and full-ownership reclaim
 */
contract InvestmentManager is Ownable, ReentrancyGuard {
    // Property registry contract
    PropertyRegistry private _propertyRegistry;

    // Fractional ownership contract
    FractionalOwnership private _fractionalOwnership;

    // Brick token contract
    BrickToken private _brickToken;

    // Platform fee percentage (scaled by 10^2)
    uint256 private _platformFeePercentage = 250; // 2.5%

    // Investment struct
    struct Investment {
        uint256 propertyId;
        address investor;
        uint256 shares;
        uint256 amount;
        uint256 timestamp;
        bool usedBCT;
    }

    // Distribution struct
    struct Distribution {
        uint256 propertyId;
        uint256 totalAmount;
        uint256 timestamp;
    }

    // Mapping from property ID to array of investments
    mapping(uint256 => Investment[]) private _investments;

    // Mapping from property ID to array of distributions
    mapping(uint256 => Distribution[]) private _distributions;

    // Mapping from investor address to claimed distributions per property
    mapping(address => mapping(uint256 => uint256)) private _claimedDistributions;

    // Events
    event PropertyFractionalized(uint256 indexed propertyId, uint256 totalShares, uint256 pricePerShare);
    event SharesPurchased(uint256 indexed propertyId, address indexed investor, uint256 shares, uint256 amount, bool usedBCT);
    event PropertyReclaimed(uint256 indexed propertyId, address indexed owner);
    event DistributionCreated(uint256 indexed propertyId, uint256 totalAmount);
    event DistributionClaimed(uint256 indexed propertyId, address indexed investor, uint256 amount);

    constructor(
        address propertyRegistryAddress,
        address fractionalOwnershipAddress,
        address brickTokenAddress
    ) Ownable(msg.sender) {
        _propertyRegistry = PropertyRegistry(propertyRegistryAddress);
        _fractionalOwnership = FractionalOwnership(fractionalOwnershipAddress);
        _brickToken = BrickToken(brickTokenAddress);
    }

    /**
     * @dev Fractionalizes a property
     * @param propertyId ID of the property
     * @param totalShares Total number of shares to create
     * @param pricePerShare Price per share in ETH
     */
    function fractionalizeProperty(
        uint256 propertyId,
        uint256 totalShares,
        uint256 pricePerShare
    ) external nonReentrant {
        // Check if caller is the property owner
        PropertyRegistry.Property memory property = _propertyRegistry.getProperty(propertyId);
        require(property.owner == msg.sender, "Not the property owner");
        require(!property.fractionalized, "Property already fractionalized");

        // Transfer property NFT to this contract
        _propertyRegistry.safeTransferFrom(msg.sender, address(this), propertyId);

        // Lock the property NFT
        _propertyRegistry.lockProperty(propertyId);

        // Create shares
        _fractionalOwnership.createShares(
            propertyId,
            property.name,
            totalShares,
            _propertyRegistry.tokenURI(propertyId)
        );

        emit PropertyFractionalized(propertyId, totalShares, pricePerShare);
    }

    /**
     * @dev Buys shares of a property using ETH
     * @param propertyId ID of the property
     * @param shares Number of shares to purchase
     */
    function buyShares(uint256 propertyId, uint256 shares) external payable nonReentrant {
        _buySharesInternal(propertyId, shares, false);
    }

    /**
     * @dev Buys shares of a property using BCT tokens
     * @param propertyId ID of the property
     * @param shares Number of shares to purchase
     */
    function buySharesWithBCT(uint256 propertyId, uint256 shares) external nonReentrant {
        _buySharesInternal(propertyId, shares, true);
    }

    /**
     * @dev Internal function to buy shares
     * @param propertyId ID of the property
     * @param shares Number of shares to purchase
     * @param useBCT Whether to use BCT tokens for payment
     */
    function _buySharesInternal(uint256 propertyId, uint256 shares, bool useBCT) private {
        // Check if property is fractionalized
        PropertyRegistry.Property memory property = _propertyRegistry.getProperty(propertyId);
        require(property.fractionalized, "Property not fractionalized");

        // Check if enough shares are available
        uint256 availableShares = _fractionalOwnership.getAvailableShares(propertyId);
        require(shares <= availableShares, "Not enough shares available");

        // Calculate investment amount
        uint256 sharePrice = property.valuation / _fractionalOwnership.getTotalShares(propertyId);
        uint256 investmentAmount = sharePrice * shares;

        // Calculate platform fee
        uint256 platformFee = (investmentAmount * _platformFeePercentage) / 10000;

        // Apply discount if using BCT
        if (useBCT) {
            platformFee = _brickToken.calculateDiscount(msg.sender, platformFee);

            // Transfer BCT tokens
            uint256 bctAmount = investmentAmount + platformFee;
            _brickToken.transferFrom(msg.sender, address(this), bctAmount);

            // Burn a portion of the BCT
            _brickToken.burn(bctAmount / 2);
        } else {
            // Check if enough ETH was sent
            require(msg.value >= investmentAmount + platformFee, "Insufficient ETH sent");

            // Refund excess ETH
            if (msg.value > investmentAmount + platformFee) {
                payable(msg.sender).transfer(msg.value - (investmentAmount + platformFee));
            }
        }

        // Create investment record
        Investment memory investment = Investment({
            propertyId: propertyId,
            investor: msg.sender,
            shares: shares,
            amount: investmentAmount,
            timestamp: block.timestamp,
            usedBCT: useBCT
        });

        // Store investment
        _investments[propertyId].push(investment);

        // Mint shares to investor
        _fractionalOwnership.mintShares(msg.sender, propertyId, shares);

        // Reward investor with BCT tokens (0.5% of investment value)
        uint256 rewardAmount = (investmentAmount * 50) / 10000;
        _brickToken.mint(msg.sender, rewardAmount);

        emit SharesPurchased(propertyId, msg.sender, shares, investmentAmount, useBCT);
    }

    /**
     * @dev Reclaims full ownership of a property
     * @param propertyId ID of the property
     */
    function reclaimProperty(uint256 propertyId) external nonReentrant {
        // Check if caller owns all shares
        require(_fractionalOwnership.ownsAllShares(msg.sender, propertyId), "Must own all shares");

        // Burn all shares
        uint256 totalShares = _fractionalOwnership.getTotalShares(propertyId);
        _fractionalOwnership.burnShares(msg.sender, propertyId, totalShares);

        // Unlock the property NFT
        _propertyRegistry.unlockProperty(propertyId, msg.sender);

        // Transfer property NFT to caller
        _propertyRegistry.safeTransferFrom(address(this), msg.sender, propertyId);

        emit PropertyReclaimed(propertyId, msg.sender);
    }

    /**
     * @dev Creates a distribution for a property
     * @param propertyId ID of the property
     */
    function createDistribution(uint256 propertyId) external payable onlyOwner {
        require(msg.value > 0, "Distribution amount must be greater than 0");

        // Create distribution record
        Distribution memory distribution = Distribution({
            propertyId: propertyId,
            totalAmount: msg.value,
            timestamp: block.timestamp
        });

        // Store distribution
        _distributions[propertyId].push(distribution);

        emit DistributionCreated(propertyId, msg.value);
    }

    /**
     * @dev Claims distributions for an investor
     * @param propertyId ID of the property
     */
    function claimDistributions(uint256 propertyId) external nonReentrant {
        uint256 totalClaim = 0;
        uint256 lastClaimedIndex = _claimedDistributions[msg.sender][propertyId];
        uint256 totalDistributions = _distributions[propertyId].length;

        // Calculate total claim amount
        for (uint256 i = lastClaimedIndex; i < totalDistributions; i++) {
            Distribution storage distribution = _distributions[propertyId][i];

            // Get investor's share of the property
            uint256 investorShares = _fractionalOwnership.balanceOf(msg.sender, propertyId);
            uint256 totalShares = _fractionalOwnership.getTotalShares(propertyId);

            // Calculate investor's portion of the distribution
            uint256 investorPortion = (distribution.totalAmount * investorShares) / totalShares;
            totalClaim += investorPortion;
        }

        require(totalClaim > 0, "No distributions to claim");

        // Update claimed distributions
        _claimedDistributions[msg.sender][propertyId] = totalDistributions;

        // Transfer claim amount to investor
        payable(msg.sender).transfer(totalClaim);

        // Reward investor with BCT tokens (1% of claim amount)
        uint256 rewardAmount = totalClaim / 100;
        _brickToken.mint(msg.sender, rewardAmount);

        emit DistributionClaimed(propertyId, msg.sender, totalClaim);
    }

    /**
     * @dev Sets the platform fee percentage
     * @param feePercentage New fee percentage (scaled by 10^2)
     */
    function setPlatformFeePercentage(uint256 feePercentage) external onlyOwner {
        require(feePercentage <= 1000, "Fee too high"); // Max 10%
        _platformFeePercentage = feePercentage;
    }

    /**
     * @dev Returns the platform fee percentage
     */
    function getPlatformFeePercentage() external view returns (uint256) {
        return _platformFeePercentage;
    }

    /**
     * @dev Returns all investments for a property
     * @param propertyId ID of the property
     */
    function getPropertyInvestments(uint256 propertyId) external view returns (Investment[] memory) {
        return _investments[propertyId];
    }

    /**
     * @dev Returns all distributions for a property
     * @param propertyId ID of the property
     */
    function getPropertyDistributions(uint256 propertyId) external view returns (Distribution[] memory) {
        return _distributions[propertyId];
    }

    /**
     * @dev Returns the unclaimed distribution amount for an investor
     * @param investor Address of the investor
     * @param propertyId ID of the property
     */
    function getUnclaimedAmount(address investor, uint256 propertyId) external view returns (uint256) {
        uint256 totalUnclaimed = 0;
        uint256 lastClaimedIndex = _claimedDistributions[investor][propertyId];
        uint256 totalDistributions = _distributions[propertyId].length;

        for (uint256 i = lastClaimedIndex; i < totalDistributions; i++) {
            Distribution storage distribution = _distributions[propertyId][i];

            // Get investor's share of the property
            uint256 investorShares = _fractionalOwnership.balanceOf(investor, propertyId);
            uint256 totalShares = _fractionalOwnership.getTotalShares(propertyId);

            // Calculate investor's portion of the distribution
            uint256 investorPortion = (distribution.totalAmount * investorShares) / totalShares;
            totalUnclaimed += investorPortion;
        }

        return totalUnclaimed;
    }

    /**
     * @dev Withdraws platform fees
     */
    function withdrawFees() external onlyOwner {
        payable(owner()).transfer(address(this).balance);
    }
}
