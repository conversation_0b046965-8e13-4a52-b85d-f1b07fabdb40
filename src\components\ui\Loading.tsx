'use client';

import React from 'react';
import { Loader2 } from 'lucide-react';
import { cn } from '@/utils/cn';

export interface LoadingProps {
  variant?: 'spinner' | 'dots' | 'pulse' | 'skeleton' | 'progress';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  text?: string;
  progress?: number; // 0-100 for progress variant
  className?: string;
  fullScreen?: boolean;
  overlay?: boolean;
}

const Loading: React.FC<LoadingProps> = ({
  variant = 'spinner',
  size = 'md',
  text,
  progress,
  className,
  fullScreen = false,
  overlay = false,
}) => {
  const sizes = {
    sm: { spinner: 16, text: 'text-sm', container: 'gap-2' },
    md: { spinner: 24, text: 'text-base', container: 'gap-3' },
    lg: { spinner: 32, text: 'text-lg', container: 'gap-4' },
    xl: { spinner: 48, text: 'text-xl', container: 'gap-5' },
  };

  const renderSpinner = () => (
    <Loader2 
      size={sizes[size].spinner} 
      className="animate-spin text-indigo-500" 
      aria-hidden="true"
    />
  );

  const renderDots = () => (
    <div className="flex space-x-1" aria-hidden="true">
      {[0, 1, 2].map((i) => (
        <div
          key={i}
          className={cn(
            'rounded-full bg-indigo-500',
            size === 'sm' ? 'w-2 h-2' : size === 'lg' ? 'w-4 h-4' : size === 'xl' ? 'w-5 h-5' : 'w-3 h-3'
          )}
          style={{
            animation: `loading-dots 1.4s ease-in-out infinite both`,
            animationDelay: `${i * 0.16}s`,
          }}
        />
      ))}
    </div>
  );

  const renderPulse = () => (
    <div
      className={cn(
        'rounded-full bg-indigo-500 animate-pulse',
        size === 'sm' ? 'w-8 h-8' : size === 'lg' ? 'w-16 h-16' : size === 'xl' ? 'w-20 h-20' : 'w-12 h-12'
      )}
      aria-hidden="true"
    />
  );

  const renderSkeleton = () => (
    <div className="space-y-3 w-full max-w-md" aria-hidden="true">
      <div className="h-4 bg-gray-700 rounded animate-pulse" />
      <div className="h-4 bg-gray-700 rounded animate-pulse w-5/6" />
      <div className="h-4 bg-gray-700 rounded animate-pulse w-4/6" />
    </div>
  );

  const renderProgress = () => (
    <div className="w-full max-w-md space-y-2" aria-hidden="true">
      <div className="flex justify-between text-sm text-gray-400">
        <span>Loading...</span>
        <span>{progress || 0}%</span>
      </div>
      <div className="w-full bg-gray-700 rounded-full h-2">
        <div
          className="bg-indigo-500 h-2 rounded-full transition-all duration-300 ease-out"
          style={{ width: `${progress || 0}%` }}
        />
      </div>
    </div>
  );

  const renderContent = () => {
    switch (variant) {
      case 'dots':
        return renderDots();
      case 'pulse':
        return renderPulse();
      case 'skeleton':
        return renderSkeleton();
      case 'progress':
        return renderProgress();
      default:
        return renderSpinner();
    }
  };

  const content = (
    <div
      className={cn(
        'flex flex-col items-center justify-center',
        sizes[size].container,
        variant === 'skeleton' ? 'w-full' : '',
        className
      )}
      role="status"
      aria-live="polite"
      aria-label={text || 'Loading'}
    >
      {renderContent()}
      {text && variant !== 'progress' && variant !== 'skeleton' && (
        <p className={cn('text-gray-300 font-medium', sizes[size].text)}>
          {text}
        </p>
      )}
    </div>
  );

  if (fullScreen) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-gray-950">
        {content}
      </div>
    );
  }

  if (overlay) {
    return (
      <div className="absolute inset-0 z-10 flex items-center justify-center bg-gray-950/80 backdrop-blur-sm">
        {content}
      </div>
    );
  }

  return content;
};

// Skeleton components for specific use cases
export const SkeletonCard: React.FC<{ className?: string }> = ({ className }) => (
  <div className={cn('p-4 space-y-3', className)} aria-hidden="true">
    <div className="h-4 bg-gray-700 rounded animate-pulse" />
    <div className="h-4 bg-gray-700 rounded animate-pulse w-5/6" />
    <div className="h-32 bg-gray-700 rounded animate-pulse" />
    <div className="flex space-x-2">
      <div className="h-8 bg-gray-700 rounded animate-pulse w-20" />
      <div className="h-8 bg-gray-700 rounded animate-pulse w-16" />
    </div>
  </div>
);

export const SkeletonText: React.FC<{ 
  lines?: number; 
  className?: string;
}> = ({ lines = 3, className }) => (
  <div className={cn('space-y-2', className)} aria-hidden="true">
    {Array.from({ length: lines }).map((_, i) => (
      <div
        key={i}
        className={cn(
          'h-4 bg-gray-700 rounded animate-pulse',
          i === lines - 1 ? 'w-3/4' : 'w-full'
        )}
      />
    ))}
  </div>
);

export default Loading;
