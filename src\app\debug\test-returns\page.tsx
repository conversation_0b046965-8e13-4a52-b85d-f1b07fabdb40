'use client';

import React, { useState, useEffect } from 'react';
import { createClient } from '@supabase/supabase-js';
import { uploadToIPFS, getIPFSUrl } from '@/utils/ipfs';
import Link from 'next/link';
import { Loader2, Cloud, AlertCircle, Check, Upload } from 'lucide-react';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';
const supabase = createClient(supabaseUrl, supabaseAnonKey);

export default function IPFSDebugPage() {
  const [nftStorageKey, setNftStorageKey] = useState('');
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');
  const [uploadResult, setUploadResult] = useState<any>(null);
  const [envVars, setEnvVars] = useState<{[key: string]: string | undefined}>({});

  useEffect(() => {
    // Check which environment variables are set
    setEnvVars({
      NEXT_PUBLIC_NFT_STORAGE_API_KEY: process.env.NEXT_PUBLIC_NFT_STORAGE_API_KEY,
      NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,
      NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    });
  }, []);

  useEffect(() => {
    if (imageFile) {
      const url = URL.createObjectURL(imageFile);
      setPreviewUrl(url);
      return () => URL.revokeObjectURL(url);
    }
  }, [imageFile]);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setImageFile(file);
      setError('');
      setMessage('');
    }
  };

  const clearImage = () => {
    setImageFile(null);
    setPreviewUrl(null);
  };

  const testIPFSUpload = async () => {
    if (!imageFile) {
      setError('Please select an image file first');
      return;
    }

    setLoading(true);
    setError('');
    setMessage('');
    setUploadResult(null);

    // Log the keys for debugging
    console.log('[Debug] NFT_STORAGE_API_KEY from process.env:', process.env.NEXT_PUBLIC_NFT_STORAGE_API_KEY);
    console.log('[Debug] NFT_STORAGE_API_KEY from temporary input:', nftStorageKey);

    try {
      // If a key was provided in the UI, set it temporarily for this function call
      let effectiveApiKey = process.env.NEXT_PUBLIC_NFT_STORAGE_API_KEY;
      if (nftStorageKey) {
        effectiveApiKey = nftStorageKey;
        console.log('[Debug] Using temporary API key for this upload:', effectiveApiKey);
      }

      // Temporarily modify process.env for the uploadToIPFS function if using temporary key
      // This is a workaround for how uploadToIPFS accesses the key directly.
      // Ideally, uploadToIPFS would accept the key as a parameter.
      let originalEnvKey = process.env.NEXT_PUBLIC_NFT_STORAGE_API_KEY;
      if (nftStorageKey) {
        // @ts-ignore
        process.env.NEXT_PUBLIC_NFT_STORAGE_API_KEY = nftStorageKey;
      }

      const ipfsResult = await uploadToIPFS(imageFile);
      
      // Restore original process.env key if it was changed
      if (nftStorageKey) {
        // @ts-ignore
        process.env.NEXT_PUBLIC_NFT_STORAGE_API_KEY = originalEnvKey;
      }

      setUploadResult(ipfsResult);
      
      if (ipfsResult.success) {
        setMessage(`Successfully uploaded to ${ipfsResult.error ? 'Supabase (fallback)' : 'IPFS (NFT.Storage)'}`);
      } else {
        throw new Error(ipfsResult.error || 'Upload failed');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-950 to-indigo-950 p-8 text-white">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-2">IPFS Configuration Test</h1>
        <p className="text-gray-400 mb-8">Test and debug your IPFS integration</p>
        
        <div className="bg-gray-900/50 p-6 rounded-lg border border-gray-800 mb-8">
          <h2 className="text-xl font-semibold mb-4 flex items-center">
            <Cloud className="mr-2" />
            Environment Configuration
          </h2>
          
          <div className="mb-6 grid gap-2">
            {Object.entries(envVars).map(([key, value]) => (
              <div key={key} className="flex items-center">
                <div className="bg-gray-800 rounded-l px-3 py-2 text-sm font-mono w-[300px] overflow-hidden overflow-ellipsis">
                  {key}
                </div>
                <div className="bg-gray-800/50 rounded-r px-3 py-2 text-sm flex-1 flex items-center">
                  {value ? (
                    <>
                      <span className="text-green-400 font-mono">
                        {key.includes('KEY') ? '************' : value}
                      </span>
                      <Check size={16} className="ml-2 text-green-400" />
                    </>
                  ) : (
                    <span className="text-red-400 italic">Not set</span>
                  )}
                </div>
              </div>
            ))}
          </div>
          
          <div className="mb-6">
            <h3 className="text-md font-semibold mb-2">Temporary NFT.Storage API Key</h3>
            <p className="text-sm text-gray-400 mb-2">
              Enter a key to test, this won't be saved and will only be used for this session
            </p>
            <input
              type="password"
              value={nftStorageKey}
              onChange={(e) => setNftStorageKey(e.target.value)}
              className="w-full p-2 bg-gray-800 border border-gray-700 rounded"
              placeholder="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            />
          </div>
        </div>
        
        <div className="bg-gray-900/50 p-6 rounded-lg border border-gray-800 mb-8">
          <h2 className="text-xl font-semibold mb-4">Test IPFS Upload</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              {/* Image upload area */}
              {previewUrl ? (
                <div className="relative mb-4">
                  <img 
                    src={previewUrl} 
                    alt="Test Image" 
                    className="w-full h-48 object-cover rounded-lg border border-gray-700"
                  />
                  <button
                    type="button"
                    onClick={clearImage}
                    className="absolute top-2 right-2 p-1 bg-red-600 rounded-full text-white hover:bg-red-700"
                  >
                    <span className="sr-only">Remove</span>
                    ✕
                  </button>
                </div>
              ) : (
                <div 
                  className="border-2 border-dashed border-gray-700 rounded-lg p-6 h-48 flex items-center justify-center"
                  onClick={() => document.getElementById('file-upload')?.click()}
                >
                  <div className="text-center">
                    <Upload className="mx-auto h-10 w-10 text-gray-500" />
                    <p className="mt-1 text-sm text-gray-400">Upload a test image</p>
                    <input
                      id="file-upload"
                      name="file-upload"
                      type="file"
                      className="sr-only"
                      accept="image/*"
                      onChange={handleImageChange}
                    />
                  </div>
                </div>
              )}
              
              <button
                onClick={testIPFSUpload}
                disabled={!imageFile || loading}
                className={`mt-4 w-full py-2 px-4 rounded-lg flex items-center justify-center ${
                  !imageFile 
                    ? 'bg-gray-700 text-gray-400 cursor-not-allowed' 
                    : 'bg-indigo-600 hover:bg-indigo-700 text-white'
                }`}
              >
                {loading ? (
                  <>
                    <Loader2 className="animate-spin h-4 w-4 mr-2" />
                    Uploading...
                  </>
                ) : (
                  <>
                    <Cloud className="mr-2 h-4 w-4" />
                    Test IPFS Upload
                  </>
                )}
              </button>
            </div>
            
            <div>
              {error && (
                <div className="p-3 bg-red-900/40 border border-red-800 rounded-lg mb-4">
                  <div className="flex">
                    <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
                    <p className="text-red-300 text-sm">{error}</p>
                  </div>
                </div>
              )}
              
              {message && (
                <div className="p-3 bg-green-900/40 border border-green-800 rounded-lg mb-4">
                  <div className="flex">
                    <Check className="h-5 w-5 text-green-500 mr-2" />
                    <p className="text-green-300 text-sm">{message}</p>
                  </div>
                </div>
              )}
              
              {uploadResult && (
                <div className="border border-gray-700 rounded-lg overflow-hidden">
                  <div className="bg-gray-800 px-4 py-2 font-semibold text-sm">
                    Upload Result
                  </div>
                  <div className="p-4 bg-gray-900">
                    <pre className="text-xs overflow-auto max-h-[200px] font-mono">
                      {JSON.stringify(uploadResult, null, 2)}
                    </pre>
                    
                    {uploadResult.url && (
                      <div className="mt-3">
                        <a 
                          href={uploadResult.url} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-indigo-400 hover:text-indigo-300 text-sm"
                        >
                          View Uploaded File
                        </a>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
        
        <div className="bg-gray-900/50 p-6 rounded-lg border border-gray-800">
          <h2 className="text-xl font-semibold mb-4">Setup Instructions</h2>
          
          <div className="space-y-4">
            <div>
              <h3 className="text-md font-semibold mb-2">1. Register on NFT.Storage</h3>
              <p className="text-sm text-gray-400">
                Visit <a href="https://nft.storage" target="_blank" rel="noopener noreferrer" className="text-indigo-400 hover:underline">https://nft.storage</a> and create an account
              </p>
            </div>
            
            <div>
              <h3 className="text-md font-semibold mb-2">2. Get an API Key</h3>
              <p className="text-sm text-gray-400">
                Once logged in, go to API Keys section and create a new API key
              </p>
            </div>
            
            <div>
              <h3 className="text-md font-semibold mb-2">3. Add to Environment Variables</h3>
              <p className="text-sm text-gray-400 mb-2">
                Add your API key to the project's environment variables:
              </p>
              <div className="bg-gray-800 p-3 rounded font-mono text-sm overflow-x-auto">
                NEXT_PUBLIC_NFT_STORAGE_API_KEY=your_api_key_here
              </div>
            </div>
            
            <div>
              <h3 className="text-md font-semibold mb-2">4. Restart Your Development Server</h3>
              <p className="text-sm text-gray-400">
                After adding the environment variables, restart your Next.js development server 
                to apply the changes
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-gray-900/50 p-6 rounded-lg border border-gray-800 mt-8">
          <h2 className="text-xl font-semibold mb-4">Advanced Testing</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-md font-semibold mb-2">Test Fallback Scenarios</h3>
              <p className="text-sm text-gray-400 mb-4">
                These buttons simulate different failure scenarios to test the fallback methods
              </p>
              
              <div className="space-y-2">
                <button
                  onClick={async () => {
                    if (!imageFile) {
                      setError('Please select an image file first');
                      return;
                    }
                    
                    setLoading(true);
                    setError('');
                    setMessage('');
                    
                    try {
                      // Temporarily clear NFT.Storage key to force fallback
                      const origKey = process.env.NEXT_PUBLIC_NFT_STORAGE_API_KEY;
                      // @ts-ignore
                      process.env.NEXT_PUBLIC_NFT_STORAGE_API_KEY = '';
                      
                      const result = await uploadToIPFS(imageFile);
                      
                      // Restore key
                      // @ts-ignore
                      process.env.NEXT_PUBLIC_NFT_STORAGE_API_KEY = origKey;
                      
                      setUploadResult(result);
                      setMessage('Tested Supabase fallback storage: ' + 
                        (result.success ? 'SUCCESS' : 'FAILED'));
                    } catch (err) {
                      setError(err instanceof Error ? err.message : 'Unknown error');
                    } finally {
                      setLoading(false);
                    }
                  }}
                  disabled={!imageFile || loading}
                  className="w-full py-2 px-4 bg-amber-600 hover:bg-amber-700 text-white rounded-lg flex items-center justify-center"
                >
                  Test Supabase Fallback
                </button>
                
                <button
                  onClick={async () => {
                    if (!imageFile) {
                      setError('Please select an image file first');
                      return;
                    }
                    
                    setLoading(true);
                    setError('');
                    setMessage('');
                    
                    try {
                      // Access the fileToDataUrl directly for testing
                      // @ts-ignore - Accessing internal function for testing
                      const dataUrl = await import('@/utils/ipfs').then(module => 
                        // Using Function to dynamically access the private function
                        new Function('file', 'module', 'return module.fileToDataUrl(file)')(imageFile, module)
                      );
                      
                      setUploadResult({
                        success: true,
                        url: dataUrl,
                        fallback: true,
                        error: 'Testing data URL fallback'
                      });
                      
                      setMessage('Tested Data URL fallback: SUCCESS');
                    } catch (err) {
                      setError(err instanceof Error ? err.message : 'Unknown error');
                    } finally {
                      setLoading(false);
                    }
                  }}
                  disabled={!imageFile || loading}
                  className="w-full py-2 px-4 bg-orange-600 hover:bg-orange-700 text-white rounded-lg flex items-center justify-center"
                >
                  Test Data URL Fallback
                </button>
              </div>
            </div>
            
            <div>
              <h3 className="text-md font-semibold mb-2">Debug Info</h3>
              <div className="bg-gray-800 p-3 rounded text-xs font-mono text-gray-300 overflow-auto max-h-[150px]">
                {Object.entries(envVars).map(([key, value]) => (
                  <div key={key} className="mb-1">
                    <span className="text-indigo-400">{key}:</span> {key.includes('KEY') 
                      ? (value ? '******' : 'not set') 
                      : (value || 'not set')}
                  </div>
                ))}
                
                <div className="mt-2 pt-2 border-t border-gray-700">
                  <span className="text-indigo-400">Browser:</span> {navigator.userAgent}
                </div>
              </div>
              
              <div className="mt-4">
                <button
                  onClick={() => {
                    // Log for debugging
                    console.log('Environment variables:', envVars);
                    console.log('Upload result:', uploadResult);
                    
                    // Show success message
                    setMessage('Debug info logged to console');
                  }}
                  className="w-full py-2 px-4 bg-gray-600 hover:bg-gray-700 text-white rounded-lg"
                >
                  Log Debug Info to Console
                </button>
              </div>
            </div>
          </div>
        </div>
        
        <div className="mt-6 flex justify-between">
          <Link href="/debug" className="text-indigo-400 hover:text-indigo-300">
            Back to Debug Dashboard
          </Link>
        </div>
      </div>
    </div>
  );
} 