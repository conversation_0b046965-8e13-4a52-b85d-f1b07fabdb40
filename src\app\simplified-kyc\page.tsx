'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useWalletAuth } from '@/context/WalletAuthContext';
import { Loader2 } from 'lucide-react';
import SimplifiedKYC from '@/components/SimplifiedKYC';

export default function KYCVerificationPage() {
  const router = useRouter();
  const { isAuthenticated, loading: authLoading } = useWalletAuth();

  // Redirect if not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/');
    }
  }, [authLoading, isAuthenticated, router]);

  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-900 to-indigo-950">
        <Loader2 className="h-12 w-12 text-indigo-500 animate-spin" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 to-indigo-950 py-12 px-6">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-white mb-4">KYC Verification</h1>
          <p className="text-xl text-gray-300">
            Complete your identity verification to unlock all platform features
          </p>
        </div>

        <SimplifiedKYC />
      </div>
    </div>
  );
}
