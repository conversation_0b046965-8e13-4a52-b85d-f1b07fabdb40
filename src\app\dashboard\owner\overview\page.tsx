'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@supabase/supabase-js';
import { Home, DollarSign, TrendingUp, Users, Loader2 } from 'lucide-react';
import LoadingSpinner from '@/components/LoadingSpinner';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

type Investment = {
  amount: number;
};

type Return = {
  amount: number;
};

type Property = {
  id: string;
  name: string;
  price: number;
  status: string;
  created_at: string;
  investments: Investment[];
  returns: Return[];
};

type DashboardStats = {
  totalProperties: number;
  totalInvestments: number;
  totalReturns: number;
  totalInvestors: number;
  recentProperties: {
    id: string;
    name: string;
    price: number;
    status: string;
    total_invested: number;
    total_returns: number;
  }[];
  recentInvestments: {
    id: string;
    property_name: string;
    amount: number;
    date: string;
    investor_email: string;
  }[];
};

type PropertyWithStats = {
  id: string;
  name: string;
  price: number;
  status: string;
  total_invested: number;
  total_returns: number;
  created_at: string;
};

type InvestmentWithRelations = {
  id: string;
  amount: number;
  created_at: string;
  properties: {
    name: string;
  };
  profiles: {
    email: string;
  };
};

export default function OwnerOverviewPage() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const fetchDashboardStats = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      // Fetch properties
      const { data: properties, error: propertiesError } = await supabase
        .from('properties')
        .select(`
          *,
          investments:investments(amount),
          returns:returns(amount)
        `)
        .eq('owner_id', user.id);

      if (propertiesError) throw propertiesError;

      // Calculate property stats
      const totalProperties = properties?.length || 0;
      const totalInvestments = properties?.reduce((sum: number, property: Property) => 
        sum + (property.investments?.reduce((s: number, inv: Investment) => s + (inv.amount || 0), 0) || 0), 0) || 0;
      const totalReturns = properties?.reduce((sum: number, property: Property) => 
        sum + (property.returns?.reduce((s: number, ret: Return) => s + (ret.amount || 0), 0) || 0), 0) || 0;

      // Fetch unique investors
      const { data: investors, error: investorsError } = await supabase
        .from('investments')
        .select('investor_id')
        .eq('property_id', properties?.map(p => p.id))
        .limit(1000); // Use limit instead of distinct

      if (investorsError) throw investorsError;

      // Get unique investor count
      const uniqueInvestors = new Set(investors?.map(inv => inv.investor_id)).size;

      // Fetch recent investments
      const { data: recentInvestments, error: investmentsError } = await supabase
        .from('investments')
        .select(`
          id,
          amount,
          created_at,
          properties(name),
          profiles(email)
        `)
        .eq('property_id', properties?.map(p => p.id))
        .order('created_at', { ascending: false })
        .limit(5);

      if (investmentsError) throw investmentsError;

      // Format recent properties
      const recentProperties = (properties as Property[])
        ?.map(property => ({
          id: property.id,
          name: property.name,
          price: property.price,
          status: property.status,
          total_invested: property.investments?.reduce((sum: number, inv: Investment) => sum + (inv.amount || 0), 0) || 0,
          total_returns: property.returns?.reduce((sum: number, ret: Return) => sum + (ret.amount || 0), 0) || 0,
          created_at: property.created_at
        }))
        .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
        .slice(0, 5) || [];

      setStats({
        totalProperties,
        totalInvestments,
        totalReturns,
        totalInvestors: uniqueInvestors,
        recentProperties,
        recentInvestments: recentInvestments?.map(inv => ({
          id: inv.id,
          property_name: inv.properties[0]?.name || 'Unknown Property',
          amount: inv.amount,
          date: inv.created_at,
          investor_email: inv.profiles[0]?.email || 'Unknown Investor'
        })) || []
      });
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      setError('Failed to load dashboard statistics');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-red-500">{error}</div>
      </div>
    );
  }

  if (!stats) return null;

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Dashboard Overview</h1>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Total Properties</p>
              <p className="text-2xl font-bold">{stats.totalProperties}</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-full">
              <Home className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Total Investments</p>
              <p className="text-2xl font-bold">${stats.totalInvestments.toLocaleString()}</p>
            </div>
            <div className="p-3 bg-green-100 rounded-full">
              <DollarSign className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Total Returns</p>
              <p className="text-2xl font-bold">${stats.totalReturns.toLocaleString()}</p>
            </div>
            <div className="p-3 bg-purple-100 rounded-full">
              <TrendingUp className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Total Investors</p>
              <p className="text-2xl font-bold">{stats.totalInvestors}</p>
            </div>
            <div className="p-3 bg-yellow-100 rounded-full">
              <Users className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Properties */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-6 border-b">
            <h2 className="text-lg font-semibold">Recent Properties</h2>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {stats.recentProperties.map((property) => (
                <div key={property.id} className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">{property.name}</p>
                    <p className="text-sm text-gray-500">${property.price.toLocaleString()}</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      property.status === 'available' ? 'bg-green-100 text-green-800' :
                      property.status === 'sold' ? 'bg-red-100 text-red-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {property.status}
                    </span>
                    <div className="text-right">
                      <p className="text-sm font-medium">${property.total_invested.toLocaleString()}</p>
                      <p className="text-xs text-green-600">${property.total_returns.toLocaleString()} returns</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Recent Investments */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-6 border-b">
            <h2 className="text-lg font-semibold">Recent Investments</h2>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {stats.recentInvestments.map((investment) => (
                <div key={investment.id} className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">{investment.property_name}</p>
                    <p className="text-sm text-gray-500">{investment.investor_email}</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">${investment.amount.toLocaleString()}</p>
                    <p className="text-sm text-gray-500">
                      {new Date(investment.date).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 