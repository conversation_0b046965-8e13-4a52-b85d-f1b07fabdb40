// Follow this setup guide to integrate the Deno runtime into your Supabase project:
// https://supabase.com/docs/guides/functions/deno-runtime

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { SiweMessage } from 'https://esm.sh/siwe@2';
import { verifyMessage } from 'https://esm.sh/@ethersproject/wallet@5.7.0';

interface RequestBody {
  message: string;
  signature: string;
  wallet_address: string;
}

serve(async (req) => {
  try {
    // Create a Supabase client with the Auth context of the function
    const supabaseClient = createClient(
      // Supabase API URL - env var exported by default.
      Deno.env.get('SUPABASE_URL') ?? '',
      // Supabase API ANON KEY - env var exported by default.
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      // Create client with Auth context of the user that called the function.
      { global: { headers: { Authorization: req.headers.get('Authorization')! } } }
    );

    // Parse request body
    const { message, signature, wallet_address } = await req.json() as RequestBody;

    // Validate inputs
    if (!message || !signature || !wallet_address) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields' }),
        { headers: { 'Content-Type': 'application/json' }, status: 400 }
      );
    }

    // Verify the signature
    try {
      // Parse the SIWE message
      const siweMessage = new SiweMessage(message);
      
      // Verify that the address in the message matches the provided wallet address
      if (siweMessage.address.toLowerCase() !== wallet_address.toLowerCase()) {
        return new Response(
          JSON.stringify({ error: 'Address mismatch' }),
          { headers: { 'Content-Type': 'application/json' }, status: 400 }
        );
      }

      // Verify the signature using ethers.js
      const recoveredAddress = verifyMessage(siweMessage.prepareMessage(), signature);
      
      if (recoveredAddress.toLowerCase() !== wallet_address.toLowerCase()) {
        return new Response(
          JSON.stringify({ error: 'Invalid signature' }),
          { headers: { 'Content-Type': 'application/json' }, status: 400 }
        );
      }
    } catch (error) {
      console.error('Error verifying signature:', error);
      return new Response(
        JSON.stringify({ error: 'Failed to verify signature' }),
        { headers: { 'Content-Type': 'application/json' }, status: 400 }
      );
    }

    // Check if a user with this wallet address already exists
    const { data: existingUsers, error: queryError } = await supabaseClient
      .from('profiles')
      .select('id')
      .eq('wallet_address', wallet_address.toLowerCase())
      .limit(1);

    if (queryError) {
      console.error('Error querying profiles:', queryError);
      return new Response(
        JSON.stringify({ error: 'Database query error' }),
        { headers: { 'Content-Type': 'application/json' }, status: 500 }
      );
    }

    let userId;
    let session;

    if (existingUsers && existingUsers.length > 0) {
      // User exists, sign them in
      userId = existingUsers[0].id;
      
      // Get the auth user associated with this profile
      const { data: authUser, error: authUserError } = await supabaseClient.auth.admin.getUserById(userId);
      
      if (authUserError || !authUser) {
        console.error('Error getting auth user:', authUserError);
        return new Response(
          JSON.stringify({ error: 'Failed to get auth user' }),
          { headers: { 'Content-Type': 'application/json' }, status: 500 }
        );
      }
      
      // Create a new session for the user
      const { data: sessionData, error: sessionError } = await supabaseClient.auth.admin.createSession({
        user_id: userId
      });
      
      if (sessionError) {
        console.error('Error creating session:', sessionError);
        return new Response(
          JSON.stringify({ error: 'Failed to create session' }),
          { headers: { 'Content-Type': 'application/json' }, status: 500 }
        );
      }
      
      session = sessionData;
    } else {
      // User doesn't exist, create a new one
      const { data: newUser, error: createUserError } = await supabaseClient.auth.admin.createUser({
        email: `${wallet_address.toLowerCase()}@wallet.brickchain.com`,
        email_confirm: true,
        user_metadata: {
          wallet_address: wallet_address.toLowerCase()
        }
      });
      
      if (createUserError || !newUser) {
        console.error('Error creating user:', createUserError);
        return new Response(
          JSON.stringify({ error: 'Failed to create user' }),
          { headers: { 'Content-Type': 'application/json' }, status: 500 }
        );
      }
      
      userId = newUser.user.id;
      
      // Create a profile for the new user
      const { error: createProfileError } = await supabaseClient
        .from('profiles')
        .insert([
          {
            id: userId,
            wallet_address: wallet_address.toLowerCase(),
            kyc_status: 'none',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }
        ]);
      
      if (createProfileError) {
        console.error('Error creating profile:', createProfileError);
        return new Response(
          JSON.stringify({ error: 'Failed to create profile' }),
          { headers: { 'Content-Type': 'application/json' }, status: 500 }
        );
      }
      
      // Create a session for the new user
      const { data: sessionData, error: sessionError } = await supabaseClient.auth.admin.createSession({
        user_id: userId
      });
      
      if (sessionError) {
        console.error('Error creating session:', sessionError);
        return new Response(
          JSON.stringify({ error: 'Failed to create session' }),
          { headers: { 'Content-Type': 'application/json' }, status: 500 }
        );
      }
      
      session = sessionData;
    }

    // Return the session
    return new Response(
      JSON.stringify({ session }),
      { headers: { 'Content-Type': 'application/json' }, status: 200 }
    );
  } catch (error) {
    console.error('Unexpected error:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { headers: { 'Content-Type': 'application/json' }, status: 500 }
    );
  }
});
