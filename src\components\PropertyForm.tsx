import { useState, useEffect } from 'react';
import { useSupabaseClient } from '@supabase/auth-helpers-react';
import type { SupabaseClient } from '@supabase/supabase-js';
import { Building, Upload, X, Loader2, AlertCircle, Cloud } from 'lucide-react';
import { uploadToIPFS, getIPFSUrl } from '@/utils/ipfs';
import PropertyImageUploader from './PropertyImageUploader';
import { PropertyImage } from '@/utils/fileStorage';
import { Form, FormField, FormLabel, FormControl, FormMessage, FormActions, FormGrid, FormSection } from '@/components/ui/Form';
import Input from '@/components/ui/Input';
import Button from '@/components/ui/Button';
import { useToast } from '@/context/ToastContext';

// Define types
type PropertyStatus = 'draft' | 'available' | 'pending' | 'funded' | 'sold' | 'archived';

export type Property = {
  id?: string;
  name: string;
  location: string;
  price: number;
  return_rate: number;
  description: string;
  status: PropertyStatus;
  image_url?: string;
  image_cid?: string; // IPFS CID for the image
  published?: boolean;
  owner_id?: string;
  property_images?: PropertyImage[]; // Array of property images
};

type PropertyFormProps = {
  property?: Property;
  onSave: (property: Property) => Promise<void>;
  onCancel: () => void;
  isSubmitting?: boolean;
  theme?: 'light' | 'dark';
};

const defaultProperty: Property = {
  name: '',
  location: '',
  price: 0,
  return_rate: 8,
  description: '',
  status: 'draft',
  published: false,
};

export default function PropertyForm({
  property,
  onSave,
  onCancel,
  isSubmitting = false,
  theme = 'light'
}: PropertyFormProps) {
  const supabase = useSupabaseClient<SupabaseClient>();
  const { success, error: showError } = useToast();

  const [formData, setFormData] = useState<Property>(property || defaultProperty);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [validation, setValidation] = useState<Record<string, string>>({});
  const [uploadingToIPFS, setUploadingToIPFS] = useState(false);
  const [ipfsStatus, setIpfsStatus] = useState<string>('');
  const [propertyImages, setPropertyImages] = useState<PropertyImage[]>([]);



  // Initialize form data when property changes
  useEffect(() => {
    if (property) {
      setFormData(property);
      setPreviewUrl(property.image_url || null);

      // Fetch property images if we have a property ID
      if (property.id) {
        const fetchPropertyImages = async () => {
          try {
            const { data: images, error } = await supabase
              .from('property_images')
              .select('*')
              .eq('property_id', property.id)
              .order('sort_order', { ascending: true });

            if (error) {
              console.error('Error fetching property images:', error);
              return;
            }

            if (images && images.length > 0) {
              setPropertyImages(images);
            }
          } catch (err) {
            console.error('Error fetching property images:', err);
          }
        };

        fetchPropertyImages();
      }
    } else {
      setFormData(defaultProperty);
      setPreviewUrl(null);
      setPropertyImages([]);
    }
  }, [property, supabase]);

  // Create preview URL for selected image
  useEffect(() => {
    if (imageFile) {
      const url = URL.createObjectURL(imageFile);
      setPreviewUrl(url);
      return () => URL.revokeObjectURL(url);
    }
  }, [imageFile]);

  // Use separate handlers for input/textarea and checkbox
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    // Handle special types
    if (name === 'price' || name === 'return_rate') {
      const numValue = parseFloat(value);
      setFormData(prev => ({ ...prev, [name]: isNaN(numValue) ? 0 : numValue }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }

    // Clear validation error for this field
    if (validation[name]) {
      setValidation(prev => {
        const updated = { ...prev };
        delete updated[name];
        return updated;
      });
    }
  };

  // Handle checkbox change separately
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData(prev => ({ ...prev, [name]: checked }));

    // Clear validation error for this field
    if (validation[name]) {
      setValidation(prev => {
        const updated = { ...prev };
        delete updated[name];
        return updated;
      });
    }
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setImageFile(file);
      // Clear validation error
      if (validation['image_url']) {
        setValidation(prev => {
          const updated = { ...prev };
          delete updated['image_url'];
          return updated;
        });
      }
    }
  };

  const clearImage = () => {
    setImageFile(null);
    if (!property?.image_url) {
      setPreviewUrl(null);
    } else {
      setPreviewUrl(property.image_url);
    }
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.name.trim()) {
      errors.name = 'Name is required';
    }

    if (!formData.location.trim()) {
      errors.location = 'Location is required';
    }

    if (formData.price <= 0) {
      errors.price = 'Price must be greater than 0';
    }

    if (formData.return_rate <= 0) {
      errors.return_rate = 'Return rate must be greater than 0';
    }

    // Check if we have at least one image (either the main image or in propertyImages)
    if (!property?.image_url && !imageFile && propertyImages.length === 0) {
      errors.image_url = 'At least one property image is required';
    }

    setValidation(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      // Validate form
      if (!validateForm()) {
        showError('Validation Error', 'Please fix the errors below and try again.');
        return;
      }

      // Make a copy of form data to submit
      const submissionData = { ...formData };

      // Include property images in the submission data
      submissionData.property_images = propertyImages;

      // If there's a new image file, upload it to IPFS
      if (imageFile) {
        setUploadingToIPFS(true);
        setIpfsStatus('Uploading to IPFS...');

        // Upload to IPFS
        const ipfsResult = await uploadToIPFS(imageFile);

        if (!ipfsResult.success) {
          throw new Error(`Failed to upload image: ${ipfsResult.error || 'Unknown error'}`);
        }

        // Set appropriate status message based on whether we used fallback
        if (ipfsResult.fallback) {
          setIpfsStatus('Image uploaded using fallback storage');
          showError('Upload Warning', 'Image uploaded using fallback storage. IPFS may be unavailable.');
        } else {
          setIpfsStatus('Successfully uploaded to IPFS!');
          success('Image Uploaded', 'Property image successfully uploaded to IPFS.');
        }

        // Store both the CID and the gateway URL
        submissionData.image_cid = ipfsResult.cid;
        submissionData.image_url = ipfsResult.url;

        setUploadingToIPFS(false);
      }

      // Save the property (this function is passed from parent)
      await onSave(submissionData);

      // Show success message
      success(
        property?.id ? 'Property Updated' : 'Property Created',
        property?.id
          ? 'Your property has been successfully updated.'
          : 'Your property has been successfully created.'
      );

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      showError('Save Failed', errorMessage);
      setUploadingToIPFS(false);
    }
  };

  return (
    <Form onSubmit={handleSubmit}>
      <FormGrid columns={2}>
        <FormSection title="Property Details" description="Basic information about the property">
          <FormField>
            <FormLabel required>Property Name</FormLabel>
            <FormControl>
              <Input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                error={validation.name}
                placeholder="e.g. Oceanview Apartment"
              />
            </FormControl>
            {validation.name && (
              <FormMessage type="error">{validation.name}</FormMessage>
            )}
          </FormField>

          <FormField>
            <FormLabel required>Location</FormLabel>
            <FormControl>
              <Input
                type="text"
                name="location"
                value={formData.location}
                onChange={handleInputChange}
                error={validation.location}
                placeholder="e.g. 123 Main St, San Francisco, CA"
              />
            </FormControl>
            {validation.location && (
              <FormMessage type="error">{validation.location}</FormMessage>
            )}
          </FormField>

          <FormGrid columns={2}>
            <FormField>
              <FormLabel required>Price ($)</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  name="price"
                  value={formData.price === 0 ? '' : formData.price}
                  onChange={handleInputChange}
                  error={validation.price}
                  placeholder="e.g. 500000"
                  min="0"
                  step="1000"
                />
              </FormControl>
              {validation.price && (
                <FormMessage type="error">{validation.price}</FormMessage>
              )}
            </FormField>

            <FormField>
              <FormLabel required>Return Rate (%)</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  name="return_rate"
                  value={formData.return_rate === 0 ? '' : formData.return_rate}
                  onChange={handleInputChange}
                  error={validation.return_rate}
                  placeholder="e.g. 8"
                  min="0"
                  max="100"
                  step="0.1"
                />
              </FormControl>
              {validation.return_rate && (
                <FormMessage type="error">{validation.return_rate}</FormMessage>
              )}
            </FormField>
          </FormGrid>

          <FormGrid columns={2}>
            <FormField>
              <FormLabel>Status</FormLabel>
              <FormControl>
                <select
                  name="status"
                  value={formData.status}
                  onChange={handleInputChange}
                  className="w-full p-3 border border-indigo-700 rounded-lg focus:ring-2 focus:ring-indigo-500 bg-indigo-800/50 text-white"
                >
                  <option value="draft">Draft</option>
                  <option value="available">Available</option>
                  <option value="pending">Pending</option>
                  <option value="funded">Funded</option>
                  <option value="sold">Sold</option>
                  <option value="archived">Archived</option>
                </select>
              </FormControl>
            </FormField>

            <FormField>
              <FormLabel>Published</FormLabel>
              <FormControl>
                <div className="flex items-center pt-2">
                  <input
                    type="checkbox"
                    id="published"
                    name="published"
                    checked={!!formData.published}
                    onChange={handleCheckboxChange}
                    className="h-4 w-4 rounded border-indigo-700 text-indigo-600 focus:ring-indigo-500 bg-indigo-800/50"
                  />
                  <label htmlFor="published" className="ml-2 block text-sm text-gray-300">
                    Published (visible to all)
                  </label>
                </div>
              </FormControl>
            </FormField>
          </FormGrid>

          <FormField>
            <FormLabel>Description</FormLabel>
            <FormControl>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={4}
                className="w-full p-3 border border-indigo-700 rounded-lg focus:ring-2 focus:ring-indigo-500 bg-indigo-800/50 text-white placeholder-indigo-300"
                placeholder="Describe the property, features, investment opportunity, etc."
              />
            </FormControl>
          </FormField>
        </FormSection>

        <FormSection title="Property Images" description="Upload images to showcase your property">
          <FormField>
            <FormLabel required>Property Image</FormLabel>
            {validation.image_url && (
              <FormMessage type="error">{validation.image_url}</FormMessage>
            )}

            {/* IPFS Status */}
            {ipfsStatus && (
              <div className="mb-3 p-2 bg-indigo-900/30 border border-indigo-800 rounded-md flex items-center">
                <Cloud size={16} className="text-indigo-400 mr-2" />
                <span className="text-indigo-300 text-xs">{ipfsStatus}</span>
              </div>
            )}

            <FormControl>
              {/* Image preview */}
              {previewUrl ? (
                <div className="relative mb-4">
                  <img
                    src={previewUrl}
                    alt="Property Preview"
                    className="w-full h-64 object-cover rounded-lg border border-indigo-700"
                  />
                  {/* Only show remove button for newly uploaded images */}
                  {(imageFile || (previewUrl !== property?.image_url)) && (
                    <button
                      type="button"
                      onClick={clearImage}
                      className="absolute top-2 right-2 p-1 bg-red-600 rounded-full text-white hover:bg-red-700"
                    >
                      <X size={16} />
                    </button>
                  )}

                  {/* IPFS Badge for images on IPFS */}
                  {formData.image_cid && (
                    <div className="absolute top-2 left-2">
                      <span className={`px-2 py-1 rounded-full text-xs font-semibold flex items-center ${
                        formData.image_cid?.startsWith('supabase')
                          ? 'bg-yellow-900/50 text-yellow-300'
                          : 'bg-indigo-900/50 text-indigo-300'
                      }`}>
                        <Cloud size={12} className="mr-1" />
                        {formData.image_cid?.startsWith('supabase')
                          ? 'Fallback Storage'
                          : 'Stored on IPFS'}
                      </span>
                    </div>
                  )}
                </div>
              ) : (
                <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-indigo-700 border-dashed rounded-lg h-64">
                  <div className="space-y-1 text-center flex flex-col items-center justify-center">
                    <Building size={48} className="mx-auto text-gray-400" />
                    <div className="text-sm text-gray-300">
                      <label
                        htmlFor="file-upload"
                        className="relative cursor-pointer rounded-md font-medium text-indigo-400 hover:text-indigo-300"
                      >
                        <span>Upload an image</span>
                        <input
                          id="file-upload"
                          name="file-upload"
                          type="file"
                          className="sr-only"
                          accept="image/*"
                          onChange={handleImageChange}
                        />
                      </label>
                      <p className="pl-1">or drag and drop</p>
                    </div>
                    <p className="text-xs text-gray-400">PNG, JPG, GIF up to 10MB</p>
                    <div className="mt-2 flex items-center text-xs text-indigo-400">
                      <Cloud size={14} className="mr-1" />
                      <span>Will be stored on IPFS</span>
                    </div>
                  </div>
                </div>
              )}
            </FormControl>
          </FormField>

          {/* IPFS Info */}
          {formData.image_cid && (
            <div className="p-3 bg-indigo-900/30 border border-indigo-800 rounded-lg">
              <h4 className="text-sm font-medium text-indigo-300 mb-1 flex items-center">
                <Cloud size={16} className="mr-1" />
                IPFS Information
              </h4>
              <div className="text-xs text-gray-300 font-mono bg-gray-800/50 p-2 rounded overflow-auto mb-1">
                <span className="text-indigo-400">CID:</span> {formData.image_cid}
              </div>
              <a
                href={formData.image_url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-xs text-indigo-400 hover:text-indigo-300"
              >
                View on IPFS Gateway
              </a>
            </div>
          )}

          {/* Additional Property Images */}
          {property?.id && (
            <div className="mt-6">
              <h3 className="text-lg font-medium text-white mb-2">
                Additional Property Images
              </h3>
              <PropertyImageUploader
                propertyId={property.id}
                onImagesChange={(images) => setPropertyImages(images)}
                maxImages={10}
                allowedTypes={['primary', 'additional', 'floorplan', 'document']}
                theme={theme}
              />
            </div>
          )}
        </FormSection>
      </FormGrid>

      {/* Form Actions */}
      <FormActions align="right">
        <Button
          type="button"
          variant="secondary"
          onClick={onCancel}
          disabled={isSubmitting || uploadingToIPFS}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          variant="primary"
          loading={isSubmitting || uploadingToIPFS}
          loadingText={uploadingToIPFS ? 'Uploading to IPFS...' : 'Saving...'}
          icon={uploadingToIPFS ? <Cloud size={18} /> : undefined}
        >
          {property?.id ? 'Update Property' : 'Create Property'}
        </Button>
      </FormActions>
    </Form>
  );
}