'use client';

import { useState, useEffect } from 'react';
import { X, Wallet, Shield, FileCheck, ArrowRight } from 'lucide-react';
import { useLocalStorage } from '@/hooks/useLocalStorage';

interface WelcomeModalProps {
  onClose: () => void;
}

export default function WelcomeModal({ onClose }: WelcomeModalProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const [isVisible, setIsVisible] = useState(false);
  const totalSteps = 3;

  useEffect(() => {
    // Animate in
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 100);
    
    return () => clearTimeout(timer);
  }, []);

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(onClose, 300); // Wait for animation to complete
  };

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    } else {
      handleClose();
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  return (
    <div 
      className={`fixed inset-0 z-50 flex items-center justify-center p-4 transition-opacity duration-300 ${
        isVisible ? 'opacity-100' : 'opacity-0'
      }`}
    >
      <div className="absolute inset-0 bg-black/70" onClick={handleClose} />
      
      <div className="relative bg-gradient-to-br from-gray-900 to-indigo-950 rounded-xl border border-indigo-800 shadow-2xl max-w-2xl w-full overflow-hidden transform transition-all duration-300">
        {/* Close button */}
        <button 
          onClick={handleClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-white transition-colors z-10"
        >
          <X className="h-6 w-6" />
        </button>
        
        {/* Progress indicator */}
        <div className="absolute top-4 left-4 flex space-x-2">
          {Array.from({ length: totalSteps }).map((_, index) => (
            <div 
              key={index}
              className={`h-2 w-8 rounded-full transition-colors ${
                index + 1 <= currentStep ? 'bg-indigo-500' : 'bg-gray-700'
              }`}
            />
          ))}
        </div>
        
        {/* Content */}
        <div className="p-8 pt-16">
          {currentStep === 1 && (
            <div className="text-center">
              <div className="w-20 h-20 bg-gradient-to-r from-cyan-400 to-purple-600 rounded-xl shadow-xl flex items-center justify-center mb-6 mx-auto">
                <span className="text-white text-3xl font-black">BC</span>
              </div>
              <h2 className="text-3xl font-bold text-white mb-4">Welcome to BrickChain</h2>
              <p className="text-gray-300 mb-8 max-w-md mx-auto">
                We&apos;ve updated our platform to use wallet-based authentication for enhanced security and seamless blockchain integration.
              </p>
              
              <div className="flex justify-center mb-8">
                <Wallet className="h-16 w-16 text-indigo-400" />
              </div>
              
              <div className="bg-indigo-900/40 border border-indigo-800 rounded-lg p-4 mb-6 text-left">
                <h3 className="font-medium text-white mb-2">What is wallet-based authentication?</h3>
                <p className="text-gray-300 text-sm">
                  Instead of traditional username/password login, you&apos;ll connect your crypto wallet (like MetaMask) to access the platform. This provides better security and enables blockchain transactions.
                </p>
              </div>
            </div>
          )}
          
          {currentStep === 2 && (
            <div className="text-center">
              <div className="flex justify-center mb-6">
                <Shield className="h-16 w-16 text-indigo-400" />
              </div>
              <h2 className="text-3xl font-bold text-white mb-4">Getting Started</h2>
              <p className="text-gray-300 mb-6 max-w-md mx-auto">
                Follow these simple steps to start using BrickChain with your wallet:
              </p>
              
              <div className="space-y-4 text-left mb-6">
                <div className="flex items-start">
                  <div className="w-8 h-8 rounded-full bg-indigo-600 flex items-center justify-center mr-3 shrink-0">
                    <span className="text-white font-bold">1</span>
                  </div>
                  <div>
                    <h3 className="font-medium text-white">Connect Your Wallet</h3>
                    <p className="text-gray-300 text-sm">Click the &quot;Connect Wallet&quot; button and select your preferred wallet provider.</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="w-8 h-8 rounded-full bg-indigo-600 flex items-center justify-center mr-3 shrink-0">
                    <span className="text-white font-bold">2</span>
                  </div>
                  <div>
                    <h3 className="font-medium text-white">Sign Message</h3>
                    <p className="text-gray-300 text-sm">Sign a message to verify your wallet ownership (this doesn&apos;t cost any gas fees).</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="w-8 h-8 rounded-full bg-indigo-600 flex items-center justify-center mr-3 shrink-0">
                    <span className="text-white font-bold">3</span>
                  </div>
                  <div>
                    <h3 className="font-medium text-white">Complete KYC</h3>
                    <p className="text-gray-300 text-sm">Complete the KYC verification process to unlock all platform features.</p>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {currentStep === 3 && (
            <div className="text-center">
              <div className="flex justify-center mb-6">
                <FileCheck className="h-16 w-16 text-indigo-400" />
              </div>
              <h2 className="text-3xl font-bold text-white mb-4">Don&apos;t Have a Wallet?</h2>
              <p className="text-gray-300 mb-6 max-w-md mx-auto">
                If you don&apos;t have a crypto wallet yet, here are some popular options to get started:
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div className="bg-indigo-900/40 border border-indigo-800 rounded-lg p-4 text-left">
                  <h3 className="font-medium text-white mb-2">MetaMask</h3>
                  <p className="text-gray-300 text-sm mb-3">
                    The most popular Ethereum wallet, available as a browser extension and mobile app.
                  </p>
                  <a 
                    href="https://metamask.io/download/" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-indigo-400 hover:text-indigo-300 text-sm flex items-center"
                  >
                    Get MetaMask <ArrowRight className="h-3 w-3 ml-1" />
                  </a>
                </div>
                
                <div className="bg-indigo-900/40 border border-indigo-800 rounded-lg p-4 text-left">
                  <h3 className="font-medium text-white mb-2">Coinbase Wallet</h3>
                  <p className="text-gray-300 text-sm mb-3">
                    User-friendly wallet from Coinbase, great for beginners.
                  </p>
                  <a 
                    href="https://www.coinbase.com/wallet" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-indigo-400 hover:text-indigo-300 text-sm flex items-center"
                  >
                    Get Coinbase Wallet <ArrowRight className="h-3 w-3 ml-1" />
                  </a>
                </div>
              </div>
              
              <p className="text-gray-400 text-sm mb-6">
                After setting up your wallet, return to BrickChain and click &quot;Connect Wallet&quot; to get started.
              </p>
            </div>
          )}
        </div>
        
        {/* Footer */}
        <div className="p-6 border-t border-indigo-800 flex justify-between">
          <button
            onClick={prevStep}
            className={`px-4 py-2 rounded-lg text-white transition-colors ${
              currentStep === 1 
                ? 'opacity-0 pointer-events-none' 
                : 'bg-gray-800 hover:bg-gray-700'
            }`}
          >
            Back
          </button>
          
          <button
            onClick={nextStep}
            className="px-6 py-2 bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 rounded-lg text-white font-medium"
          >
            {currentStep === totalSteps ? 'Get Started' : 'Next'}
          </button>
        </div>
      </div>
    </div>
  );
}
