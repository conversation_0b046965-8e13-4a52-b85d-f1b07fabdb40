'use client';

import React from 'react';
import { Building, Wallet, TrendingUp, Users, Plus } from 'lucide-react';
import Link from 'next/link';

export default function ExampleDashboardPage() {
  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-white mb-2">Dashboard Example</h1>
        <p className="text-gray-400">
          This page demonstrates the new unified navigation system for both owner and investor dashboards.
        </p>
      </div>

      {/* Example Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-indigo-900/30 border border-indigo-800 rounded-xl p-6">
          <div className="flex items-start justify-between">
            <div>
              <p className="text-gray-400">Properties</p>
              <h3 className="text-2xl font-bold text-white mt-1">12</h3>
            </div>
            <div className="p-3 rounded-lg bg-blue-600">
              <Building className="text-white w-6 h-6" />
            </div>
          </div>
        </div>

        <div className="bg-indigo-900/30 border border-indigo-800 rounded-xl p-6">
          <div className="flex items-start justify-between">
            <div>
              <p className="text-gray-400">Investments</p>
              <h3 className="text-2xl font-bold text-white mt-1">$245,000</h3>
            </div>
            <div className="p-3 rounded-lg bg-green-600">
              <Wallet className="text-white w-6 h-6" />
            </div>
          </div>
        </div>

        <div className="bg-indigo-900/30 border border-indigo-800 rounded-xl p-6">
          <div className="flex items-start justify-between">
            <div>
              <p className="text-gray-400">Returns</p>
              <h3 className="text-2xl font-bold text-white mt-1">$1,850</h3>
            </div>
            <div className="p-3 rounded-lg bg-purple-600">
              <TrendingUp className="text-white w-6 h-6" />
            </div>
          </div>
        </div>

        <div className="bg-indigo-900/30 border border-indigo-800 rounded-xl p-6">
          <div className="flex items-start justify-between">
            <div>
              <p className="text-gray-400">Investors</p>
              <h3 className="text-2xl font-bold text-white mt-1">48</h3>
            </div>
            <div className="p-3 rounded-lg bg-amber-600">
              <Users className="text-white w-6 h-6" />
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Links */}
      <div className="bg-indigo-900/30 border border-indigo-800 rounded-xl p-6 mb-8">
        <h2 className="text-xl font-bold text-white mb-4">Navigation Links</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Link 
            href="/dashboard/owner" 
            className="flex items-center p-4 bg-indigo-800/50 hover:bg-indigo-700/50 rounded-lg transition-colors"
          >
            <Building className="mr-3 text-indigo-400" />
            <span className="text-white">Owner Dashboard</span>
          </Link>
          
          <Link 
            href="/dashboard/investor" 
            className="flex items-center p-4 bg-indigo-800/50 hover:bg-indigo-700/50 rounded-lg transition-colors"
          >
            <Wallet className="mr-3 text-indigo-400" />
            <span className="text-white">Investor Dashboard</span>
          </Link>
          
          <Link 
            href="/dashboard/owner/properties" 
            className="flex items-center p-4 bg-indigo-800/50 hover:bg-indigo-700/50 rounded-lg transition-colors"
          >
            <Building className="mr-3 text-indigo-400" />
            <span className="text-white">My Properties</span>
          </Link>
          
          <Link 
            href="/dashboard/investor/opportunities" 
            className="flex items-center p-4 bg-indigo-800/50 hover:bg-indigo-700/50 rounded-lg transition-colors"
          >
            <Plus className="mr-3 text-indigo-400" />
            <span className="text-white">Investment Opportunities</span>
          </Link>
        </div>
      </div>

      {/* Information */}
      <div className="bg-indigo-900/30 border border-indigo-800 rounded-xl p-6">
        <h2 className="text-xl font-bold text-white mb-4">About the New Navigation</h2>
        <div className="text-gray-300 space-y-4">
          <p>
            The new navigation system provides a consistent experience across all dashboard pages.
            It includes:
          </p>
          <ul className="list-disc pl-6 space-y-2">
            <li>A unified top navigation bar with wallet connection</li>
            <li>Role-specific sidebar navigation</li>
            <li>Mobile-responsive design with a collapsible menu</li>
            <li>Consistent styling and user experience</li>
            <li>Wallet information displayed in the sidebar</li>
          </ul>
          <p className="mt-4">
            This eliminates the duplicate navigation elements and provides a cleaner, more efficient interface.
          </p>
        </div>
      </div>
    </div>
  );
}
