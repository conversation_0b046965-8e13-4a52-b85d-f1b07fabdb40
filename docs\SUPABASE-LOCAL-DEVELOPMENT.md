# Supabase Local Development Guide

This guide will help you set up and work with Supabase locally, eliminating the need to constantly go to the browser for database operations.

## Prerequisites

1. **Docker Desktop** - Required for running Supabase locally
2. **Node.js** - Already installed in your project
3. **Supabase CLI** - We'll install this

## Installation

### 1. Install Supabase CLI

**Windows (PowerShell):**
```powershell
# Using Scoop (recommended)
scoop bucket add supabase https://github.com/supabase/scoop-bucket.git
scoop install supabase

# Or using npm
npm install -g supabase
```

**Alternative methods:**
```bash
# Using npm globally
npm install -g supabase

# Using npx (no global install)
npx supabase --help
```

### 2. Initialize Supabase in your project

```bash
# Initialize Supabase (already done - config.toml exists)
supabase init

# Link to your remote project
supabase link --project-ref cfagwxqgbojsatddfhac
```

## Quick Start

### 1. Start Local Supabase

```bash
# Start all Supabase services locally
npm run supabase:start

# Or directly
supabase start
```

This will start:
- PostgreSQL database (port 54322)
- API server (port 54321)
- Studio UI (port 54323)
- Auth server
- Storage server
- Realtime server

### 2. Switch to Local Environment

```bash
# Switch your app to use local Supabase
node scripts/switch-env.js local

# Check current environment
node scripts/switch-env.js status
```

### 3. Access Local Services

- **Supabase Studio**: http://localhost:54323
- **API**: http://localhost:54321
- **Database**: postgresql://postgres:postgres@localhost:54322/postgres

## Daily Workflow

### Working with Database

```bash
# Run SQL queries from command line
npm run db:query "SELECT * FROM users LIMIT 5"

# Run queries from file
npm run db:query --file scripts/queries/get-properties.sql

# Interactive SQL mode
npm run db:query --interactive

# Seed database with sample data
npm run db:seed

# Reset and seed
npm run db:seed --reset
```

### Managing Schema

```bash
# Generate TypeScript types from your schema
npm run db:generate

# Push local changes to remote
npm run db:migrate

# Reset local database to match remote
npm run supabase:reset
```

### Environment Switching

```bash
# Switch to local development
node scripts/switch-env.js local

# Switch back to production
node scripts/switch-env.js remote

# Check current environment
node scripts/switch-env.js status
```

## Available Scripts

| Script | Description |
|--------|-------------|
| `npm run supabase:start` | Start local Supabase services |
| `npm run supabase:stop` | Stop local Supabase services |
| `npm run supabase:status` | Check status of services |
| `npm run supabase:studio` | Open Supabase Studio |
| `npm run supabase:reset` | Reset local database |
| `npm run db:query` | Run SQL queries |
| `npm run db:seed` | Seed database with sample data |
| `npm run db:generate` | Generate TypeScript types |

## Common Use Cases

### 1. Testing Database Changes

```bash
# Start local environment
npm run supabase:start
node scripts/switch-env.js local

# Make your changes in Studio or via SQL
npm run db:query "ALTER TABLE users ADD COLUMN new_field TEXT"

# Test your application
npm run dev

# Generate updated types
npm run db:generate
```

### 2. Developing with Sample Data

```bash
# Start fresh with sample data
npm run supabase:reset
npm run db:seed

# Your app now has test data to work with
```

### 3. Running Complex Queries

```bash
# Use the interactive SQL mode
npm run db:query --interactive

# Or run prepared queries
npm run db:query --file scripts/queries/get-investments.sql
```

## File Structure

```
scripts/
├── queries/                 # SQL query templates
│   ├── get-properties.sql
│   ├── get-users.sql
│   └── get-investments.sql
├── run-query.js            # Query runner utility
├── seed-database.js        # Database seeding
└── switch-env.js           # Environment switcher

supabase/
├── config.toml             # Supabase configuration
├── functions/              # Edge functions
└── migrations/             # Database migrations
```

## Tips and Best Practices

1. **Always use local for development** - Faster and safer
2. **Keep your queries in files** - Easier to version control and reuse
3. **Use the seeding script** - Consistent test data across team
4. **Generate types regularly** - Keep TypeScript types in sync
5. **Test locally before pushing** - Catch issues early

## Troubleshooting

### Services won't start
```bash
# Check Docker is running
docker ps

# Stop and restart
npm run supabase:stop
npm run supabase:start
```

### Database connection issues
```bash
# Check status
npm run supabase:status

# Reset if needed
npm run supabase:reset
```

### Environment confusion
```bash
# Always check which environment you're using
node scripts/switch-env.js status
```

## Next Steps

1. Start local Supabase: `npm run supabase:start`
2. Switch to local env: `node scripts/switch-env.js local`
3. Seed some data: `npm run db:seed`
4. Start developing: `npm run dev`

You're now set up for efficient local Supabase development! 🚀
