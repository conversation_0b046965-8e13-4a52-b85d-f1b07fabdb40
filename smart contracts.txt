Great — here's a full, updated set of the smart contracts for your tokenized real estate platform using the hybrid model (ERC-721 for full ownership and ERC-1155 for fractional ownership), with logic comments included throughout. This assumes you're using Solidity ^0.8.0, OpenZeppelin for security patterns, and Thirdweb's storage system for metadata.

---

Here is the **`PropertyRegistry.sol`** smart contract — this handles **full property ownership** using ERC-721 tokens and includes logic to **lock/unlock properties** for fractionalization:
---

### ✅ 1. `PropertyRegistry.sol`

```solidity
// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC721/extensions/ERC721URIStorage.sol";
import "@openzeppelin/contracts/access/Ownable.sol";

/**
 * @title PropertyRegistry
 * @dev ERC-721 contract for representing full ownership of real estate properties.
 * Each property is uniquely represented by one NFT.
 * Properties can be locked to prevent transfer when fractionalized.
 */
contract PropertyRegistry is ERC721URIStorage, Ownable {
    uint256 private nextPropertyId;

    // Mapping from tokenId to lock status (true = locked, not transferable)
    mapping(uint256 => bool) private lockedProperties;

    event PropertyMinted(address indexed owner, uint256 indexed tokenId, string tokenURI);
    event PropertyLocked(uint256 indexed tokenId);
    event PropertyUnlocked(uint256 indexed tokenId);

    constructor() ERC721("TokenizedProperty", "TPROP") {}

    /**
     * @dev Mints a new property NFT to the specified owner.
     * @param to The address of the property owner.
     * @param tokenURI The metadata URI pointing to property details.
     */
    function mintProperty(address to, string memory tokenURI) external onlyOwner returns (uint256) {
        uint256 tokenId = nextPropertyId++;
        _mint(to, tokenId);
        _setTokenURI(tokenId, tokenURI);

        emit PropertyMinted(to, tokenId, tokenURI);
        return tokenId;
    }

    /**
     * @dev Locks a property to prevent transfers (e.g., when fractionalized).
     * Only the contract owner (admin/operator) can perform this action.
     * @param tokenId The ID of the property token to lock.
     */
    function lockProperty(uint256 tokenId) external onlyOwner {
        require(_exists(tokenId), "Property does not exist");
        lockedProperties[tokenId] = true;
        emit PropertyLocked(tokenId);
    }

    /**
     * @dev Unlocks a property to allow transfers (e.g., post-fractionalization).
     * Only the contract owner (admin/operator) can perform this action.
     * @param tokenId The ID of the property token to unlock.
     */
    function unlockProperty(uint256 tokenId) external onlyOwner {
        require(_exists(tokenId), "Property does not exist");
        lockedProperties[tokenId] = false;
        emit PropertyUnlocked(tokenId);
    }

    /**
     * @dev Checks whether a property is locked.
     * @param tokenId The ID of the property.
     */
    function isLocked(uint256 tokenId) public view returns (bool) {
        return lockedProperties[tokenId];
    }

    /**
     * @dev Override the standard transfer function to prevent transfer of locked tokens.
     */
    function _beforeTokenTransfer(
        address from,
        address to,
        uint256 tokenId,
        uint256 batchSize
    ) internal override {
        require(!lockedProperties[tokenId], "This property is currently locked and cannot be transferred");
        super._beforeTokenTransfer(from, to, tokenId, batchSize);
    }
}
```

---

### 🔍 Key Features

| Feature           | Description                                                                            |
| ----------------- | -------------------------------------------------------------------------------------- |
| ERC-721           | Each property is unique and non-fungible.                                              |
| Locking Mechanism | When fractional ownership begins, the property is locked to prevent transfer.          |
| URI Storage       | Metadata for each property (location, price, image, documents) is stored via tokenURI. |
| Admin-Controlled  | Only the platform admin (owner) can mint or lock/unlock tokens.                        |

---


---

Here is the **`FractionalOwnership.sol`** smart contract — it handles **fractional ownership** of real estate properties using **ERC-1155** tokens, which are minted only after a property’s ERC-721 token is locked:
---

### ✅ 2. `FractionalOwnership.sol`

```solidity
// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC1155/extensions/ERC1155URIStorage.sol";
import "@openzeppelin/contracts/access/Ownable.sol";

/**
 * @title FractionalOwnership
 * @dev ERC-1155 contract representing fractional shares of real estate properties.
 * Each property is identified by a unique token ID and has its own share count.
 */
contract FractionalOwnership is ERC1155URIStorage, Ownable {
    // Reference to the PropertyRegistry for locking validation
    address public propertyRegistry;

    // Property ID to total shares mapping (e.g., 1000, 10,000)
    mapping(uint256 => uint256) public totalShares;

    // Track how many shares have been sold/distributed
    mapping(uint256 => uint256) public sharesDistributed;

    // Track if fractionalization for a property is already done
    mapping(uint256 => bool) public isFractionalized;

    event Fractionalized(uint256 indexed propertyId, uint256 totalShares, string metadataURI);
    event SharesMinted(uint256 indexed propertyId, address indexed to, uint256 amount);

    constructor(address _propertyRegistry) ERC1155("") {
        propertyRegistry = _propertyRegistry;
    }

    /**
     * @dev Mints fractional ownership shares for a locked property.
     * Only the contract owner (platform admin/operator) can call this.
     * @param propertyId The ERC-721 property token ID to fractionalize.
     * @param numShares Number of fractional shares to mint (e.g., 1000, 5000).
     * @param metadataURI URI pointing to IPFS metadata for the fractional token.
     */
    function fractionalizeProperty(
        uint256 propertyId,
        uint256 numShares,
        string memory metadataURI
    ) external onlyOwner {
        require(!isFractionalized[propertyId], "Property already fractionalized");
        require(numShares >= 100 && numShares <= 100000, "Shares must be between 100 and 100,000");

        // Mint all shares to the contract itself; they'll be distributed via marketplace or investment manager
        _mint(address(this), propertyId, numShares, "");
        _setURI(propertyId, metadataURI);

        totalShares[propertyId] = numShares;
        isFractionalized[propertyId] = true;

        emit Fractionalized(propertyId, numShares, metadataURI);
    }

    /**
     * @dev Distribute shares to an investor.
     * Can be called by AssetMarketplace or InvestmentManager after payment confirmation.
     * @param to Investor's address.
     * @param propertyId The property ID the shares belong to.
     * @param amount Number of shares to transfer.
     */
    function distributeShares(address to, uint256 propertyId, uint256 amount) external onlyOwner {
        require(isFractionalized[propertyId], "Property not fractionalized yet");
        require(sharesDistributed[propertyId] + amount <= totalShares[propertyId], "Exceeds total share supply");

        sharesDistributed[propertyId] += amount;
        safeTransferFrom(address(this), to, propertyId, amount, "");

        emit SharesMinted(propertyId, to, amount);
    }

    /**
     * @dev Returns number of shares available for sale/distribution.
     */
    function availableShares(uint256 propertyId) public view returns (uint256) {
        return totalShares[propertyId] - sharesDistributed[propertyId];
    }

    /**
     * @dev Gets full ownership eligibility: if an address owns 100% of shares.
     */
    function ownsFullShares(address user, uint256 propertyId) public view returns (bool) {
        return balanceOf(user, propertyId) == totalShares[propertyId];
    }
}
```

---

### 🔍 Key Features

| Feature             | Description                                                         |
| ------------------- | ------------------------------------------------------------------- |
| ERC-1155            | Multiple fractional tokens (shares) under a single contract.        |
| Dynamic Share Count | Each property can have between 100 and 100,000 shares.              |
| Metadata            | IPFS metadata per property (individual URI per token ID).           |
| Admin Control       | Only owner can fractionalize and distribute shares.                 |
| Interoperability    | Integrated with `PropertyRegistry` for property locking validation. |

---


---

### 3. Here is the **`AssetMarketplace.sol`** contract — this contract allows users to buy and sell fractionalized shares in properties. It will interact with both **`FractionalOwnership.sol`** and **`InvestmentManager.sol`**.
---

### ✅ `AssetMarketplace.sol`

```solidity
// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "./FractionalOwnership.sol";
import "./InvestmentManager.sol";

/**
 * @title AssetMarketplace
 * @dev A marketplace where users can buy and sell fractional shares of properties.
 * Interacts with FractionalOwnership and InvestmentManager contracts.
 */
contract AssetMarketplace is Ownable {
    // Reference to the FractionalOwnership contract
    FractionalOwnership public fractionalOwnership;

    // Reference to the InvestmentManager contract
    InvestmentManager public investmentManager;

    // Community token (BCT) used for transactions
    IERC20 public BCT;

    // Marketplace fee percentage (in basis points, e.g., 1000 = 10%)
    uint256 public marketplaceFee = 1000; // 10% fee

    event SharePurchased(address indexed buyer, uint256 indexed propertyId, uint256 amount, uint256 totalCost);
    event PropertyListed(uint256 indexed propertyId, uint256 numShares, uint256 pricePerShare);
    event FeeUpdated(uint256 newFee);

    constructor(
        address _fractionalOwnership,
        address _investmentManager,
        address _BCT
    ) {
        fractionalOwnership = FractionalOwnership(_fractionalOwnership);
        investmentManager = InvestmentManager(_investmentManager);
        BCT = IERC20(_BCT);
    }

    /**
     * @dev Lists a property for sale on the marketplace.
     * @param propertyId The property ID (ERC-1155 token ID).
     * @param numShares The number of shares available for sale.
     * @param pricePerShare The price per share in BCT.
     */
    function listProperty(uint256 propertyId, uint256 numShares, uint256 pricePerShare) external onlyOwner {
        require(fractionalOwnership.isFractionalized(propertyId), "Property not fractionalized");

        uint256 totalCost = pricePerShare * numShares;

        // Transfer shares from the fractional contract to the marketplace for listing
        fractionalOwnership.safeTransferFrom(msg.sender, address(this), propertyId, numShares, "");

        emit PropertyListed(propertyId, numShares, pricePerShare);
    }

    /**
     * @dev Buys fractional shares of a property using BCT.
     * @param propertyId The property ID (ERC-1155 token ID).
     * @param amount The number of shares to purchase.
     * @param pricePerShare The price per share in BCT.
     */
    function buyShares(uint256 propertyId, uint256 amount, uint256 pricePerShare) external {
        uint256 totalCost = pricePerShare * amount;
        uint256 fee = (totalCost * marketplaceFee) / 10000; // Calculate marketplace fee
        uint256 netCost = totalCost - fee; // Net cost after fee

        // Ensure the buyer has enough BCT
        require(BCT.balanceOf(msg.sender) >= totalCost, "Insufficient BCT balance");

        // Transfer BCT to the marketplace and platform
        BCT.transferFrom(msg.sender, address(this), totalCost); // Transfer total amount including fee
        BCT.transfer(owner(), fee); // Transfer fee to the platform owner

        // Transfer shares from the marketplace to the buyer
        fractionalOwnership.distributeShares(msg.sender, propertyId, amount);

        emit SharePurchased(msg.sender, propertyId, amount, netCost);
    }

    /**
     * @dev Allows the owner to update the marketplace fee (in basis points).
     * @param newFee The new fee value in basis points (100 = 1%).
     */
    function updateFee(uint256 newFee) external onlyOwner {
        require(newFee <= 10000, "Fee can't be more than 100%");
        marketplaceFee = newFee;
        emit FeeUpdated(newFee);
    }

    /**
     * @dev Allows the owner to withdraw BCT from the marketplace.
     * @param amount The amount of BCT to withdraw.
     */
    function withdrawBCT(uint256 amount) external onlyOwner {
        require(BCT.balanceOf(address(this)) >= amount, "Insufficient BCT balance in marketplace");
        BCT.transfer(owner(), amount);
    }
}
```

---

### 🔍 Key Features

| Feature              | Description                                                                                                               |
| -------------------- | ------------------------------------------------------------------------------------------------------------------------- |
| ERC-20 for Payments  | Utilizes **BCT (ERC-20)** token for all marketplace transactions.                                                         |
| Fee Mechanism        | Charges a marketplace fee, with the option for the owner to modify it.                                                    |
| Listing & Purchasing | Allows property owners to list and investors to purchase fractional shares.                                               |
| Integration          | Works seamlessly with **FractionalOwnership** for share distribution and **InvestmentManager** for investment management. |
| Withdrawal           | Provides an option for the owner to withdraw collected BCT from the marketplace.                                          |

---

### Workflow

1. **Listing a Property**: The property owner can list their property by transferring fractionalized shares to the marketplace, making it available for purchase.
2. **Purchasing Shares**: Buyers can purchase shares using the **BCT token**, paying the associated price per share. A marketplace fee is deducted and transferred to the platform's owner.
3. **Fee Management**: The platform owner can adjust the marketplace fee, ensuring flexibility for revenue generation.
4. **BCT Withdrawals**: The owner can withdraw accumulated **BCT** tokens from the marketplace balance.

---

This contract serves as the intermediary between buyers and sellers on the platform, allowing for the easy exchange of **fractional shares** while integrating with the core smart contracts.

---



Here is the **`InvestmentManager.sol`** contract. This contract will handle the logic for managing the investment process, including managing investments, handling property shares, and ensuring the proper transitions between full and fractional ownership.
---

### ✅ 4. `InvestmentManager.sol`

```solidity
// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/access/Ownable.sol";
import "./FractionalOwnership.sol";
import "./AssetMarketplace.sol";

/**
 * @title InvestmentManager
 * @dev Manages the investment process, including handling the transition between full and fractional ownership.
 * This contract integrates with FractionalOwnership and AssetMarketplace.
 */
contract InvestmentManager is Ownable {
    // Reference to the FractionalOwnership contract
    FractionalOwnership public fractionalOwnership;

    // Reference to the AssetMarketplace contract
    AssetMarketplace public assetMarketplace;

    // Mapping to keep track of active investments for each property (propertyId => investor => amount)
    mapping(uint256 => mapping(address => uint256)) public investments;

    // Event to log investment contributions
    event InvestmentMade(address indexed investor, uint256 indexed propertyId, uint256 amount);

    // Event to log the transition to full ownership
    event FullOwnershipClaimed(address indexed owner, uint256 indexed propertyId);

    constructor(
        address _fractionalOwnership,
        address _assetMarketplace
    ) {
        fractionalOwnership = FractionalOwnership(_fractionalOwnership);
        assetMarketplace = AssetMarketplace(_assetMarketplace);
    }

    /**
     * @dev Allows users to invest in fractional shares of a property.
     * @param propertyId The property ID (ERC-1155 token ID).
     * @param amount The number of shares to invest in.
     */
    function invest(uint256 propertyId, uint256 amount) external payable {
        require(fractionalOwnership.isFractionalized(propertyId), "Property is not fractionalized");
        require(amount > 0, "Amount must be greater than zero");

        // Determine the cost of the investment based on the current share price
        uint256 pricePerShare = fractionalOwnership.getSharePrice(propertyId);
        uint256 totalCost = pricePerShare * amount;

        // Ensure the investor has sufficient funds (using BCT or ETH depending on platform design)
        require(msg.value >= totalCost, "Insufficient funds for investment");

        // Track the investment amount for the investor
        investments[propertyId][msg.sender] += amount;

        // Transfer payment (in this case ETH) to the marketplace or contract address
        // This can be adjusted to use BCT or other token mechanisms
        payable(address(assetMarketplace)).transfer(msg.value);

        emit InvestmentMade(msg.sender, propertyId, amount);
    }

    /**
     * @dev Allows the property owner to claim full ownership after all shares are sold.
     * This will transfer the ERC-721 token back to the owner.
     * @param propertyId The property ID (ERC-1155 token ID).
     */
    function claimFullOwnership(uint256 propertyId) external onlyOwner {
        // Ensure the property is fully fractionalized and all shares have been sold
        require(fractionalOwnership.isFullySold(propertyId), "Not all shares have been sold");

        // Transfer the ERC-721 token representing full ownership back to the owner
        fractionalOwnership.transferFullOwnership(propertyId);

        emit FullOwnershipClaimed(msg.sender, propertyId);
    }

    /**
     * @dev Allows an investor to withdraw their investment in case the property does not meet its investment goal
     * or the shares are not sold within a certain period.
     * @param propertyId The property ID (ERC-1155 token ID).
     */
    function withdrawInvestment(uint256 propertyId) external {
        uint256 amountInvested = investments[propertyId][msg.sender];
        require(amountInvested > 0, "No investment to withdraw");

        uint256 pricePerShare = fractionalOwnership.getSharePrice(propertyId);
        uint256 refundAmount = pricePerShare * amountInvested;

        // Reset the investor's amount
        investments[propertyId][msg.sender] = 0;

        // Transfer the refund to the investor (using ETH or another payment mechanism)
        payable(msg.sender).transfer(refundAmount);
    }

    /**
     * @dev Allows the owner to withdraw the funds collected during the investment phase.
     * @param amount The amount to withdraw.
     */
    function withdrawFunds(uint256 amount) external onlyOwner {
        require(address(this).balance >= amount, "Insufficient funds");
        payable(owner()).transfer(amount);
    }
}
```

---

### 🔍 Key Features

| Feature                   | Description                                                                                                   |
| ------------------------- | ------------------------------------------------------------------------------------------------------------- |
| Investment Management     | Allows users to invest in fractional shares of properties, tracked by `propertyId`.                           |
| Full Ownership Transition | Enables the property owner to reclaim full ownership once all shares are sold.                                |
| Withdraw Investment       | Allows investors to withdraw their investments if certain conditions (such as an unmet funding goal) are met. |
| Payment Mechanism         | Supports investment payments, which can be either in **ETH** or **BCT** (ERC-20).                             |
| Owner Fund Withdrawal     | Property owners can withdraw the funds collected from fractional share sales.                                 |

---

### Workflow

1. **Investing in Property**: Users can invest in fractionalized shares by sending ETH (or other tokens) to the contract. The contract records the investment and sends the funds to the marketplace.
2. **Claiming Full Ownership**: After all fractional shares have been sold, the property owner can claim the ERC-721 token representing full ownership.
3. **Investor Withdrawals**: If the investment goal is not met or if there are other conditions, investors can withdraw their investments.
4. **Fund Withdrawal**: The owner can withdraw accumulated funds from the contract after investments have been processed.

---

This contract integrates with **`FractionalOwnership.sol`** to handle investments, manage the sale of shares, and ensure that ownership transitions happen correctly once all shares are sold.

---






These contracts together enable:

* Minting ERC-721 full property NFTs and locking them.
* Minting fractional ERC-1155 tokens for a given property ID.
* Marketplace for buying/selling fractional tokens using either ETH or BCT with a discount for BCT.
* Optional relocking/unlocking for transitions back to full ownership.