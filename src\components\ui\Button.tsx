'use client';

import React, { forwardRef } from 'react';
import { Loader2 } from 'lucide-react';
import { cn } from '@/utils/cn';

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive' | 'success';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  loading?: boolean;
  loadingText?: string;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
  rounded?: 'sm' | 'md' | 'lg' | 'full';
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({
    className,
    variant = 'primary',
    size = 'md',
    loading = false,
    loadingText,
    icon,
    iconPosition = 'left',
    fullWidth = false,
    rounded = 'md',
    disabled,
    children,
    ...props
  }, ref) => {
    const baseStyles = [
      'inline-flex items-center justify-center font-medium transition-all duration-200',
      'focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900',
      'disabled:opacity-50 disabled:cursor-not-allowed',
      'transform hover:scale-105 active:scale-95',
    ];

    const variants = {
      primary: [
        'bg-gradient-to-r from-indigo-600 to-purple-600 text-white',
        'hover:from-indigo-700 hover:to-purple-700',
        'focus:ring-indigo-500',
        'shadow-lg hover:shadow-xl hover:shadow-indigo-900/30',
      ],
      secondary: [
        'bg-indigo-800/50 text-indigo-300 border border-indigo-500/30',
        'hover:bg-indigo-700/50 hover:text-white',
        'focus:ring-indigo-500',
      ],
      outline: [
        'border border-gray-600 text-gray-300 bg-transparent',
        'hover:bg-gray-800 hover:text-white',
        'focus:ring-gray-500',
      ],
      ghost: [
        'text-gray-300 bg-transparent',
        'hover:bg-gray-800 hover:text-white',
        'focus:ring-gray-500',
      ],
      destructive: [
        'bg-red-600 text-white',
        'hover:bg-red-700',
        'focus:ring-red-500',
        'shadow-lg hover:shadow-xl hover:shadow-red-900/30',
      ],
      success: [
        'bg-green-600 text-white',
        'hover:bg-green-700',
        'focus:ring-green-500',
        'shadow-lg hover:shadow-xl hover:shadow-green-900/30',
      ],
    };

    const sizes = {
      sm: 'px-3 py-1.5 text-sm gap-1.5',
      md: 'px-4 py-2 text-sm gap-2',
      lg: 'px-6 py-3 text-base gap-2',
      xl: 'px-8 py-4 text-lg gap-3',
    };

    const roundedStyles = {
      sm: 'rounded-sm',
      md: 'rounded-lg',
      lg: 'rounded-xl',
      full: 'rounded-full',
    };

    const isDisabled = disabled || loading;

    return (
      <button
        ref={ref}
        className={cn(
          baseStyles,
          variants[variant],
          sizes[size],
          roundedStyles[rounded],
          fullWidth && 'w-full',
          isDisabled && 'transform-none hover:scale-100',
          className
        )}
        disabled={isDisabled}
        aria-label={loading ? loadingText || 'Loading...' : undefined}
        {...props}
      >
        {loading && (
          <Loader2 
            className="animate-spin" 
            size={size === 'sm' ? 14 : size === 'lg' ? 20 : size === 'xl' ? 24 : 16}
            aria-hidden="true"
          />
        )}
        
        {!loading && icon && iconPosition === 'left' && (
          <span aria-hidden="true">{icon}</span>
        )}
        
        <span>
          {loading ? (loadingText || 'Loading...') : children}
        </span>
        
        {!loading && icon && iconPosition === 'right' && (
          <span aria-hidden="true">{icon}</span>
        )}
      </button>
    );
  }
);

Button.displayName = 'Button';

export default Button;
