'use client';
import React, { useState, useEffect } from 'react';
import { createClient } from '@supabase/supabase-js';
import { Users, Search, Filter, UserPlus, Edit, Trash2, AlertTriangle, Check, X } from 'lucide-react';
// No longer need to import DashboardWrapper
import { useUser } from '../../../../context/UserContext';
import LoadingSpinner from '@/components/LoadingSpinner';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';
const supabase = createClient(supabaseUrl, supabaseAnonKey);

type UserProfile = {
  id: string;
  full_name: string;
  email: string;
  role: 'owner' | 'investor' | 'admin';
  created_at: string;
  last_sign_in_at?: string;
  company_name?: string;
  phone?: string;
  status: 'active' | 'inactive';
};

export default function AdminUsersPage() {
  const { user } = useUser();
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState<string>('all');
  const [error, setError] = useState('');
  const [selectedUser, setSelectedUser] = useState<UserProfile | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Define secondary nav for admin dashboard
  const secondaryNav = [
    { label: 'Dashboard', href: '/dashboard/admin' },
    { label: 'Users', href: '/dashboard/admin/users' },
    { label: 'Properties', href: '/dashboard/admin/properties' },
    { label: 'Transactions', href: '/dashboard/admin/transactions' },
  ];

  useEffect(() => {
    const fetchUsers = async () => {
      if (!user || user.role !== 'admin') return;

      try {
        setLoading(true);

        // Get auth users joined with profiles
        const { data, error } = await supabase
          .from('profiles')
          .select('*, auth_users:id(email, last_sign_in_at)');

        if (error) throw error;

        // Format user data
        const formattedUsers = (data || []).map((profile: any) => ({
          id: profile.id,
          full_name: profile.full_name || 'Unnamed User',
          email: profile.auth_users?.email || 'No email',
          role: profile.role || 'unknown',
          created_at: profile.created_at,
          last_sign_in_at: profile.auth_users?.last_sign_in_at,
          company_name: profile.company_name,
          phone: profile.phone,
          status: 'active'
        }));

        setUsers(formattedUsers);
      } catch (err) {
        console.error('Error fetching users:', err);
        setError('Failed to load users');
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
  }, [user]);

  // Filter users based on search term and role filter
  const filteredUsers = users.filter(user => {
    const matchesSearch = searchTerm === '' ||
      user.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesRole = roleFilter === 'all' || user.role === roleFilter;

    return matchesSearch && matchesRole;
  });

  const handleDeleteUser = async (userId: string) => {
    if (!confirm('Are you sure you want to delete this user? This action cannot be undone.')) return;

    try {
      // First delete from profiles table
      const { error: profileError } = await supabase
        .from('profiles')
        .delete()
        .eq('id', userId);

      if (profileError) throw profileError;

      // Then delete from auth users
      const { error: authError } = await supabase.auth.admin.deleteUser(userId);

      if (authError) throw authError;

      // Update local state
      setUsers(users.filter(u => u.id !== userId));
    } catch (err) {
      console.error('Error deleting user:', err);
      setError('Failed to delete user');
    }
  };

  const handleChangeRole = async (userId: string, newRole: 'owner' | 'investor' | 'admin') => {
    try {
      const { error } = await supabase
        .from('profiles')
        .update({ role: newRole })
        .eq('id', userId);

      if (error) throw error;

      // Update local state
      setUsers(users.map(u =>
        u.id === userId ? { ...u, role: newRole } : u
      ));
    } catch (err) {
      console.error('Error updating user role:', err);
      setError('Failed to update user role');
    }
  };

  const handleUpdateStatus = async (userId: string, newStatus: UserProfile['status']) => {
    try {
      const { error } = await supabase
        .from('profiles')
        .update({ status: newStatus })
        .eq('id', userId);

      if (error) throw error;
      setUsers(users.map(user =>
        user.id === userId ? { ...user, status: newStatus } : user
      ));
    } catch (error) {
      console.error('Error updating user status:', error);
      setError('Failed to update user status');
    }
  };

  if (!user || user.role !== 'admin') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="bg-red-900/40 border border-red-800 rounded-lg p-6 max-w-md">
          <div className="flex items-center space-x-3 mb-4">
            <AlertTriangle size={24} className="text-red-400" />
            <h2 className="text-xl font-bold text-white">Access Denied</h2>
          </div>
          <p className="text-red-300">You do not have permission to access the admin user management page.</p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-red-500">{error}</div>
      </div>
    );
  }

  return (
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div className="flex-1">
            <h2 className="text-2xl font-bold text-white">User Management</h2>
            <p className="text-gray-400">Manage all registered users on the platform</p>
          </div>

          {/* Search and Filter */}
          <div className="flex flex-col sm:flex-row gap-3">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search size={18} className="text-gray-500" />
              </div>
              <input
                type="text"
                placeholder="Search users..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 bg-indigo-900/30 border border-indigo-800 rounded-lg text-white w-full focus:ring-2 focus:ring-indigo-500"
              />
            </div>

            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Filter size={18} className="text-gray-500" />
              </div>
              <select
                value={roleFilter}
                onChange={(e) => setRoleFilter(e.target.value)}
                className="pl-10 pr-4 py-2 bg-indigo-900/30 border border-indigo-800 rounded-lg text-white focus:ring-2 focus:ring-indigo-500 appearance-none"
              >
                <option value="all">All Roles</option>
                <option value="owner">Property Owners</option>
                <option value="investor">Investors</option>
                <option value="admin">Administrators</option>
              </select>
            </div>

            <button
              onClick={() => {
                setSelectedUser(null);
                setIsModalOpen(true);
              }}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg text-white"
            >
              <UserPlus size={18} />
              <span>Add User</span>
            </button>
          </div>
        </div>

        {/* Users Table */}
        <div className="bg-indigo-900/30 border border-indigo-800 rounded-xl overflow-hidden">
          {loading ? (
            <div className="p-6 flex flex-col space-y-4">
              {[1, 2, 3, 4].map((_, index) => (
                <div key={index} className="animate-pulse flex items-center space-x-4">
                  <div className="w-10 h-10 bg-indigo-800/40 rounded-full"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-indigo-800/40 rounded w-1/4"></div>
                    <div className="h-3 bg-indigo-800/40 rounded w-1/3"></div>
                  </div>
                  <div className="h-6 bg-indigo-800/40 rounded w-16"></div>
                </div>
              ))}
            </div>
          ) : filteredUsers.length === 0 ? (
            <div className="p-8 text-center">
              <Users size={48} className="mx-auto text-indigo-600/40 mb-4" />
              <h3 className="text-xl font-semibold text-white mb-2">No Users Found</h3>
              <p className="text-gray-400">
                {searchTerm || roleFilter !== 'all'
                  ? 'Try adjusting your search or filter criteria'
                  : 'There are no users registered in the system yet'}
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full text-left">
                <thead className="bg-indigo-800/30 border-y border-indigo-700">
                  <tr>
                    <th className="p-4 text-xs font-semibold text-gray-300 uppercase">User</th>
                    <th className="p-4 text-xs font-semibold text-gray-300 uppercase">Email</th>
                    <th className="p-4 text-xs font-semibold text-gray-300 uppercase">Role</th>
                    <th className="p-4 text-xs font-semibold text-gray-300 uppercase">Status</th>
                    <th className="p-4 text-xs font-semibold text-gray-300 uppercase">Joined</th>
                    <th className="p-4 text-xs font-semibold text-gray-300 uppercase">Last Login</th>
                    <th className="p-4 text-xs font-semibold text-gray-300 uppercase">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-indigo-800/30">
                  {filteredUsers.map((user) => (
                    <tr key={user.id} className="hover:bg-indigo-800/10">
                      <td className="p-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 rounded-full bg-gradient-to-br from-indigo-600 to-purple-600 flex items-center justify-center">
                            <span className="text-white font-bold">
                              {user.full_name.charAt(0).toUpperCase()}
                            </span>
                          </div>
                          <div>
                            <p className="font-medium text-white">{user.full_name}</p>
                            {user.company_name && (
                              <p className="text-xs text-gray-400">{user.company_name}</p>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="p-4 text-gray-300">{user.email}</td>
                      <td className="p-4">
                        <div className="relative group">
                          <span className={`px-2.5 py-1 rounded-full text-xs font-medium inline-flex items-center
                            ${user.role === 'admin' ? 'bg-purple-900/40 text-purple-300' :
                              user.role === 'owner' ? 'bg-blue-900/40 text-blue-300' :
                              'bg-green-900/40 text-green-300'}`}
                          >
                            {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                          </span>

                          {/* Role change dropdown */}
                          <div className="absolute left-0 mt-2 w-36 bg-indigo-950 border border-indigo-800 rounded-lg shadow-lg z-10 hidden group-hover:block">
                            <div className="p-1">
                              <button
                                onClick={() => handleChangeRole(user.id, 'investor')}
                                className="w-full text-left px-3 py-2 text-sm rounded-md hover:bg-indigo-800/50 text-white flex items-center space-x-2"
                              >
                                <span>Investor</span>
                                {user.role === 'investor' && <Check size={14} className="ml-auto text-green-400" />}
                              </button>
                              <button
                                onClick={() => handleChangeRole(user.id, 'owner')}
                                className="w-full text-left px-3 py-2 text-sm rounded-md hover:bg-indigo-800/50 text-white flex items-center space-x-2"
                              >
                                <span>Owner</span>
                                {user.role === 'owner' && <Check size={14} className="ml-auto text-green-400" />}
                              </button>
                              <button
                                onClick={() => handleChangeRole(user.id, 'admin')}
                                className="w-full text-left px-3 py-2 text-sm rounded-md hover:bg-indigo-800/50 text-white flex items-center space-x-2"
                              >
                                <span>Admin</span>
                                {user.role === 'admin' && <Check size={14} className="ml-auto text-green-400" />}
                              </button>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="relative group">
                          <span className={`px-2.5 py-1 rounded-full text-xs font-medium inline-flex items-center
                            ${user.status === 'active' ? 'bg-green-900/40 text-green-300' : 'bg-red-900/40 text-red-300'}`}
                          >
                            {user.status === 'active' ? 'Active' : 'Inactive'}
                          </span>

                          {/* Status change dropdown */}
                          <div className="absolute left-0 mt-2 w-36 bg-indigo-950 border border-indigo-800 rounded-lg shadow-lg z-10 hidden group-hover:block">
                            <div className="p-1">
                              <button
                                onClick={() => handleUpdateStatus(user.id, 'active')}
                                className="w-full text-left px-3 py-2 text-sm rounded-md hover:bg-indigo-800/50 text-white flex items-center space-x-2"
                              >
                                <span>Active</span>
                                {user.status === 'active' && <Check size={14} className="ml-auto text-green-400" />}
                              </button>
                              <button
                                onClick={() => handleUpdateStatus(user.id, 'inactive')}
                                className="w-full text-left px-3 py-2 text-sm rounded-md hover:bg-indigo-800/50 text-white flex items-center space-x-2"
                              >
                                <span>Inactive</span>
                                {user.status === 'inactive' && <Check size={14} className="ml-auto text-green-400" />}
                              </button>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="p-4 text-sm text-gray-400">
                        {new Date(user.created_at).toLocaleDateString()}
                      </td>
                      <td className="p-4 text-sm text-gray-400">
                        {user.last_sign_in_at
                          ? new Date(user.last_sign_in_at).toLocaleDateString()
                          : 'Never'}
                      </td>
                      <td className="p-4">
                        <div className="flex space-x-2">
                          <button
                            className="p-1.5 bg-blue-800/40 hover:bg-blue-700/60 rounded text-blue-300"
                            title="Edit User"
                          >
                            <Edit size={16} />
                          </button>
                          <button
                            onClick={() => {
                              setSelectedUser(user);
                              setIsModalOpen(true);
                            }}
                            className="p-1.5 bg-green-800/40 hover:bg-green-700/60 rounded text-green-300"
                            title="Edit User"
                          >
                            <Edit size={16} />
                          </button>
                          <button
                            onClick={() => handleDeleteUser(user.id)}
                            className="p-1.5 bg-red-800/40 hover:bg-red-700/60 rounded text-red-300"
                            title="Delete User"
                          >
                            <Trash2 size={16} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Pagination controls could be added here */}
        <div className="flex justify-between items-center mt-4 text-sm text-gray-400">
          <div>
            Showing {filteredUsers.length} of {users.length} users
          </div>
          <div className="flex space-x-2">
            <button className="px-3 py-1 border border-indigo-800 rounded-md hover:bg-indigo-800/50">
              Previous
            </button>
            <button className="px-3 py-1 border border-indigo-800 rounded-md bg-indigo-700 text-white">
              1
            </button>
            <button className="px-3 py-1 border border-indigo-800 rounded-md hover:bg-indigo-800/50">
              Next
            </button>
          </div>
        </div>
      </div>

      {/* User Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h2 className="text-xl font-bold mb-4">
              {selectedUser ? 'Edit User' : 'Add User'}
            </h2>
            {/* Add your user form here */}
            <div className="flex justify-end gap-2 mt-6">
              <button
                onClick={() => setIsModalOpen(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  // Handle save
                  setIsModalOpen(false);
                }}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Save
              </button>
            </div>
          </div>
        </div>
      )}
  );
}