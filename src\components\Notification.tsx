import { useState, useEffect } from 'react';
import { CheckCircle, AlertCircle, X } from 'lucide-react';

type NotificationType = 'success' | 'error' | 'info' | 'warning';

type NotificationProps = {
  type: NotificationType;
  message: string;
  isVisible: boolean;
  onClose: () => void;
  duration?: number; // Auto-hide duration in milliseconds, 0 for no auto-hide
};

export default function Notification({
  type,
  message,
  isVisible,
  onClose,
  duration = 5000 // Default 5 seconds
}: NotificationProps) {
  // Auto-hide timer
  useEffect(() => {
    if (isVisible && duration > 0) {
      const timer = setTimeout(() => {
        onClose();
      }, duration);
      
      return () => clearTimeout(timer);
    }
  }, [isVisible, duration, onClose]);
  
  if (!isVisible) return null;
  
  const bgColor = {
    success: 'bg-green-100 border-green-400',
    error: 'bg-red-100 border-red-400',
    info: 'bg-blue-100 border-blue-400',
    warning: 'bg-yellow-100 border-yellow-400'
  }[type];
  
  const textColor = {
    success: 'text-green-800',
    error: 'text-red-800',
    info: 'text-blue-800',
    warning: 'text-yellow-800'
  }[type];
  
  const Icon = {
    success: CheckCircle,
    error: AlertCircle,
    info: AlertCircle,
    warning: AlertCircle
  }[type];
  
  const iconColor = {
    success: 'text-green-500',
    error: 'text-red-500',
    info: 'text-blue-500',
    warning: 'text-yellow-500'
  }[type];
  
  return (
    <div className={`fixed top-4 right-4 z-50 p-4 rounded-lg border ${bgColor} shadow-lg max-w-md`}>
      <div className="flex items-start">
        <div className="shrink-0">
          <Icon className={`h-5 w-5 ${iconColor}`} />
        </div>
        <div className={`ml-3 ${textColor}`}>
          <p className="text-sm font-medium">{message}</p>
        </div>
        <div className="ml-auto pl-3">
          <div className="-mx-1.5 -my-1.5">
            <button
              onClick={onClose}
              className={`inline-flex rounded-md p-1.5 ${textColor} hover:bg-opacity-20 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-${type === 'success' ? 'green' : type === 'error' ? 'red' : type === 'info' ? 'blue' : 'yellow'}-100 focus:ring-${type === 'success' ? 'green' : type === 'error' ? 'red' : type === 'info' ? 'blue' : 'yellow'}-500`}
            >
              <span className="sr-only">Dismiss</span>
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
} 