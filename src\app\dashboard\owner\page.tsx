'use client';
import React, { useEffect, useState } from 'react';
import { createClient } from '@supabase/supabase-js';
import { useRouter } from 'next/navigation';
import { Building, Edit, Trash2, Eye, Plus, X } from 'lucide-react';
// No longer need to import DashboardWrapper
import { useUser } from '../../../context/UserContext';
import PropertyForm, { Property as PropertyType } from '../../../components/PropertyForm';

// Define types for property data
type Property = {
  id: number;
  name: string;
  location: string;
  price: string;
  image?: string;
  image_url?: string;
  return_rate?: number;
  status?: string;
};

// Define investment data type
type InvestmentData = {
  id: number;
  property_id: number;
  investor_id: string;
  amount: number;
  created_at: string;
  profiles: {
    full_name: string;
  } | null;
};

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';
const supabase = createClient(supabaseUrl, supabaseAnonKey);

export default function OwnerDashboard() {
  const router = useRouter();
  const { user } = useUser();
  const [properties, setProperties] = useState<Property[]>([]);
  const [loading, setLoading] = useState(true);
  const [newProperty, setNewProperty] = useState({ name: '', location: '', price: '', image: '', return_rate: 8 });
  const [error, setError] = useState('');
  const [editingProperty, setEditingProperty] = useState<Property | null>(null);
  const [selectedProperty, setSelectedProperty] = useState<Property | null>(null);
  const [investments, setInvestments] = useState<{ id: number; property_id: number; investor_name: string; shares: number; total_value: string }[]>([]);
  const [uploading, setUploading] = useState(false);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [editingImageFile, setEditingImageFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [editPreviewUrl, setEditPreviewUrl] = useState<string | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);

  useEffect(() => {
    const fetchProperties = async () => {
      if (!user) return;

      try {
        const { data: propertiesData, error: propError } = await supabase
          .from('properties')
          .select('*')
          .eq('owner_id', user.id);

        if (propError) throw propError;
        setProperties(propertiesData || []);

        // Fetch investments related to this owner's properties
        if (propertiesData && propertiesData.length > 0) {
          const propertyIds = propertiesData.map(p => p.id);
          const { data: investmentsData, error: invError } = await supabase
            .from('investments')
            .select('id, property_id, investor_id, amount, created_at, profiles:investor_id(full_name)')
            .in('property_id', propertyIds);

          if (invError) throw invError;

          // Format investments data with safer typing
          const formattedInvestments = (investmentsData || []).map((inv: any) => ({
            id: inv.id,
            property_id: inv.property_id,
            investor_name: inv.profiles?.full_name || 'Unknown Investor',
            shares: 0, // Calculate based on your model
            total_value: inv.amount?.toString() || '0'
          }));

          setInvestments(formattedInvestments);
        }
      } catch (error: any) {
        console.error(
          'Error fetching owner data:',
          error?.message || error?.details || JSON.stringify(error)
        );
        setError(
          `Failed to fetch owner data: ${error?.message || 'Unknown error'}`
        );
      } finally {
      setLoading(false);
      }
    };

    fetchProperties();
  }, [user]);

  // Generate preview for new property image
  useEffect(() => {
    if (imageFile) {
      const url = URL.createObjectURL(imageFile);
      setPreviewUrl(url);
      return () => URL.revokeObjectURL(url);
    } else {
      setPreviewUrl(null);
    }
  }, [imageFile]);

  // Generate preview for editing property image
  useEffect(() => {
    if (editingImageFile) {
      const url = URL.createObjectURL(editingImageFile);
      setEditPreviewUrl(url);
      return () => URL.revokeObjectURL(url);
    } else {
      setEditPreviewUrl(null);
    }
  }, [editingImageFile]);

  const handleAddProperty = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setUploading(true);
    try {
      if (!user) throw new Error('User not authenticated');

      // 1. Insert property to get the new id
      const { data: insertData, error: insertError } = await supabase
        .from('properties')
        .insert([{
          name: newProperty.name,
          location: newProperty.location,
          price: newProperty.price,
          owner_id: user.id,
          status: 'available',
          return_rate: newProperty.return_rate || 8,
          description: ''
        }])
        .select();

      if (insertError || !insertData || insertData.length === 0)
        throw insertError || new Error('Failed to create property');

      const property = insertData[0];
      let imageUrl = '';

      // 2. Upload image to Supabase Storage with property id in the name
      if (imageFile) {
        const fileName = `property-${property.id}-${Date.now()}-${imageFile.name}`;
        const { error: uploadError } = await supabase
          .storage
          .from('property-images')
          .upload(fileName, imageFile);

        if (uploadError) throw uploadError;

        const { data: publicUrlData } = supabase
          .storage
          .from('property-images')
          .getPublicUrl(fileName);

        if (!publicUrlData?.publicUrl)
          throw new Error('Failed to get public URL for the uploaded image.');

        imageUrl = publicUrlData.publicUrl;

        // 3. Update property row with image URL
        await supabase
          .from('properties')
          .update({
            image: imageUrl,
            image_url: imageUrl  // Add image_url field to ensure consistency
          })
          .eq('id', property.id);
      }

      setProperties([...properties, { ...property, image: imageUrl }]);
      setNewProperty({ name: '', location: '', price: '', image: '' });
      setImageFile(null);
      setShowAddForm(false);
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to add property.';
      setError(errorMessage);
    } finally {
      setUploading(false);
    }
  };

  const handleEditProperty = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingProperty) return;
    setError('');
    setUploading(true);
    try {
      let imageUrl = editingProperty.image || '';

      // If a new image is selected, upload to Supabase
      if (editingImageFile) {
        const fileName = `property-${editingProperty.id}-${Date.now()}-${editingImageFile.name}`;
        const { error: uploadError } = await supabase
          .storage
          .from('property-images')
          .upload(fileName, editingImageFile);

        if (uploadError) throw uploadError;

        const { data: publicUrlData } = supabase
          .storage
          .from('property-images')
          .getPublicUrl(fileName);

        if (!publicUrlData?.publicUrl)
          throw new Error('Failed to get public URL for the uploaded image.');

        imageUrl = publicUrlData.publicUrl;
      }

      // Update property row
      const { error } = await supabase
        .from('properties')
        .update({
          ...editingProperty,
          image: imageUrl,
          image_url: imageUrl  // Add image_url field to ensure consistency
        })
        .eq('id', editingProperty.id);

      if (error) throw error;

      setProperties(properties.map(prop =>
        prop.id === editingProperty.id ? { ...editingProperty, image: imageUrl } : prop
      ));
      setEditingProperty(null);
      setEditingImageFile(null);
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update property.';
      setError(errorMessage);
    } finally {
      setUploading(false);
    }
  };

  const handleDeleteProperty = async (id: number) => {
    if (!confirm('Are you sure you want to delete this property?')) return;

    try {
      const { error } = await supabase
        .from('properties')
        .delete()
        .eq('id', id);

      if (error) throw error;

      setProperties(properties.filter(prop => prop.id !== id));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete property');
    }
  };

  const handleViewProperty = (property: Property) => {
    setSelectedProperty(property);
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setImageFile(file);
      setNewProperty((prev) => ({ ...prev, image: '' })); // Clear preview until upload
    }
  };

  const handleEditImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && editingProperty) {
      setEditingImageFile(file);
      setEditingProperty({ ...editingProperty, image: '' }); // Clear preview until upload
    }
  };

  /* eslint-disable @next/next/no-img-element */
  return (
    <div>
      {/* Properties section */}
      <div className="bg-indigo-900/30 border border-indigo-800 rounded-xl overflow-hidden mb-8">
        <div className="p-4 md:p-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <h3 className="text-lg md:text-xl font-bold text-white">My Properties</h3>
          <button
            onClick={() => setShowAddForm(!showAddForm)}
            className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 rounded-lg text-white flex items-center justify-center sm:justify-start"
          >
            {showAddForm ? 'Cancel' : <><Plus size={16} className="mr-1" /> Add Property</>}
          </button>
        </div>

        {error && (
          <div className="mx-4 md:mx-6 mb-4 md:mb-6 p-4 bg-red-900/40 border border-red-800 rounded-lg">
            <p className="text-sm md:text-base text-red-300">{error}</p>
          </div>
        )}

        {/* Add Property Form */}
        {showAddForm && (
          <div className="p-4 md:p-6 border-t border-indigo-800">
            <PropertyForm
              onSave={async (propertyData) => {
                try {
                  setError('');
                  setUploading(true);

                  if (!user) throw new Error('User not authenticated');

                  // Prepare data for insertion
                  const insertData = {
                    name: propertyData.name,
                    location: propertyData.location,
                    price: propertyData.price.toString(),
                    owner_id: user.id,
                    status: 'available',
                    return_rate: propertyData.return_rate,
                    description: propertyData.description || '',
                    image: propertyData.image_url
                  };

                  // Insert property
                  const { data: insertedData, error: insertError } = await supabase
                    .from('properties')
                    .insert([insertData])
                    .select();

                  if (insertError || !insertedData || insertedData.length === 0)
                    throw insertError || new Error('Failed to create property');

                  // Add to local state
                  setProperties([...properties, insertedData[0]]);
                  setShowAddForm(false);
                } catch (err) {
                  const errorMessage = err instanceof Error ? err.message : 'Failed to add property.';
                  setError(errorMessage);
                } finally {
                  setUploading(false);
                }
              }}
              onCancel={() => setShowAddForm(false)}
              isSubmitting={uploading}
              theme="dark"
            />
          </div>
        )}

        {/* Properties List */}
      {loading ? (
          <div className="p-4 md:p-6 space-y-4">
            {[1, 2, 3].map((_, index) => (
              <div key={index} className="animate-pulse flex items-center space-x-4">
                <div className="w-16 h-16 bg-indigo-800/40 rounded-lg"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-indigo-800/40 rounded w-1/4"></div>
                  <div className="h-3 bg-indigo-800/40 rounded w-1/3"></div>
                </div>
                <div className="h-6 bg-indigo-800/40 rounded w-16"></div>
              </div>
            ))}
          </div>
        ) : properties.length === 0 ? (
          <div className="p-8 text-center">
            <Building size={48} className="mx-auto text-indigo-600/40 mb-4" />
            <h3 className="text-lg md:text-xl font-semibold text-white mb-2">No Properties Yet</h3>
            <p className="text-sm md:text-base text-gray-400 mb-4">Add your first property to get started</p>
            <button
              onClick={() => setShowAddForm(true)}
              className="inline-flex items-center px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors text-sm md:text-base"
            >
              <Plus size={18} className="mr-2" /> Add Property
            </button>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <div className="min-w-full divide-y divide-indigo-800/30">
          {properties.map((property) => (
                <div key={property.id} className="p-4 md:p-6 hover:bg-indigo-800/10">
                  <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                    <div className="flex items-center space-x-4">
                      <div className="w-16 h-16 rounded-lg bg-indigo-800/20 overflow-hidden flex-shrink-0">
                        {property.image ? (
                          <img
                            src={property.image}
                            alt={property.name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <Building size={24} className="m-auto text-indigo-400" />
                        )}
                      </div>
                      <div>
                        <h4 className="font-medium text-white">{property.name}</h4>
                        <p className="text-sm text-gray-400">{property.location}</p>
                      </div>
                    </div>
                    <div className="flex flex-col sm:items-end gap-1">
                      <span className="font-medium text-white">{property.price}</span>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleViewProperty(property)}
                          className="p-1.5 bg-blue-800/40 hover:bg-blue-700/60 rounded text-blue-300"
                          title="View Details"
                        >
                          <Eye size={16} />
                        </button>
                        <button
                          onClick={() => setEditingProperty(property)}
                          className="p-1.5 bg-amber-800/40 hover:bg-amber-700/60 rounded text-amber-300"
                          title="Edit Property"
                        >
                          <Edit size={16} />
                        </button>
                        <button
                          onClick={() => handleDeleteProperty(property.id)}
                          className="p-1.5 bg-red-800/40 hover:bg-red-700/60 rounded text-red-300"
                          title="Delete Property"
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </div>
                  </div>
            </div>
          ))}
        </div>
          </div>
        )}
      </div>

      {/* Investments in your properties */}
      <div className="bg-indigo-900/30 border border-indigo-800 rounded-xl overflow-hidden">
        <div className="p-4 md:p-6">
          <h3 className="text-lg md:text-xl font-bold text-white">Recent Investments in Your Properties</h3>
          <p className="text-sm md:text-base text-gray-400 mt-1">Track investments made in properties you own</p>
        </div>

        {investments.length === 0 ? (
          <div className="p-8 text-center">
            <h3 className="text-lg md:text-xl font-semibold text-white mb-2">No Investments Yet</h3>
            <p className="text-sm md:text-base text-gray-400 mb-4">
              Your properties haven't received any investments yet.
              {properties.length > 0 ? ' They will appear here once investors start funding your properties.' : ' Add properties first to attract investors.'}
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <div className="min-w-full divide-y divide-indigo-800/30">
              {investments.map((investment) => {
                const property = properties.find(p => p.id === investment.property_id);
                return (
                  <div key={investment.id} className="p-4 md:p-6 hover:bg-indigo-800/10">
                    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                      <div className="flex items-center space-x-4">
                        <div className="w-16 h-16 rounded-lg bg-indigo-800/20 overflow-hidden flex-shrink-0">
                          {property?.image ? (
                            <img
                              src={property.image}
                              alt={property.name}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <Building size={24} className="m-auto text-indigo-400" />
                          )}
                        </div>
                        <div>
                          <h4 className="font-medium text-white">{investment.investor_name}</h4>
                          <p className="text-sm text-gray-400">{property?.name || 'Unknown Property'}</p>
                        </div>
                      </div>
                      <div className="flex flex-col sm:items-end gap-1">
                        <span className="font-medium text-white">${investment.total_value}</span>
                        <span className="text-sm text-gray-400">
                          {new Date().toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </div>

      {/* Edit Property Modal */}
      {editingProperty && (
        <div className="fixed inset-0 z-50 overflow-y-auto bg-black bg-opacity-75 flex items-center justify-center p-4">
          <div className="bg-indigo-900 rounded-xl p-6 max-w-4xl w-full">
            <h3 className="text-xl font-bold text-white mb-4">Edit Property</h3>
            <PropertyForm
              property={{
                id: editingProperty.id?.toString(),
                name: editingProperty.name || '',
                location: editingProperty.location || '',
                price: parseFloat(editingProperty.price?.toString() || '0'),
                return_rate: editingProperty.return_rate || 8,
                description: editingProperty.description || '',
                status: (editingProperty.status as any) || 'available',
                image_url: editingProperty.image || editingProperty.image_url
              }}
              onSave={async (propertyData) => {
                try {
                  setError('');
                  setUploading(true);

                  // Prepare data for update
                  const updateData = {
                    name: propertyData.name,
                    location: propertyData.location,
                    price: propertyData.price.toString(),
                    return_rate: propertyData.return_rate,
                    description: propertyData.description || '',
                    image: propertyData.image_url
                  };

                  // Update property
                  const { error: updateError } = await supabase
                    .from('properties')
                    .update(updateData)
                    .eq('id', editingProperty.id);

                  if (updateError) throw updateError;

                  // Update local state
                  setProperties(properties.map(prop =>
                    prop.id === editingProperty.id
                      ? { ...prop, ...updateData }
                      : prop
                  ));

                  setEditingProperty(null);
                } catch (err) {
                  const errorMessage = err instanceof Error ? err.message : 'Failed to update property.';
                  setError(errorMessage);
                } finally {
                  setUploading(false);
                }
              }}
              onCancel={() => setEditingProperty(null)}
              isSubmitting={uploading}
              theme="dark"
            />
          </div>
        </div>
      )}

      {/* View Property Modal */}
      {selectedProperty && (
        <div className="fixed inset-0 z-50 overflow-y-auto bg-black bg-opacity-75 flex items-center justify-center p-4">
          <div className="bg-indigo-900 rounded-xl p-6 max-w-2xl w-full">
            <div className="flex justify-between items-start mb-4">
              <h3 className="text-xl font-bold text-white">{selectedProperty.name}</h3>
              <button
                onClick={() => setSelectedProperty(null)}
                className="text-gray-400 hover:text-white"
              >
                &times;
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                {selectedProperty.image || selectedProperty.image_url ? (
                  <img
                    src={selectedProperty.image || selectedProperty.image_url}
                    alt={selectedProperty.name}
                    className="w-full rounded-lg object-cover h-48"
                  />
                ) : (
                  <div className="w-full h-48 bg-indigo-800/20 flex items-center justify-center rounded-lg">
                    <Building size={48} className="text-indigo-600/40" />
                  </div>
                )}

                <div className="mt-4 space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Location:</span>
                    <span className="text-white">{selectedProperty.location}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Asking Price:</span>
                    <span className="text-white">{selectedProperty.price}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Status:</span>
                    <span className="text-green-400">Available</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Return Rate:</span>
                    <span className="text-green-400">{selectedProperty.return_rate || 8}%</span>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="text-lg font-medium text-white mb-2">Investment Summary</h4>
                <div className="bg-indigo-800/30 rounded-lg p-4 space-y-4">
                  {investments.filter(i => i.property_id === selectedProperty.id).length > 0 ? (
                    <>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Total Investments:</span>
                        <span className="text-white">
                          ${investments
                            .filter(i => i.property_id === selectedProperty.id)
                            .reduce((sum, inv) => sum + parseFloat(inv.total_value), 0)
                            .toLocaleString()}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Number of Investors:</span>
                        <span className="text-white">
                          {investments.filter(i => i.property_id === selectedProperty.id).length}
                        </span>
                      </div>
                    </>
                  ) : (
                    <p className="text-gray-400">No investments received yet</p>
                  )}
                </div>

                <div className="mt-6 flex justify-end space-x-4">
                  <button
                    onClick={() => {
                      setSelectedProperty(null);
                      setEditingProperty(selectedProperty);
                    }}
                    className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 rounded-lg text-white"
                  >
                    Edit Property
                  </button>
                  <button
                    onClick={() => setSelectedProperty(null)}
                    className="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg text-white"
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
  /* eslint-enable @next/next/no-img-element */
}