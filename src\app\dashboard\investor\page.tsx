'use client';
import React, { useState, useEffect } from 'react';
import { createClient } from '@supabase/supabase-js';
import { useUser } from '../../../context/UserContext';
// No longer need to import DashboardWrapper
import { Building, Wallet, TrendingUp, ArrowRight } from 'lucide-react';
import Link from 'next/link';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';
const supabase = createClient(supabaseUrl, supabaseAnonKey);

type Investment = {
  id: string;
  property_id: string;
  amount: number;
  date_invested: string;
  property_name?: string;
  property_image?: string;
  property_location?: string;
  return_rate?: number;
};

export default function InvestorDashboard() {
  const { user } = useUser();
  const [investments, setInvestments] = useState<Investment[]>([]);
  const [loading, setLoading] = useState(true);
  const [portfolioValue, setPortfolioValue] = useState(0);
  const [availableProperties, setAvailableProperties] = useState(0);
  const [monthlyReturn, setMonthlyReturn] = useState(0);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    const fetchDashboardData = async () => {
      if (!user) return;

      try {
        setLoading(true);
        setError('');

        // Instead of using properties(...)
        const { data: userInvestments, error: investmentsError } = await supabase
          .from('investments')
          .select(`
            id,
            property_id,
            amount,
            date_invested
          `)
          .eq('investor_id', user.id);

        if (investmentsError) throw investmentsError;

        // Early exit if no investments
        if (!userInvestments || userInvestments.length === 0) {
          setInvestments([]);
          setPortfolioValue(0);
          setMonthlyReturn(0);
          setLoading(false);
          return;
        }

        // Get property IDs from investments
        const propertyIds = userInvestments.map(inv => inv.property_id);

        // Fetch properties in a separate query
        const { data: properties, error: propertiesError } = await supabase
          .from('properties')
          .select('id, name, image_url, location, return_rate')
          .in('id', propertyIds);

        if (propertiesError) throw propertiesError;

        // Create a map of property data by ID for easy lookup
        const propertyMap = (properties || []).reduce((map, property) => {
          map[property.id] = property;
          return map;
        }, {});

        // Format the investments data with property information
        const formattedInvestments = userInvestments.map(inv => ({
          id: inv.id,
          property_id: inv.property_id,
          amount: inv.amount,
          date_invested: inv.date_invested,
          property_name: propertyMap[inv.property_id]?.name,
          property_image: propertyMap[inv.property_id]?.image_url,
          property_location: propertyMap[inv.property_id]?.location,
          return_rate: propertyMap[inv.property_id]?.return_rate
        }));

        setInvestments(formattedInvestments);

        // Calculate portfolio value
        const total = formattedInvestments.reduce((sum, inv) => sum + inv.amount, 0);
        setPortfolioValue(total);

        // Calculate estimated monthly return
        const monthlyReturnAmount = formattedInvestments.reduce((sum, inv) => {
          const monthlyRate = (inv.return_rate || 0) / 12 / 100;
          return sum + (inv.amount * monthlyRate);
        }, 0);
        setMonthlyReturn(monthlyReturnAmount);

        // Count available properties
        const { count } = await supabase
          .from('properties')
          .select('id', { count: 'exact', head: true })
          .eq('status', 'available');

        setAvailableProperties(count || 0);
      } catch (error: any) {
        const errorMsg = error?.message || JSON.stringify(error) || 'Failed to fetch dashboard data.';
        console.error('Error fetching investor dashboard data:', errorMsg);
        setError('Failed to load dashboard data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [user]);

  return (
    <div>
      {error && (
        <div className="mb-4 p-3 bg-red-900/40 border border-red-800 rounded-lg">
          <p className="text-red-300 whitespace-pre-line">{error}</p>
        </div>
      )}
      {/* Dashboard statistics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 mb-8">
        <div className="bg-indigo-900/30 border border-indigo-800 rounded-xl p-4 md:p-6">
          <div className="flex items-start justify-between">
            <div>
              <p className="text-sm md:text-base text-gray-400">Portfolio Value</p>
              <h3 className="text-xl md:text-2xl font-bold text-white mt-1">
                ${loading ? '...' : portfolioValue.toLocaleString()}
              </h3>
            </div>
            <div className="p-2 md:p-3 rounded-lg bg-blue-600">
              <Wallet className="text-white w-5 h-5 md:w-6 md:h-6" />
              </div>
          </div>
        </div>

        <div className="bg-indigo-900/30 border border-indigo-800 rounded-xl p-4 md:p-6">
          <div className="flex items-start justify-between">
            <div>
              <p className="text-sm md:text-base text-gray-400">Monthly Return</p>
              <h3 className="text-xl md:text-2xl font-bold text-white mt-1">
                ${loading ? '...' : monthlyReturn.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
              </h3>
            </div>
            <div className="p-2 md:p-3 rounded-lg bg-green-600">
              <TrendingUp className="text-white w-5 h-5 md:w-6 md:h-6" />
            </div>
          </div>
        </div>

        <div className="bg-indigo-900/30 border border-indigo-800 rounded-xl p-4 md:p-6 sm:col-span-2 lg:col-span-1">
          <div className="flex items-start justify-between">
            <div>
              <p className="text-sm md:text-base text-gray-400">Available Properties</p>
              <h3 className="text-xl md:text-2xl font-bold text-white mt-1">
                {loading ? '...' : availableProperties}
              </h3>
            </div>
            <div className="p-2 md:p-3 rounded-lg bg-purple-600">
              <Building className="text-white w-5 h-5 md:w-6 md:h-6" />
            </div>
      </div>
        </div>
      </div>

      {/* Recent Investments */}
      <div className="bg-indigo-900/30 border border-indigo-800 rounded-xl overflow-hidden">
        <div className="p-4 md:p-6">
          <h3 className="text-lg md:text-xl font-bold text-white">Recent Investments</h3>
          <p className="text-sm md:text-base text-gray-400 mt-1">Your latest property investments</p>
        </div>

        {loading ? (
          <div className="p-4 md:p-6 space-y-4">
            {[1, 2, 3].map((_, index) => (
              <div key={index} className="animate-pulse flex items-center space-x-4">
                <div className="w-16 h-16 bg-indigo-800/40 rounded-lg"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-indigo-800/40 rounded w-1/4"></div>
                  <div className="h-3 bg-indigo-800/40 rounded w-1/3"></div>
                </div>
                <div className="h-6 bg-indigo-800/40 rounded w-16"></div>
              </div>
            ))}
          </div>
        ) : investments.length === 0 ? (
          <div className="p-8 text-center">
            <Wallet size={48} className="mx-auto text-indigo-600/40 mb-4" />
            <h3 className="text-lg md:text-xl font-semibold text-white mb-2">No Investments Yet</h3>
            <p className="text-sm md:text-base text-gray-400 mb-4">
              Start building your portfolio by investing in properties
            </p>
            <Link
              href="/dashboard/investor/opportunities"
              className="inline-flex items-center px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors"
            >
              Browse Properties
              <ArrowRight size={18} className="ml-2" />
            </Link>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <div className="min-w-full divide-y divide-indigo-800/30">
              {investments.slice(0, 5).map((investment) => (
                <div key={investment.id} className="p-4 md:p-6 hover:bg-indigo-800/10">
                  <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                    <div className="flex items-center space-x-4">
                      <div className="w-16 h-16 rounded-lg bg-indigo-800/20 overflow-hidden flex-shrink-0">
                        {investment.property_image ? (
                          <img
                            src={investment.property_image}
                            alt={investment.property_name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <Building size={24} className="m-auto text-indigo-400" />
                        )}
                      </div>
                      <div>
                        <h4 className="font-medium text-white">{investment.property_name}</h4>
                        <p className="text-sm text-gray-400">{investment.property_location}</p>
                      </div>
                    </div>
                    <div className="flex flex-col sm:items-end gap-1">
                      <span className="font-medium text-white">
                        ${investment.amount.toLocaleString()}
                      </span>
                      <span className="text-sm text-gray-400">
                        {new Date(investment.date_invested).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {investments.length > 5 && (
              <div className="p-4 md:p-6 border-t border-indigo-800/30">
                <Link
                  href="/dashboard/investor/portfolio"
                  className="inline-flex items-center text-sm text-indigo-400 hover:text-indigo-300"
                >
                  View All Investments
                  <ArrowRight size={16} className="ml-1" />
                </Link>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}