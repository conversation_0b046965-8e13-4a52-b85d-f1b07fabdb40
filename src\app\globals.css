@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

.dark {
  --background: #0a0a0a;
  --foreground: #ededed;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Custom animations */
@keyframes toast-progress {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out forwards;
}

@keyframes loading-dots {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Dark mode styles for common elements */
.dark .bg-white {
  background-color: #1a1a1a;
}

.dark .text-gray-900 {
  color: #f3f4f6;
}

.dark .text-gray-800 {
  color: #e5e7eb;
}

.dark .text-gray-700 {
  color: #d1d5db;
}

.dark .text-gray-600 {
  color: #9ca3af;
}

.dark .border-gray-200 {
  border-color: #374151;
}

.dark .border-gray-300 {
  border-color: #4b5563;
}

/* Custom dark mode styles for the settings page */
.dark .bg-indigo-900\/30 {
  background-color: rgba(49, 46, 129, 0.5);
}

.dark .bg-indigo-800\/30 {
  background-color: rgba(55, 48, 163, 0.5);
}

.dark .bg-indigo-700\/50 {
  background-color: rgba(67, 56, 202, 0.7);
}

.dark .border-indigo-800 {
  border-color: rgba(55, 48, 163, 0.8);
}

.dark .border-indigo-700 {
  border-color: rgba(67, 56, 202, 0.8);
}

/* Light mode overrides for the settings page */
.light .bg-indigo-900\/30 {
  background-color: rgba(224, 231, 255, 0.6);
}

.light .bg-indigo-800\/30 {
  background-color: rgba(199, 210, 254, 0.6);
}

.light .bg-indigo-700\/50 {
  background-color: rgba(165, 180, 252, 0.7);
}

.light .border-indigo-800 {
  border-color: rgba(199, 210, 254, 0.8);
}

.light .border-indigo-700 {
  border-color: rgba(165, 180, 252, 0.8);
}

.light .text-white {
  color: #1e1b4b;
}

.light .text-gray-400 {
  color: #4b5563;
}

.light .text-gray-300 {
  color: #374151;
}
