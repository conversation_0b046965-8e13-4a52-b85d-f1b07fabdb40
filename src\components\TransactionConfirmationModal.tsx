'use client';

import { useState } from 'react';
import { useWalletAuth } from '@/context/WalletAuthContext';
import { useAccount, useSigner } from 'wagmi';
import { 
  signTransaction, 
  storeTransactionSignature, 
  type TransactionType, 
  type TransactionData 
} from '@/utils/transactionSigning';
import { 
  X, 
  Wallet, 
  AlertCircle, 
  CheckCircle2, 
  Loader2, 
  FileSignature, 
  ShieldAlert 
} from 'lucide-react';

interface TransactionConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  transactionType: TransactionType;
  transactionData: TransactionData;
  onSuccess?: (signatureId: string) => void;
  onError?: (error: Error) => void;
  title?: string;
  description?: string;
}

export default function TransactionConfirmationModal({
  isOpen,
  onClose,
  transactionType,
  transactionData,
  onSuccess,
  onError,
  title = 'Confirm Transaction',
  description = 'Please review and sign this transaction with your wallet'
}: TransactionConfirmationModalProps) {
  const { user, isAuthenticated, kycStatus } = useWalletAuth();
  const { address, chainId } = useAccount();
  const { data: signer } = useSigner();
  
  const [status, setStatus] = useState<'idle' | 'signing' | 'processing' | 'success' | 'error'>('idle');
  const [error, setError] = useState<string | null>(null);
  const [signatureId, setSignatureId] = useState<string | null>(null);
  
  // Format transaction amount if present
  const formattedAmount = transactionData.amount 
    ? `${transactionData.amount} ETH` 
    : '';
  
  // Get transaction description based on type
  const getTransactionDescription = () => {
    switch (transactionType) {
      case 'property_investment':
        return `Invest ${formattedAmount} in property ${transactionData.property_id}`;
      case 'property_listing':
        return `List property for ${formattedAmount}`;
      case 'withdraw_funds':
        return `Withdraw ${formattedAmount} to your wallet`;
      case 'claim_rewards':
        return `Claim ${formattedAmount} in rewards`;
      case 'update_property':
        return `Update property ${transactionData.property_id}`;
      case 'cancel_listing':
        return `Cancel listing for property ${transactionData.property_id}`;
      default:
        return 'Confirm this transaction';
    }
  };
  
  // Handle transaction signing
  const handleSign = async () => {
    if (!isAuthenticated || !user || !address || !signer || !chainId) {
      setError('Wallet not connected or not authenticated');
      setStatus('error');
      return;
    }
    
    if (kycStatus !== 'verified') {
      setError('KYC verification required for transactions');
      setStatus('error');
      return;
    }
    
    try {
      setStatus('signing');
      setError(null);
      
      // Sign the transaction
      const signature = await signTransaction(
        signer,
        address,
        transactionType,
        transactionData,
        chainId
      );
      
      setStatus('processing');
      
      // Store the signature
      const storedSignature = await storeTransactionSignature(
        user.id,
        signature
      );
      
      // Set signature ID and status
      setSignatureId(storedSignature.id || null);
      setStatus('success');
      
      // Call success callback
      if (onSuccess && storedSignature.id) {
        onSuccess(storedSignature.id);
      }
    } catch (err) {
      console.error('Transaction signing error:', err);
      setError(err instanceof Error ? err.message : 'Failed to sign transaction');
      setStatus('error');
      
      // Call error callback
      if (onError && err instanceof Error) {
        onError(err);
      }
    }
  };
  
  // Reset state when closing
  const handleClose = () => {
    // Only allow closing if not in the middle of signing
    if (status !== 'signing' && status !== 'processing') {
      setStatus('idle');
      setError(null);
      onClose();
    }
  };
  
  if (!isOpen) return null;
  
  return (
    <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900 rounded-xl shadow-2xl overflow-hidden max-w-md w-full">
        {/* Header */}
        <div className="bg-gray-800 px-6 py-4 flex justify-between items-center">
          <h2 className="text-xl font-bold text-white">{title}</h2>
          <button
            onClick={handleClose}
            disabled={status === 'signing' || status === 'processing'}
            className="text-gray-400 hover:text-white disabled:opacity-50"
          >
            <X size={20} />
          </button>
        </div>
        
        {/* Content */}
        <div className="p-6">
          {status === 'idle' && (
            <>
              <p className="text-gray-300 mb-6">{description}</p>
              
              <div className="bg-gray-800 rounded-lg p-4 mb-6">
                <div className="flex items-center mb-2">
                  <FileSignature className="h-5 w-5 text-indigo-400 mr-2" />
                  <h3 className="text-white font-medium">Transaction Details</h3>
                </div>
                <div className="text-gray-300 text-sm">
                  <p className="mb-1"><span className="text-gray-500">Type:</span> {transactionType.replace('_', ' ')}</p>
                  <p className="mb-1"><span className="text-gray-500">Action:</span> {getTransactionDescription()}</p>
                  {transactionData.property_id && (
                    <p className="mb-1"><span className="text-gray-500">Property ID:</span> {transactionData.property_id}</p>
                  )}
                  {transactionData.token_id && (
                    <p className="mb-1"><span className="text-gray-500">Token ID:</span> {transactionData.token_id}</p>
                  )}
                  <p><span className="text-gray-500">Wallet:</span> {address ? `${address.slice(0, 6)}...${address.slice(-4)}` : 'Not connected'}</p>
                </div>
              </div>
              
              {!isAuthenticated && (
                <div className="bg-red-900/30 border border-red-800 rounded-lg p-4 mb-6 flex items-start">
                  <AlertCircle className="text-red-500 h-5 w-5 mt-0.5 mr-3 flex-shrink-0" />
                  <div>
                    <h4 className="text-red-300 font-medium">Authentication Required</h4>
                    <p className="text-red-200/70 text-sm mt-1">
                      You need to connect your wallet and authenticate before signing transactions.
                    </p>
                  </div>
                </div>
              )}
              
              {isAuthenticated && kycStatus !== 'verified' && (
                <div className="bg-amber-900/30 border border-amber-800 rounded-lg p-4 mb-6 flex items-start">
                  <ShieldAlert className="text-amber-500 h-5 w-5 mt-0.5 mr-3 flex-shrink-0" />
                  <div>
                    <h4 className="text-amber-300 font-medium">KYC Verification Required</h4>
                    <p className="text-amber-200/70 text-sm mt-1">
                      You need to complete KYC verification before signing transactions.
                    </p>
                  </div>
                </div>
              )}
            </>
          )}
          
          {status === 'signing' && (
            <div className="text-center py-6">
              <Loader2 className="h-12 w-12 text-indigo-500 animate-spin mx-auto mb-4" />
              <h3 className="text-white font-medium mb-2">Waiting for Signature</h3>
              <p className="text-gray-400 text-sm">
                Please confirm this transaction in your wallet...
              </p>
            </div>
          )}
          
          {status === 'processing' && (
            <div className="text-center py-6">
              <Loader2 className="h-12 w-12 text-indigo-500 animate-spin mx-auto mb-4" />
              <h3 className="text-white font-medium mb-2">Processing Transaction</h3>
              <p className="text-gray-400 text-sm">
                Your transaction is being processed...
              </p>
            </div>
          )}
          
          {status === 'success' && (
            <div className="text-center py-6">
              <CheckCircle2 className="h-12 w-12 text-green-500 mx-auto mb-4" />
              <h3 className="text-white font-medium mb-2">Transaction Signed Successfully</h3>
              <p className="text-gray-400 text-sm mb-4">
                Your transaction has been signed and is being processed.
              </p>
              {signatureId && (
                <p className="text-xs text-gray-500">
                  Signature ID: {signatureId}
                </p>
              )}
            </div>
          )}
          
          {status === 'error' && (
            <div className="text-center py-6">
              <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-white font-medium mb-2">Transaction Failed</h3>
              <p className="text-red-400 text-sm mb-4">
                {error || 'An error occurred while processing your transaction.'}
              </p>
              <button
                onClick={() => setStatus('idle')}
                className="text-indigo-400 hover:text-indigo-300 text-sm font-medium"
              >
                Try Again
              </button>
            </div>
          )}
        </div>
        
        {/* Footer */}
        {status === 'idle' && (
          <div className="border-t border-gray-800 px-6 py-4 flex justify-end">
            <button
              onClick={handleClose}
              className="px-4 py-2 bg-gray-800 text-white rounded-lg mr-3 hover:bg-gray-700"
            >
              Cancel
            </button>
            <button
              onClick={handleSign}
              disabled={!isAuthenticated || kycStatus !== 'verified'}
              className="px-4 py-2 bg-indigo-600 text-white rounded-lg flex items-center disabled:opacity-50 disabled:cursor-not-allowed hover:bg-indigo-700"
            >
              <Wallet size={16} className="mr-2" />
              Sign Transaction
            </button>
          </div>
        )}
        
        {(status === 'success' || status === 'error') && (
          <div className="border-t border-gray-800 px-6 py-4 flex justify-end">
            <button
              onClick={handleClose}
              className="px-4 py-2 bg-gray-800 text-white rounded-lg hover:bg-gray-700"
            >
              Close
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
