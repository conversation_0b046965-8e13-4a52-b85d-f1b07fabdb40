import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import CryptoJS from 'crypto-js';

export const runtime = 'nodejs';

/**
 * GET /api/kyc-documents?userId={userId}
 * 
 * Get all KYC documents for a user
 * If userId is not provided, returns documents for the authenticated user
 * Admin users can access documents for any user
 */
export async function GET(req: NextRequest) {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    // Verify authentication
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Get user ID from query params
    const { searchParams } = new URL(req.url);
    const requestedUserId = searchParams.get('userId') || user.id;
    
    // If requesting documents for another user, check if the current user is an admin
    if (requestedUserId !== user.id) {
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single();
      
      if (profileError || profile.role !== 'admin') {
        return NextResponse.json({ error: 'Unauthorized to access these documents' }, { status: 403 });
      }
    }
    
    // Get all documents for the user
    const { data: documents, error: documentsError } = await supabase
      .from('kyc_documents')
      .select('*')
      .eq('user_id', requestedUserId)
      .order('created_at', { ascending: false });
    
    if (documentsError) {
      return NextResponse.json({ error: 'Failed to get KYC documents' }, { status: 500 });
    }
    
    return NextResponse.json({ documents });
  } catch (error) {
    console.error('Error in GET /api/kyc-documents:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * POST /api/kyc-documents
 * 
 * Upload a new KYC document
 * 
 * Body: {
 *   userId?: string (optional, defaults to authenticated user, only admins can set this)
 *   documentType: string,
 *   documentCategory: string,
 *   encrypt: boolean
 * }
 * 
 * File should be included in the request as FormData
 */
export async function POST(req: NextRequest) {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    // Verify authentication
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Parse form data
    const formData = await req.formData();
    let userId = formData.get('userId') as string || user.id;
    const documentType = formData.get('documentType') as string;
    const documentCategory = formData.get('documentCategory') as string;
    const encrypt = formData.get('encrypt') === 'true';
    const file = formData.get('file') as File;
    
    // If uploading for another user, check if the current user is an admin
    if (userId !== user.id) {
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single();
      
      if (profileError || profile.role !== 'admin') {
        return NextResponse.json({ error: 'Unauthorized to upload documents for another user' }, { status: 403 });
      }
    }
    
    if (!documentType) {
      return NextResponse.json({ error: 'Missing documentType' }, { status: 400 });
    }
    
    if (!documentCategory) {
      return NextResponse.json({ error: 'Missing documentCategory' }, { status: 400 });
    }
    
    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }
    
    // Process the file (encrypt if needed)
    let fileToUpload = file;
    let isEncrypted = false;
    
    if (encrypt) {
      // Read the file as ArrayBuffer
      const arrayBuffer = await file.arrayBuffer();
      const fileContent = Buffer.from(arrayBuffer).toString('base64');
      
      // Use user ID as part of the encryption key for added security
      const encryptionKey = `${userId}-${process.env.KYC_ENCRYPTION_KEY || 'default-key'}`;
      
      // Encrypt the file content
      const encrypted = CryptoJS.AES.encrypt(fileContent, encryptionKey).toString();
      
      // Create a new file with encrypted content
      fileToUpload = new File(
        [encrypted],
        `encrypted-${file.name}`,
        { type: 'application/encrypted' }
      );
      
      isEncrypted = true;
    }
    
    // Create a unique file path
    const timestamp = Date.now();
    const safeFileName = file.name.replace(/[^a-zA-Z0-9.]/g, '_');
    const filePath = `kyc_documents/${userId}/${documentCategory}/${timestamp}-${safeFileName}`;
    
    // Upload to Supabase Storage
    const { error: uploadError } = await supabase.storage
      .from('kyc_documents')
      .upload(filePath, fileToUpload, {
        cacheControl: '0', // No caching for sensitive documents
        upsert: false // Don't overwrite existing files
      });
    
    if (uploadError) {
      return NextResponse.json({ error: 'Failed to upload document' }, { status: 500 });
    }
    
    // Record in database
    const documentData = {
      user_id: userId,
      document_type: documentType,
      document_category: documentCategory,
      file_path: filePath,
      file_name: file.name,
      file_size: file.size,
      file_type: file.type,
      verification_status: 'pending'
    };
    
    const { data: document, error: insertError } = await supabase
      .from('kyc_documents')
      .insert(documentData)
      .select()
      .single();
    
    if (insertError) {
      return NextResponse.json({ error: 'Failed to save document metadata' }, { status: 500 });
    }
    
    return NextResponse.json({ document });
  } catch (error) {
    console.error('Error in POST /api/kyc-documents:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * PATCH /api/kyc-documents?documentId={documentId}
 * 
 * Update a KYC document's verification status
 * Only admin users can update verification status
 * 
 * Body: {
 *   verificationStatus: string,
 *   verificationNotes?: string
 * }
 */
export async function PATCH(req: NextRequest) {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    // Get document ID from query params
    const { searchParams } = new URL(req.url);
    const documentId = searchParams.get('documentId');
    
    if (!documentId) {
      return NextResponse.json({ error: 'Missing documentId parameter' }, { status: 400 });
    }
    
    // Verify authentication
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Check if user is admin
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();
    
    if (profileError || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized to update document verification status' }, { status: 403 });
    }
    
    // Parse request body
    const body = await req.json();
    const { verificationStatus, verificationNotes } = body;
    
    if (!verificationStatus) {
      return NextResponse.json({ error: 'Missing verificationStatus' }, { status: 400 });
    }
    
    // Update the document
    const updateData: any = {
      verification_status: verificationStatus,
      verified_by: user.id,
      verified_at: new Date().toISOString()
    };
    
    if (verificationNotes !== undefined) {
      updateData.verification_notes = verificationNotes;
    }
    
    // Set expiration date if verified (1 year from now)
    if (verificationStatus === 'verified') {
      const expiryDate = new Date();
      expiryDate.setFullYear(expiryDate.getFullYear() + 1);
      updateData.expires_at = expiryDate.toISOString();
    }
    
    const { data: document, error: updateError } = await supabase
      .from('kyc_documents')
      .update(updateData)
      .eq('id', documentId)
      .select()
      .single();
    
    if (updateError) {
      return NextResponse.json({ error: 'Failed to update document' }, { status: 500 });
    }
    
    return NextResponse.json({ document });
  } catch (error) {
    console.error('Error in PATCH /api/kyc-documents:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * DELETE /api/kyc-documents?documentId={documentId}
 * 
 * Delete a KYC document
 * Users can only delete their own documents
 * Admin users can delete any document
 */
export async function DELETE(req: NextRequest) {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    // Get document ID from query params
    const { searchParams } = new URL(req.url);
    const documentId = searchParams.get('documentId');
    
    if (!documentId) {
      return NextResponse.json({ error: 'Missing documentId parameter' }, { status: 400 });
    }
    
    // Verify authentication
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Get the document to check ownership
    const { data: document, error: documentError } = await supabase
      .from('kyc_documents')
      .select('user_id, file_path')
      .eq('id', documentId)
      .single();
    
    if (documentError) {
      return NextResponse.json({ error: 'Document not found' }, { status: 404 });
    }
    
    // Check if user is owner of the document or an admin
    if (document.user_id !== user.id) {
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single();
      
      if (profileError || profile.role !== 'admin') {
        return NextResponse.json({ error: 'Unauthorized to delete this document' }, { status: 403 });
      }
    }
    
    // Delete the file from storage
    const { error: storageError } = await supabase.storage
      .from('kyc_documents')
      .remove([document.file_path]);
    
    if (storageError) {
      console.error('Error deleting file from storage:', storageError);
      // Continue with database deletion even if storage deletion fails
    }
    
    // Delete the document from the database
    const { error: deleteError } = await supabase
      .from('kyc_documents')
      .delete()
      .eq('id', documentId);
    
    if (deleteError) {
      return NextResponse.json({ error: 'Failed to delete document' }, { status: 500 });
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error in DELETE /api/kyc-documents:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
