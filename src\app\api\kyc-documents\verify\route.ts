import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { updateKYCDocumentVerification, type VerificationStatus } from '@/utils/fileStorage';

/**
 * PUT /api/kyc-documents/verify
 * 
 * Update the verification status of a KYC document
 * 
 * Body: {
 *   documentId: string,
 *   status: 'verified' | 'rejected' | 'expired',
 *   notes?: string,
 *   expirationDays?: number
 * }
 */
export async function PUT(req: NextRequest) {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    // Verify authentication
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Verify admin role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();
    
    if (profileError || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden: Admin access required' }, { status: 403 });
    }
    
    // Parse request body
    const { documentId, status, notes, expirationDays } = await req.json();
    
    if (!documentId) {
      return NextResponse.json({ error: 'Missing documentId' }, { status: 400 });
    }
    
    if (!status || !['verified', 'rejected', 'expired'].includes(status)) {
      return NextResponse.json({ error: 'Invalid status' }, { status: 400 });
    }
    
    // Update document verification status
    const updatedDocument = await updateKYCDocumentVerification(
      documentId,
      user.id,
      status as VerificationStatus,
      notes,
      expirationDays
    );
    
    return NextResponse.json({ document: updatedDocument });
  } catch (error) {
    console.error('Error in PUT /api/kyc-documents/verify:', error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Internal server error' 
    }, { status: 500 });
  }
}

/**
 * GET /api/kyc-documents/verify
 * 
 * Get pending KYC documents for verification
 * 
 * Query parameters:
 *   limit: number (default: 20)
 *   offset: number (default: 0)
 */
export async function GET(req: NextRequest) {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    // Verify authentication
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Verify admin role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();
    
    if (profileError || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden: Admin access required' }, { status: 403 });
    }
    
    // Parse query parameters
    const url = new URL(req.url);
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const offset = parseInt(url.searchParams.get('offset') || '0');
    
    // Get pending documents
    const { data, error } = await supabase
      .from('kyc_documents')
      .select(`
        *,
        profiles:user_id (
          id,
          full_name,
          email
        )
      `)
      .eq('verification_status', 'pending')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);
    
    if (error) {
      throw error;
    }
    
    // Get total count for pagination
    const { count, error: countError } = await supabase
      .from('kyc_documents')
      .select('id', { count: 'exact', head: true })
      .eq('verification_status', 'pending');
    
    if (countError) {
      throw countError;
    }
    
    return NextResponse.json({ 
      documents: data || [],
      pagination: {
        total: count || 0,
        limit,
        offset
      }
    });
  } catch (error) {
    console.error('Error in GET /api/kyc-documents/verify:', error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Internal server error' 
    }, { status: 500 });
  }
}
