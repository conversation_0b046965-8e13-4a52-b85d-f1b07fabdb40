# 🚀 Local Supabase Development Setup

Work with Supabase locally without constantly going to the browser! This setup gives you a complete local development environment with all Supabase services running on your machine.

## ⚡ Quick Start

### 1. One-Command Setup
```bash
npm run setup:local
```

This will:
- ✅ Check prerequisites (Docker, Supabase CLI)
- ✅ Install dependencies
- ✅ Start local Supabase services
- ✅ Switch your app to local environment
- ✅ Seed database with sample data

### 2. Start Development
```bash
npm run dev
```

Your app now uses the local Supabase instance! 🎉

## 🛠️ Daily Commands

### Database Operations
```bash
# Run SQL queries from command line
npm run db:query "SELECT * FROM users LIMIT 5"

# Interactive SQL mode
npm run db:query --interactive

# Run queries from files
npm run db:query --file scripts/queries/get-properties.sql

# Seed database with sample data
npm run db:seed
```

### Environment Management
```bash
# Switch to local development
node scripts/switch-env.js local

# Switch back to production
node scripts/switch-env.js remote

# Check current environment
node scripts/switch-env.js status
```

### Supabase Services
```bash
# Start all services
npm run supabase:start

# Stop all services
npm run supabase:stop

# Check service status
npm run supabase:status

# Open Supabase Studio
npm run supabase:studio
```

## 🌐 Local Services

When running locally, you can access:

- **Supabase Studio**: http://localhost:54323
- **API Endpoint**: http://localhost:54321
- **Database**: postgresql://postgres:postgres@localhost:54322/postgres

## 📁 Useful Files

- `scripts/queries/` - Pre-written SQL queries
- `scripts/run-query.js` - Command-line query runner
- `scripts/seed-database.js` - Database seeding
- `scripts/switch-env.js` - Environment switcher
- `docs/SUPABASE-LOCAL-DEVELOPMENT.md` - Complete guide

## 🔄 Typical Workflow

1. **Start local environment**:
   ```bash
   npm run supabase:start
   node scripts/switch-env.js local
   ```

2. **Develop and test**:
   ```bash
   npm run dev
   npm run db:query --interactive
   ```

3. **When done, switch back**:
   ```bash
   node scripts/switch-env.js remote
   npm run supabase:stop
   ```

## 🆘 Troubleshooting

### Services won't start
```bash
# Make sure Docker is running
docker ps

# Restart services
npm run supabase:stop
npm run supabase:start
```

### Database issues
```bash
# Reset local database
npm run supabase:reset
npm run db:seed
```

### Environment confusion
```bash
# Always check which environment you're using
node scripts/switch-env.js status
```

## 📚 Learn More

- Read the complete guide: `docs/SUPABASE-LOCAL-DEVELOPMENT.md`
- Check available SQL queries: `scripts/queries/`
- Explore the seeding script: `scripts/seed-database.js`

---

**Happy local development!** 🎯 No more browser switching needed!
