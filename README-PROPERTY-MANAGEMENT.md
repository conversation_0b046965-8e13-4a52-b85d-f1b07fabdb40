# Property Management Module

This module provides a comprehensive property management system for property owners to manage their real estate investments within the BrickChain platform.

## Features

### 1. Enhanced Property Management Dashboard

The property management dashboard has been redesigned with improved functionality:

- **Modern, responsive UI**: Works on all devices
- **Property Cards**: Visual representation of each property with status indicators
- **Image Support**: Upload and display property images
- **Search**: Quickly find properties
- **Error Handling**: Improved error notifications

### 2. Property Actions

Each property now has the following actions available:

- **Create**: Add new properties with validation
- **Edit**: Update property details
- **Delete**: Remove properties from your portfolio
- **Publish/Unpublish**: Control whether properties are visible to investors
- **Archive/Unarchive**: Store properties you want to keep but not actively manage

### 3. Property Status Management

Properties can now have the following statuses:

- **Draft**: Initial state for new properties (not visible to investors)
- **Available**: Properties ready for investment
- **Pending**: Properties with ongoing investment transactions
- **Funded**: Properties that have reached their funding goal
- **Sold**: Properties that have been sold
- **Archived**: Properties that are no longer active but kept for record-keeping

### 4. Database Schema Updates

The database schema has been enhanced to support these new features:

- New `published` boolean field to control visibility
- Enhanced `status` field as an enum type
- Added `description` field for detailed property information
- Improved timestamps with `created_at` and `updated_at` fields

## How to Use

### Managing Properties

1. Navigate to the Owner Dashboard
2. Click on "My Properties" in the sidebar
3. Use the "Add Property" button to create new properties
4. Fill in the required information:
   - Property Name
   - Location
   - Price
   - Return Rate
   - Description (optional)
   - Upload an image
5. Choose the status (default is "Draft")
6. Choose whether to publish the property immediately

### Property Actions

For each property card, you'll see the following action buttons:

- **Edit**: Make changes to property details
- **Delete**: Permanently remove a property (requires confirmation)
- **Publish/Unpublish**: Toggle visibility to investors
- **Archive/Unarchive**: Move a property to/from the archive

### Tips

- **Draft vs. Published**: Use "Draft" status for properties you're still setting up, and only mark them as "Published" when they're ready for investors to see
- **Archiving**: Use this instead of deletion when you want to keep a record but no longer want to see the property in your active listings
- **Status Updates**: Changing the status affects how the property appears to investors

## Database Setup

If you're setting up a new instance, run the SQL commands in `src/app/debug/update-schema.sql` to add the necessary fields to your database.

## Components

This module includes the following reusable components:

1. **PropertyForm**: A comprehensive form for creating/editing properties
2. **PropertyCard**: Visual card display of properties with action buttons
3. **Notification**: Toast notification system for user feedback

## Technical Details

- **Image Storage**: Property images are stored in Supabase Storage in the `property-images` bucket
- **State Management**: React state with proper TypeScript typing
- **Validation**: Client-side validation ensures all required fields are provided
- **Error Handling**: Comprehensive error handling for all database operations 