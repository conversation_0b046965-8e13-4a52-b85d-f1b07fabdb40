import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export async function middleware(req: NextRequest) {
  const res = NextResponse.next();

  // Create a Supabase client configured to use cookies
  const supabase = createMiddlewareClient({ req, res }, {
    supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL!,
    supabaseKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  });

  // Refresh session if expired - important to do before accessing session
  await supabase.auth.getSession();

  const { data: { session } } = await supabase.auth.getSession();

  // Define protected routes
  const protectedRoutes = ['/dashboard', '/profile']; // Add all routes that need auth
  const publicRoutes = ['/api/auth/callback']; // Only keep necessary API routes

  const { pathname } = req.nextUrl;

  // If no session and trying to access a protected route, redirect to home page to connect wallet
  if (!session && protectedRoutes.some(route => pathname.startsWith(route))) {
    const redirectUrl = new URL('/', req.url);
    redirectUrl.searchParams.set('callbackUrl', pathname); // Preserve intended destination
    return NextResponse.redirect(redirectUrl);
  }

  // Remove login/signup redirects as these pages no longer exist

  // Handle /dashboard root path - redirect to role-specific dashboard
  if (session && pathname === '/dashboard') {
    try {
      // Try to get user's role from profile
      const { data: profile } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', session.user.id)
        .single();

      // Redirect to role-specific dashboard if role exists
      if (profile && profile.role) {
        return NextResponse.redirect(new URL(`/dashboard/${profile.role}`, req.url));
      }
    } catch (error) {
      // If there's an error, continue to the dashboard page which will handle the redirect
    }
  }

  return res;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * Feel free to modify this pattern to include more paths.
     */
    '/((?!_next/static|_next/image|favicon.ico|assets|test-supabase.js).*)',
  ],
};