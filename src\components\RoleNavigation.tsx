'use client';
import { useRouter, usePathname } from 'next/navigation';
import { useUser } from '../context/UserContext';
import { Home, Building, Wallet, ChartBar, Users, Settings, Book, HelpCircle, Info } from 'lucide-react';

// Define role-specific navigation with icons
const roleNavConfig = {
  owner: [
    { href: '/dashboard/owner', icon: Home, label: 'Dashboard', description: 'Overview of your properties' },
    { href: '/dashboard/owner/properties', icon: Building, label: 'My Properties', description: 'Manage your listed properties' },
    { href: '/dashboard/owner/investments', icon: Wallet, label: 'Investments', description: 'Track investments in your properties' },
    { href: '/dashboard/owner/analytics', icon: ChartBar, label: 'Analytics', description: 'Property performance metrics' },
    { href: '/dashboard/owner/settings', icon: Settings, label: 'Settings', description: 'Account preferences' },
  ],
  investor: [
    { href: '/dashboard/investor', icon: Home, label: 'Dashboard', description: 'Your investment overview' },
    { href: '/dashboard/investor/opportunities', icon: Building, label: 'Opportunities', description: 'Find new properties to invest in' },
    { href: '/dashboard/investor/portfolio', icon: Wallet, label: 'My Portfolio', description: 'View your property investments' },
    { href: '/dashboard/investor/history', icon: ChartBar, label: 'History', description: 'Past investment transactions' },
    { href: '/dashboard/investor/settings', icon: Settings, label: 'Settings', description: 'Account preferences' },
  ],
  admin: [
    { href: '/dashboard/admin', icon: Home, label: 'Dashboard', description: 'Platform overview' },
    { href: '/dashboard/admin/users', icon: Users, label: 'User Management', description: 'Manage platform users' },
    { href: '/dashboard/admin/properties', icon: Building, label: 'Properties', description: 'All platform properties' },
    { href: '/dashboard/admin/transactions', icon: Wallet, label: 'Transactions', description: 'Monitor all transactions' },
    { href: '/dashboard/admin/analytics', icon: ChartBar, label: 'Analytics', description: 'Platform performance' },
    { href: '/dashboard/admin/settings', icon: Settings, label: 'Settings', description: 'Platform configuration' },
  ],
  // Shared navigation items for all authenticated users
  shared: [
    { href: '/marketplace', icon: Building, label: 'Marketplace', description: 'Explore available properties' },
    { href: '/how-it-works', icon: Book, label: 'How It Works', description: 'Learn about BrickChain' },
    { href: '/resources', icon: HelpCircle, label: 'Resources', description: 'Guides and documentation' },
    { href: '/about', icon: Info, label: 'About BCT', description: 'About our platform' },
  ],
  // Navigation for unauthenticated users
  guest: [
    { href: '/marketplace', icon: Building, label: 'Marketplace', description: 'Explore available properties' },
    { href: '/how-it-works', icon: Book, label: 'How It Works', description: 'Learn about BrickChain' },
    { href: '/about', icon: Info, label: 'About BCT', description: 'About our platform' },
    { href: '/resources', icon: HelpCircle, label: 'Resources', description: 'Guides and documentation' },
  ],
};

export default function RoleNavigation({ onItemClick, className = '' }: {
  onItemClick?: () => void;
  className?: string;
}) {
  const pathname = usePathname();
  const router = useRouter();
  const { user, loading } = useUser();

  // Determine the user's role
  const role = user?.role || 'guest';

  // Get navigation items based on role
  const roleSpecificNavItems = role !== 'guest' ? roleNavConfig[role] || [] : [];
  const sharedNavItems = role !== 'guest' ? roleNavConfig.shared : roleNavConfig.guest;

  // Combine role-specific and shared items
  const navItems = [...roleSpecificNavItems, ...sharedNavItems];

  const handleNavigation = (href: string) => {
    router.push(href);
    if (onItemClick) onItemClick();
  };

  return (
    <nav className={`flex flex-col space-y-1 ${className}`}>
      {loading ? (
        // Loading skeleton
        Array(5).fill(0).map((_, i) => (
          <div key={i} className="px-4 py-3 animate-pulse">
            <div className="flex items-center space-x-3">
              <div className="w-6 h-6 bg-indigo-800/50 rounded-md"></div>
              <div className="h-4 bg-indigo-800/50 rounded w-24"></div>
            </div>
          </div>
        ))
      ) : (
        navItems.map((item) => {
          const isActive = pathname === item.href;
          const Icon = item.icon;

          return (
            <button
              key={item.href}
              onClick={() => handleNavigation(item.href)}
              className={`px-4 py-3 flex items-center space-x-3 rounded-lg transition-colors text-left ${
                isActive
                  ? 'bg-indigo-700/30 text-cyan-400 font-medium'
                  : 'hover:bg-indigo-800/20 text-gray-300 hover:text-white'
              }`}
              title={item.description}
            >
              <Icon size={20} className={isActive ? 'text-cyan-400' : 'text-gray-400'} />
              <span>{item.label}</span>
            </button>
          );
        })
      )}
    </nav>
  );
}