"use client";
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
// Remove direct client creation from '@supabase/supabase-js'
// import { createClient } from '@supabase/supabase-js';
import { useSupabaseClient, useSession } from '@supabase/auth-helpers-react'; // Import useSupabaseClient and useSession
import type { SupabaseClient, Session } from '@supabase/supabase-js'; // Import types

// console.log('UserContext - Supabase URL defined:', !!process.env.NEXT_PUBLIC_SUPABASE_URL);
// console.log('UserContext - Supabase Key defined:', !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY);

export type UserProfile = {
  id: string;
  full_name: string;
  phone: string;
  role: 'owner' | 'investor' | 'admin';
  company_name?: string;
};

type UserContextType = {
  user: UserProfile | null;
  session: Session | null; // Add session to context
  // setUser: (user: UserProfile | null) => void; // setUser will be handled by auth state changes
  loading: boolean;
  refreshUser: () => Promise<void>; // Keep refreshUser if needed for manual profile refresh
  signOut: () => Promise<void>; // Add signOut method
};

const UserContext = createContext<UserContextType>({
  user: null,
  session: null,
  // setUser: () => {},
  loading: true, // Start with loading true
  refreshUser: async () => {},
  signOut: async () => {},
});

export const useUser = () => useContext(UserContext);

export function UserProvider({ children }: { children: ReactNode }) {
  const supabase = useSupabaseClient<SupabaseClient>(); // Get Supabase client from context
  const session = useSession(); // Get session from context

  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchUserProfile = async (userId: string) => {
    if (!userId) {
      setUserProfile(null);
      setLoading(false);
      return;
    }
    setLoading(true);
    try {
      console.log('UserContext - Fetching profile for user:', userId);
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('UserContext - Profile fetch error:', error);
        // If profile not found (PGRST116), it's not necessarily an app error, user might just not have a profile yet.
        if (error.code !== 'PGRST116') {
            // Handle other errors more explicitly if needed
            console.log('UserContext - Non-PGRST116 error:', error.code, error.message);
        } else {
            console.log('UserContext - Profile not found (PGRST116), creating new profile');
            // Optionally create a new profile here if needed
        }
        setUserProfile(null);
      } else if (profile) {
        console.log('UserContext - Profile fetched successfully:', profile?.role);
        setUserProfile(profile as UserProfile);
      } else {
        console.log('UserContext - No profile data returned but no error');
        setUserProfile(null);
      }
    } catch (err: any) {
      console.error(
        'UserContext - Unexpected error in fetchUserProfile:',
        err?.message || err?.details || JSON.stringify(err)
      );
      setUserProfile(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Check if this is a page refresh
    const isPageRefresh = window.performance &&
      window.performance.navigation &&
      window.performance.navigation.type === 1;

    // Get the current path to check if we're on a dashboard page
    const currentPath = typeof window !== 'undefined' ? window.location.pathname : '';
    const isDashboardPage = currentPath.startsWith('/dashboard');

    if (session?.user) {
      console.log('UserContext - Session found, fetching profile for:', session.user.id);

      // If this is a page refresh on a dashboard page, prioritize keeping the current page
      if (isPageRefresh && isDashboardPage && userProfile) {
        console.log('UserContext - Page refresh detected on dashboard, preserving current state');
        // We already have a user profile, so we don't need to fetch it again immediately
        // This prevents unnecessary redirects during page refresh
        setLoading(false);

        // Still fetch the profile in the background to ensure data is fresh
        fetchUserProfile(session.user.id).catch(err => {
          console.error('UserContext - Background profile refresh error:', err);
        });
      } else {
        // Normal case - fetch profile
        fetchUserProfile(session.user.id);
      }
    } else {
      console.log('UserContext - No session, clearing profile.');
      setUserProfile(null);
      setLoading(false); // Stop loading if no session
    }
  }, [session]); // Re-run when session changes

  // refreshUser can be kept if you need a manual way to re-fetch profile,
  // but primary updates will happen via session changes.
  const refreshUser = async () => {
    if (session?.user?.id) {
      await fetchUserProfile(session.user.id);
    }
  };

  const signOut = async () => {
    setLoading(true);
    console.log('UserContext - Signing out...');

    try {
      // Clear local storage
      localStorage.clear();

      // Clear session cookies
      document.cookie.split(';').forEach(cookie => {
        const [name] = cookie.trim().split('=');
        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
      });

      // Sign out from Supabase
      const { error } = await supabase.auth.signOut();
      if (error) {
        console.error('UserContext - Error signing out:', error);
      }

      // Clear user profile state
      setUserProfile(null);

      // Clear any wallet connection data if applicable
      if (window.localStorage.getItem('wagmi.connected')) {
        window.localStorage.removeItem('wagmi.connected');
      }

      // Clear RainbowKit data
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith('rk-') || key.startsWith('wagmi-') || key.startsWith('walletconnect')) {
          localStorage.removeItem(key);
        }
      });

      console.log('UserContext - Successfully signed out and cleared all data');
    } catch (err) {
      console.error('UserContext - Error during sign out cleanup:', err);
    } finally {
      setLoading(false);
    }
  };

  // No need for onAuthStateChange listener here anymore if SessionContextProvider handles it.
  // The `session` object from `useSession()` will update automatically.

  return (
    <UserContext.Provider value={{ user: userProfile, session, loading, refreshUser, signOut }}>
      {children}
    </UserContext.Provider>
  );
}