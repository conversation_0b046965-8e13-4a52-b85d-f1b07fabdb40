"use client";

import { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { AuthChangeEvent, Session, Subscription } from '@supabase/supabase-js';
import Link from 'next/link';
import { ArrowLeft, Eye, EyeOff } from 'lucide-react';

function ResetPasswordForm() {
  const supabase = createClientComponentClient();
  const router = useRouter();
  const searchParams = useSearchParams();

  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isTokenValid, setIsTokenValid] = useState(false);
  const [checkingToken, setCheckingToken] = useState(true);

  useEffect(() => {
    // Supabase handles the token verification via onAuthStateChange
    // when the component mounts after redirect from email link.
    // The presence of the `type=recovery` in searchParams is a good indicator,
    // but the actual session update happens via the auth helper.
    const type = searchParams.get('type');
    if (type === 'recovery') {
        // This is a basic check. Supabase handles the actual token validation.
        // We listen for onAuthStateChange for the PASSWORD_RECOVERY event.
    }

    const { data: authListener }: { data: { subscription: Subscription | null } } = supabase.auth.onAuthStateChange(async (event: AuthChangeEvent, session: Session | null) => {
      if (event === 'PASSWORD_RECOVERY') {
        // This event means the user has successfully initiated password recovery.
        // The session object might be null here, but the user is in a state where they can update their password.
        setIsTokenValid(true);
        setMessage('You can now set your new password.');
      } else if (session && isTokenValid) {
        // If a session becomes active AND we previously confirmed token validity (PASSWORD_RECOVERY event),
        // it implies the password update might have occurred if we allowed it immediately.
        // However, we want the user to explicitly enter a new password on this form.
      }
      setCheckingToken(false);
    });

    // Initial check to see if we already have a session (e.g., if user is already logged in and somehow lands here)
    // or if the recovery token is in the URL fragment (Supabase client moves it to session storage).
    supabase.auth.getSession().then(({ data: { session } }) => {
      if (session) {
        // If there's an active session, and it's not part of the recovery flow,
        // we might not need to show the reset form unless specifically in recovery state.
        // The onAuthStateChange listener is more critical for PASSWORD_RECOVERY event.
      }
      // If no session and no PASSWORD_RECOVERY event fired yet, we wait.
      // If no event fires after a timeout, then token might be invalid or not present.
      setTimeout(() => {
        if(checkingToken && !isTokenValid) {
            setCheckingToken(false);
            setError('Invalid or expired recovery link. Please request a new one.');
        }
      }, 3000); // Wait for 3 seconds for the event
    });

    return () => {
      authListener?.subscription?.unsubscribe();
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [supabase, router]);

  const handlePasswordUpdate = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (password !== confirmPassword) {
      setError('Passwords do not match.');
      return;
    }
    if (password.length < 6) {
      setError('Password must be at least 6 characters long.');
      return;
    }
    setLoading(true);
    setMessage('');
    setError('');

    const { error } = await supabase.auth.updateUser({ password });

    setLoading(false);
    if (error) {
      setError(`Error: ${error.message}`);
    } else {
      setMessage('Password updated successfully! You can now log in with your new password.');
      setPassword('');
      setConfirmPassword('');
      setTimeout(() => router.push('/'), 3000);
    }
  };

  if (checkingToken) {
    return (
        <div className="min-h-screen bg-gray-900 text-white flex flex-col justify-center items-center p-4">
            <div className="w-full max-w-md bg-gray-800 p-8 rounded-lg shadow-xl text-center">
                <svg className="animate-spin h-8 w-8 text-indigo-400 mx-auto mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <p className="text-lg">Verifying recovery link...</p>
            </div>
        </div>
    );
  }

  if (!isTokenValid && !message) { // Only show this error if no success/pending message exists
    return (
        <div className="min-h-screen bg-gray-900 text-white flex flex-col justify-center items-center p-4">
            <div className="w-full max-w-md bg-gray-800 p-8 rounded-lg shadow-xl text-center">
                <h1 className="text-2xl font-bold text-red-400 mb-4">Link Invalid</h1>
                <p className="text-gray-300 mb-6">{error || 'The password recovery link is invalid or has expired. Please request a new one.'}</p>
                <Link
                    href="/forgot-password"
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-indigo-500"
                >
                    Request New Link
                </Link>
            </div>
        </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white flex flex-col justify-center items-center p-4">
      <div className="w-full max-w-md bg-gray-800 p-8 rounded-lg shadow-xl">
        <Link href="/" className="flex items-center text-indigo-400 hover:text-indigo-300 mb-6 group">
          <ArrowLeft size={18} className="mr-2 group-hover:-translate-x-1 transition-transform duration-200" />
          Back to Home
        </Link>
        <h1 className="text-3xl font-bold text-center text-indigo-400 mb-8">Reset Password</h1>

        <form onSubmit={handlePasswordUpdate} className="space-y-6">
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-300">
              New Password
            </label>
            <div className="mt-1 relative">
              <input
                id="password"
                name="password"
                type={showPassword ? 'text' : 'password'}
                required
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="block w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-md shadow-sm placeholder-gray-500 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                placeholder="••••••••"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute inset-y-0 right-0 px-3 flex items-center text-gray-400 hover:text-gray-200"
              >
                {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
              </button>
            </div>
          </div>

          <div>
            <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-300">
              Confirm New Password
            </label>
            <div className="mt-1 relative">
              <input
                id="confirmPassword"
                name="confirmPassword"
                type={showConfirmPassword ? 'text' : 'password'}
                required
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="block w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-md shadow-sm placeholder-gray-500 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                placeholder="••••••••"
              />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute inset-y-0 right-0 px-3 flex items-center text-gray-400 hover:text-gray-200"
              >
                {showConfirmPassword ? <EyeOff size={20} /> : <Eye size={20} />}
              </button>
            </div>
          </div>

          {message && <p className="text-sm text-green-400 bg-green-900/30 p-3 rounded-md">{message}</p>}
          {error && <p className="text-sm text-red-400 bg-red-900/30 p-3 rounded-md">{error}</p>}

          <div>
            <button
              type="submit"
              disabled={loading || !isTokenValid} // Disable if loading or token not (yet) validated
              className="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? (
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              ) : (
                'Update Password'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

// Wrap with Suspense because useSearchParams() needs it
export default function ResetPasswordPage() {
    return (
        <Suspense fallback={<div>Loading...</div>}>
            <ResetPasswordForm />
        </Suspense>
    );
}