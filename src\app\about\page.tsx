import React from 'react';
export default function About() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-r from-indigo-900 to-purple-900 text-white">
      <div className="bg-indigo-800 bg-opacity-50 p-8 rounded-lg shadow-lg max-w-2xl w-full">
        <h1 className="text-3xl font-bold mb-4">About BrickChain & BCT</h1>
        <p className="mb-6">BrickChain is on a mission to democratize real estate investment by leveraging blockchain technology. Our platform enables anyone to invest in global real estate, one token at a time.</p>
        <h2 className="text-xl font-semibold mb-2">Our Vision</h2>
        <p className="mb-4">To make real estate investment accessible, transparent, and liquid for everyone, everywhere.</p>
        <h2 className="text-xl font-semibold mb-2">What is BCT?</h2>
        <p className="mb-4">BCT (BrickChain Token) is the utility token that powers the BrickChain ecosystem. It is used for transaction fees, governance, and rewards within the platform.</p>
        <h2 className="text-xl font-semibold mb-2">Token Utility</h2>
        <ul className="list-disc list-inside mb-4 space-y-1">
          <li>Pay discounted platform fees</li>
          <li>Participate in governance and voting</li>
          <li>Earn rewards for holding and staking</li>
          <li>Access exclusive investment opportunities</li>
        </ul>
        <h2 className="text-xl font-semibold mb-2">Our Team</h2>
        <p className="mb-2">BrickChain is built by a global team of blockchain, real estate, and fintech experts. We are passionate about making property investment borderless and inclusive.</p>
        <p>Contact us: <a href="mailto:<EMAIL>" className="text-cyan-400 hover:underline"><EMAIL></a></p>
      </div>
    </div>
  );
} 