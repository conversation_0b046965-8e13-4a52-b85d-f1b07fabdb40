# BrickChain Smart Contracts

This directory contains the smart contracts for the BrickChain platform, which enables tokenization of real estate properties and facilitates investment in these properties through blockchain technology.

## Contract Architecture

The BrickChain platform uses the following smart contracts:

1. **PropertyToken.sol**: ERC-1155 token contract for representing property shares
2. **PropertyRegistry.sol**: Registry for managing property metadata and ownership
3. **InvestmentManager.sol**: Handles investments and distributions
4. **Marketplace.sol**: Facilitates buying and selling of property tokens

## Development Setup

1. Install dependencies:
```bash
npm install
```

2. Compile contracts:
```bash
npx hardhat compile
```

3. Run tests:
```bash
npx hardhat test
```

4. Deploy to testnet:
```bash
npx hardhat run scripts/deploy.js --network sonicBlaze
```

## Contract Interactions

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  PropertyToken  │◄────┤PropertyRegistry │◄────┤InvestmentManager│
│                 │     │                 │     │                 │
└────────┬────────┘     └─────────────────┘     └────────┬────────┘
         │                                               │
         │                                               │
         │              ┌─────────────────┐              │
         └──────────────►                 ◄──────────────┘
                        │   Marketplace   │
                        │                 │
                        └─────────────────┘
```

## Contract Addresses

| Contract | Network | Address |
|----------|---------|---------|
| PropertyToken | Sonic Blaze Testnet | TBD |
| PropertyRegistry | Sonic Blaze Testnet | TBD |
| InvestmentManager | Sonic Blaze Testnet | TBD |
| Marketplace | Sonic Blaze Testnet | TBD |
