'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useWalletAuth } from '@/context/WalletAuthContext';
import { useSupabaseClient } from '@supabase/auth-helpers-react';
import { 
  Shield, 
  CheckCircle2, 
  Clock, 
  AlertCircle, 
  ChevronRight, 
  Loader2,
  User,
  Home,
  CreditCard,
  ArrowRight
} from 'lucide-react';
import KYCDocumentUploader from '@/components/KYCDocumentUploader';
import { 
  getUserKYCDocuments, 
  type KYCDocument, 
  type KYCDocumentType, 
  type KYCDocumentCategory 
} from '@/utils/fileStorage';

export default function KYCVerificationPage() {
  const router = useRouter();
  const { user, isAuthenticated, loading: authLoading, kycStatus } = useWalletAuth();
  const supabase = useSupabaseClient();
  
  const [loading, setLoading] = useState(true);
  const [documents, setDocuments] = useState<KYCDocument[]>([]);
  const [activeStep, setActiveStep] = useState(1);
  const [formData, setFormData] = useState({
    fullName: '',
    dateOfBirth: '',
    nationality: '',
    address: '',
    city: '',
    postalCode: '',
    country: '',
    phoneNumber: ''
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [submitting, setSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  
  // Fetch user's KYC documents on component mount
  useEffect(() => {
    const fetchDocuments = async () => {
      if (!user?.id) return;
      
      try {
        setLoading(true);
        const docs = await getUserKYCDocuments(user.id);
        setDocuments(docs);
        
        // Pre-fill form data if user profile exists
        if (user.full_name) {
          setFormData(prev => ({
            ...prev,
            fullName: user.full_name || ''
          }));
        }
        
        // Determine active step based on documents
        const hasIdentityDocs = docs.some(doc => doc.document_category === 'identity');
        const hasAddressDocs = docs.some(doc => doc.document_category === 'address');
        
        if (hasIdentityDocs && hasAddressDocs) {
          setActiveStep(3); // Go to personal information step
        } else if (hasIdentityDocs) {
          setActiveStep(2); // Go to address verification step
        } else {
          setActiveStep(1); // Start with identity verification
        }
      } catch (error) {
        console.error('Error fetching KYC documents:', error);
      } finally {
        setLoading(false);
      }
    };
    
    if (isAuthenticated && user) {
      fetchDocuments();
    } else if (!authLoading && !isAuthenticated) {
      router.push('/');
    }
  }, [isAuthenticated, user, authLoading, router]);
  
  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error for this field
    if (formErrors[name]) {
      setFormErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };
  
  // Validate form data
  const validateForm = () => {
    const errors: Record<string, string> = {};
    
    if (!formData.fullName.trim()) errors.fullName = 'Full name is required';
    if (!formData.dateOfBirth.trim()) errors.dateOfBirth = 'Date of birth is required';
    if (!formData.nationality.trim()) errors.nationality = 'Nationality is required';
    if (!formData.address.trim()) errors.address = 'Address is required';
    if (!formData.city.trim()) errors.city = 'City is required';
    if (!formData.postalCode.trim()) errors.postalCode = 'Postal code is required';
    if (!formData.country.trim()) errors.country = 'Country is required';
    if (!formData.phoneNumber.trim()) errors.phoneNumber = 'Phone number is required';
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;
    if (!user?.id) return;
    
    setSubmitting(true);
    
    try {
      // Update user profile with KYC information
      const { error } = await supabase
        .from('profiles')
        .update({
          full_name: formData.fullName,
          phone: formData.phoneNumber,
          kyc_status: 'pending',
          kyc_data: {
            dateOfBirth: formData.dateOfBirth,
            nationality: formData.nationality,
            address: formData.address,
            city: formData.city,
            postalCode: formData.postalCode,
            country: formData.country
          },
          updated_at: new Date().toISOString()
        })
        .eq('id', user.id);
      
      if (error) throw error;
      
      setSubmitSuccess(true);
      
      // Redirect to dashboard after a short delay
      setTimeout(() => {
        router.push('/dashboard');
      }, 2000);
    } catch (error) {
      console.error('Error submitting KYC information:', error);
    } finally {
      setSubmitting(false);
    }
  };
  
  // Get verification status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'verified':
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-900/30 text-green-300 border border-green-800">
            <CheckCircle2 size={12} className="mr-1" />
            Verified
          </span>
        );
      case 'rejected':
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-red-900/30 text-red-300 border border-red-800">
            <AlertCircle size={12} className="mr-1" />
            Rejected
          </span>
        );
      case 'pending':
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-amber-900/30 text-amber-300 border border-amber-800">
            <Clock size={12} className="mr-1" />
            Pending
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-900/30 text-gray-300 border border-gray-800">
            <Clock size={12} className="mr-1" />
            Not Started
          </span>
        );
    }
  };
  
  if (authLoading || loading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-gray-950 to-indigo-950">
        <div className="flex flex-col items-center">
          <Loader2 className="h-12 w-12 text-indigo-500 animate-spin mb-4" />
          <p className="text-white text-lg">Loading verification status...</p>
        </div>
      </div>
    );
  }
  
  if (!user) {
    return null; // Will redirect in useEffect
  }
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-950 to-indigo-950 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-10 text-center">
          <h1 className="text-3xl font-bold text-white mb-2">KYC Verification</h1>
          <p className="text-gray-400 max-w-2xl mx-auto">
            Complete your identity verification to unlock full platform functionality, including property investments and listings.
          </p>
        </div>
        
        {/* Overall KYC Status */}
        <div className="bg-indigo-900/30 border border-indigo-800/30 rounded-lg p-6 mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Shield className="h-8 w-8 text-indigo-400 mr-3" />
              <div>
                <h2 className="text-xl font-bold text-white">Verification Status</h2>
                <p className="text-gray-400">
                  {kycStatus === 'verified' 
                    ? 'Your identity has been verified' 
                    : kycStatus === 'pending' 
                      ? 'Your verification is being reviewed' 
                      : kycStatus === 'rejected' 
                        ? 'Your verification was rejected' 
                        : 'Complete all steps to verify your identity'}
                </p>
              </div>
            </div>
            <div>
              {getStatusBadge(kycStatus || 'none')}
            </div>
          </div>
        </div>
        
        {/* Verification Steps */}
        <div className="mb-8">
          <div className="flex items-center mb-6">
            <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
              activeStep >= 1 ? 'bg-indigo-600' : 'bg-gray-700'
            }`}>
              <span className="text-white font-bold">1</span>
            </div>
            <div className={`h-1 flex-1 ${
              activeStep >= 2 ? 'bg-indigo-600' : 'bg-gray-700'
            }`}></div>
            <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
              activeStep >= 2 ? 'bg-indigo-600' : 'bg-gray-700'
            }`}>
              <span className="text-white font-bold">2</span>
            </div>
            <div className={`h-1 flex-1 ${
              activeStep >= 3 ? 'bg-indigo-600' : 'bg-gray-700'
            }`}></div>
            <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
              activeStep >= 3 ? 'bg-indigo-600' : 'bg-gray-700'
            }`}>
              <span className="text-white font-bold">3</span>
            </div>
          </div>
          
          {/* Step 1: Identity Verification */}
          <div className={`mb-8 ${activeStep !== 1 && 'hidden'}`}>
            <div className="bg-gray-900/50 border border-gray-800 rounded-lg p-6">
              <div className="flex items-center mb-4">
                <User className="h-6 w-6 text-indigo-400 mr-2" />
                <h3 className="text-xl font-bold text-white">Identity Verification</h3>
              </div>
              <p className="text-gray-400 mb-6">
                Please upload a government-issued ID document such as a passport, driver's license, or national ID card.
              </p>
              
              <div className="space-y-6">
                <KYCDocumentUploader
                  documentType="passport"
                  documentCategory="identity"
                  theme="dark"
                />
                
                <KYCDocumentUploader
                  documentType="drivers_license"
                  documentCategory="identity"
                  theme="dark"
                />
                
                <KYCDocumentUploader
                  documentType="national_id"
                  documentCategory="identity"
                  theme="dark"
                />
              </div>
              
              <div className="mt-6 flex justify-end">
                <button
                  onClick={() => setActiveStep(2)}
                  className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 rounded-lg text-white font-medium flex items-center"
                >
                  <span>Continue</span>
                  <ChevronRight className="h-5 w-5 ml-1" />
                </button>
              </div>
            </div>
          </div>
          
          {/* Step 2: Address Verification */}
          <div className={`mb-8 ${activeStep !== 2 && 'hidden'}`}>
            <div className="bg-gray-900/50 border border-gray-800 rounded-lg p-6">
              <div className="flex items-center mb-4">
                <Home className="h-6 w-6 text-indigo-400 mr-2" />
                <h3 className="text-xl font-bold text-white">Address Verification</h3>
              </div>
              <p className="text-gray-400 mb-6">
                Please upload a document that verifies your current address, such as a utility bill, bank statement, or official government correspondence.
              </p>
              
              <div className="space-y-6">
                <KYCDocumentUploader
                  documentType="proof_of_address"
                  documentCategory="address"
                  theme="dark"
                />
                
                <KYCDocumentUploader
                  documentType="bank_statement"
                  documentCategory="address"
                  theme="dark"
                />
              </div>
              
              <div className="mt-6 flex justify-between">
                <button
                  onClick={() => setActiveStep(1)}
                  className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg text-white font-medium"
                >
                  Back
                </button>
                <button
                  onClick={() => setActiveStep(3)}
                  className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 rounded-lg text-white font-medium flex items-center"
                >
                  <span>Continue</span>
                  <ChevronRight className="h-5 w-5 ml-1" />
                </button>
              </div>
            </div>
          </div>
          
          {/* Step 3: Personal Information */}
          <div className={`mb-8 ${activeStep !== 3 && 'hidden'}`}>
            <div className="bg-gray-900/50 border border-gray-800 rounded-lg p-6">
              <div className="flex items-center mb-4">
                <CreditCard className="h-6 w-6 text-indigo-400 mr-2" />
                <h3 className="text-xl font-bold text-white">Personal Information</h3>
              </div>
              <p className="text-gray-400 mb-6">
                Please provide additional personal information to complete your verification.
              </p>
              
              {submitSuccess ? (
                <div className="bg-green-900/30 border border-green-800 rounded-lg p-6 text-center">
                  <CheckCircle2 className="h-12 w-12 text-green-500 mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-white mb-2">Verification Submitted</h3>
                  <p className="text-gray-300 mb-4">
                    Your KYC verification has been submitted successfully and is pending review.
                  </p>
                  <p className="text-gray-400 text-sm">Redirecting to dashboard...</p>
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">Full Name</label>
                      <input
                        type="text"
                        name="fullName"
                        value={formData.fullName}
                        onChange={handleInputChange}
                        className={`w-full px-3 py-2 bg-gray-800 border ${
                          formErrors.fullName ? 'border-red-500' : 'border-gray-700'
                        } rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500`}
                      />
                      {formErrors.fullName && (
                        <p className="mt-1 text-sm text-red-500">{formErrors.fullName}</p>
                      )}
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">Date of Birth</label>
                      <input
                        type="date"
                        name="dateOfBirth"
                        value={formData.dateOfBirth}
                        onChange={handleInputChange}
                        className={`w-full px-3 py-2 bg-gray-800 border ${
                          formErrors.dateOfBirth ? 'border-red-500' : 'border-gray-700'
                        } rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500`}
                      />
                      {formErrors.dateOfBirth && (
                        <p className="mt-1 text-sm text-red-500">{formErrors.dateOfBirth}</p>
                      )}
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">Nationality</label>
                      <input
                        type="text"
                        name="nationality"
                        value={formData.nationality}
                        onChange={handleInputChange}
                        className={`w-full px-3 py-2 bg-gray-800 border ${
                          formErrors.nationality ? 'border-red-500' : 'border-gray-700'
                        } rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500`}
                      />
                      {formErrors.nationality && (
                        <p className="mt-1 text-sm text-red-500">{formErrors.nationality}</p>
                      )}
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">Phone Number</label>
                      <input
                        type="tel"
                        name="phoneNumber"
                        value={formData.phoneNumber}
                        onChange={handleInputChange}
                        className={`w-full px-3 py-2 bg-gray-800 border ${
                          formErrors.phoneNumber ? 'border-red-500' : 'border-gray-700'
                        } rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500`}
                      />
                      {formErrors.phoneNumber && (
                        <p className="mt-1 text-sm text-red-500">{formErrors.phoneNumber}</p>
                      )}
                    </div>
                    
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-300 mb-1">Address</label>
                      <input
                        type="text"
                        name="address"
                        value={formData.address}
                        onChange={handleInputChange}
                        className={`w-full px-3 py-2 bg-gray-800 border ${
                          formErrors.address ? 'border-red-500' : 'border-gray-700'
                        } rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500`}
                      />
                      {formErrors.address && (
                        <p className="mt-1 text-sm text-red-500">{formErrors.address}</p>
                      )}
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">City</label>
                      <input
                        type="text"
                        name="city"
                        value={formData.city}
                        onChange={handleInputChange}
                        className={`w-full px-3 py-2 bg-gray-800 border ${
                          formErrors.city ? 'border-red-500' : 'border-gray-700'
                        } rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500`}
                      />
                      {formErrors.city && (
                        <p className="mt-1 text-sm text-red-500">{formErrors.city}</p>
                      )}
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">Postal Code</label>
                      <input
                        type="text"
                        name="postalCode"
                        value={formData.postalCode}
                        onChange={handleInputChange}
                        className={`w-full px-3 py-2 bg-gray-800 border ${
                          formErrors.postalCode ? 'border-red-500' : 'border-gray-700'
                        } rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500`}
                      />
                      {formErrors.postalCode && (
                        <p className="mt-1 text-sm text-red-500">{formErrors.postalCode}</p>
                      )}
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">Country</label>
                      <input
                        type="text"
                        name="country"
                        value={formData.country}
                        onChange={handleInputChange}
                        className={`w-full px-3 py-2 bg-gray-800 border ${
                          formErrors.country ? 'border-red-500' : 'border-gray-700'
                        } rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500`}
                      />
                      {formErrors.country && (
                        <p className="mt-1 text-sm text-red-500">{formErrors.country}</p>
                      )}
                    </div>
                  </div>
                  
                  <div className="mt-6 flex justify-between">
                    <button
                      type="button"
                      onClick={() => setActiveStep(2)}
                      className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg text-white font-medium"
                    >
                      Back
                    </button>
                    <button
                      type="submit"
                      disabled={submitting}
                      className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 rounded-lg text-white font-medium flex items-center"
                    >
                      {submitting ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                          <span>Submitting...</span>
                        </>
                      ) : (
                        <>
                          <span>Submit Verification</span>
                          <ArrowRight className="h-5 w-5 ml-1" />
                        </>
                      )}
                    </button>
                  </div>
                </form>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
