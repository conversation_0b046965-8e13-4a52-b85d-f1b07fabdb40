'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAccount, useBalance, useChainId } from 'wagmi';
import { formatAddress, getNetworkName } from '@/utils/web3';

// Define the context type
interface WalletContextType {
  isConnected: boolean;
  address: string | undefined;
  displayAddress: string;
  chainId: number | undefined;
  networkName: string;
  balance: string | undefined;
  isWalletModalOpen: boolean;
  openWalletModal: () => void;
  closeWalletModal: () => void;
}

// Create the context with default values
const WalletContext = createContext<WalletContextType>({
  isConnected: false,
  address: undefined,
  displayAddress: '',
  chainId: undefined,
  networkName: 'Not Connected',
  balance: undefined,
  isWalletModalOpen: false,
  openWalletModal: () => {},
  closeWalletModal: () => {},
});

// Hook to use the wallet context
export const useWallet = () => useContext(WalletContext);

// Provider component
export function WalletProvider({ children }: { children: ReactNode }) {
  // Check if we're in a browser environment
  const isBrowser = typeof window !== 'undefined';

  // Local state for wallet modal and mounted state
  const [isWalletModalOpen, setIsWalletModalOpen] = useState(false);
  const [mounted, setMounted] = useState(false);

  // Wait until after client-side hydration to use wagmi hooks
  useEffect(() => {
    setMounted(true);
  }, []);

  // Use wagmi hooks to get wallet state (only when mounted)
  const { address, isConnected } = useAccount();
  const chainId = useChainId();
  const { data: balanceData } = useBalance({
    address: mounted ? address : undefined,
  });

  // Format the address for display
  const displayAddress = address ? formatAddress(address) : '';

  // Get network name
  const networkName = getNetworkName(chainId);

  // Open and close wallet modal functions
  const openWalletModal = () => setIsWalletModalOpen(true);
  const closeWalletModal = () => setIsWalletModalOpen(false);

  // Close modal when connected
  useEffect(() => {
    if (isConnected) {
      closeWalletModal();
    }
  }, [isConnected]);

  // Context value
  const value = {
    isConnected: mounted ? isConnected : false,
    address: mounted ? address : undefined,
    displayAddress: mounted ? displayAddress : '',
    chainId: mounted ? chainId : undefined,
    networkName: mounted ? networkName : 'Not Connected',
    balance: mounted ? balanceData?.formatted : undefined,
    isWalletModalOpen,
    openWalletModal,
    closeWalletModal,
  };

  return (
    <WalletContext.Provider value={value}>
      {children}
    </WalletContext.Provider>
  );
}
