const hre = require("hardhat");

async function main() {
  console.log("Deploying BrickChain contracts...");

  // Get the contract factories
  const PropertyRegistry = await hre.ethers.getContractFactory("PropertyRegistry");
  const FractionalOwnership = await hre.ethers.getContractFactory("FractionalOwnership");
  const BrickToken = await hre.ethers.getContractFactory("BrickToken");
  const InvestmentManager = await hre.ethers.getContractFactory("InvestmentManager");
  const AssetMarketplace = await hre.ethers.getContractFactory("AssetMarketplace");

  // Deploy PropertyRegistry (ERC-721)
  console.log("Deploying PropertyRegistry...");
  const propertyRegistry = await PropertyRegistry.deploy();
  await propertyRegistry.waitForDeployment();
  const propertyRegistryAddress = await propertyRegistry.getAddress();
  console.log(`PropertyRegistry deployed to: ${propertyRegistryAddress}`);

  // Deploy FractionalOwnership (ERC-1155)
  console.log("Deploying FractionalOwnership...");
  const fractionalOwnership = await FractionalOwnership.deploy();
  await fractionalOwnership.waitForDeployment();
  const fractionalOwnershipAddress = await fractionalOwnership.getAddress();
  console.log(`FractionalOwnership deployed to: ${fractionalOwnershipAddress}`);

  // Deploy BrickToken (ERC-20)
  console.log("Deploying BrickToken...");
  const brickToken = await BrickToken.deploy();
  await brickToken.waitForDeployment();
  const brickTokenAddress = await brickToken.getAddress();
  console.log(`BrickToken deployed to: ${brickTokenAddress}`);

  // Deploy InvestmentManager
  console.log("Deploying InvestmentManager...");
  const investmentManager = await InvestmentManager.deploy(
    propertyRegistryAddress,
    fractionalOwnershipAddress,
    brickTokenAddress
  );
  await investmentManager.waitForDeployment();
  const investmentManagerAddress = await investmentManager.getAddress();
  console.log(`InvestmentManager deployed to: ${investmentManagerAddress}`);

  // Deploy AssetMarketplace
  console.log("Deploying AssetMarketplace...");
  const assetMarketplace = await AssetMarketplace.deploy(
    propertyRegistryAddress,
    fractionalOwnershipAddress,
    brickTokenAddress
  );
  await assetMarketplace.waitForDeployment();
  const assetMarketplaceAddress = await assetMarketplace.getAddress();
  console.log(`AssetMarketplace deployed to: ${assetMarketplaceAddress}`);

  // Set up contract connections
  console.log("Setting up contract connections...");

  // Set investment manager in PropertyRegistry
  await propertyRegistry.setInvestmentManager(investmentManagerAddress);
  console.log("Investment manager set in PropertyRegistry");

  // Set investment manager in FractionalOwnership
  await fractionalOwnership.setInvestmentManager(investmentManagerAddress);
  console.log("Investment manager set in FractionalOwnership");

  // Set investment manager and marketplace in BrickToken
  await brickToken.setInvestmentManager(investmentManagerAddress);
  await brickToken.setMarketplace(assetMarketplaceAddress);
  console.log("Investment manager and marketplace set in BrickToken");

  console.log("\nDeployment complete!");
  console.log("======================");
  console.log(`PropertyRegistry (ERC-721): ${propertyRegistryAddress}`);
  console.log(`FractionalOwnership (ERC-1155): ${fractionalOwnershipAddress}`);
  console.log(`BrickToken (ERC-20): ${brickTokenAddress}`);
  console.log(`InvestmentManager: ${investmentManagerAddress}`);
  console.log(`AssetMarketplace: ${assetMarketplaceAddress}`);

  // Save addresses to .env file format for easy copying
  console.log("\nAdd these to your .env.local file:");
  console.log("======================");
  console.log(`NEXT_PUBLIC_PROPERTY_REGISTRY_ADDRESS=${propertyRegistryAddress}`);
  console.log(`NEXT_PUBLIC_FRACTIONAL_OWNERSHIP_ADDRESS=${fractionalOwnershipAddress}`);
  console.log(`NEXT_PUBLIC_BRICK_TOKEN_ADDRESS=${brickTokenAddress}`);
  console.log(`NEXT_PUBLIC_INVESTMENT_MANAGER_ADDRESS=${investmentManagerAddress}`);
  console.log(`NEXT_PUBLIC_ASSET_MARKETPLACE_ADDRESS=${assetMarketplaceAddress}`);
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
