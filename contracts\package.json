{"name": "brickchain-contracts", "version": "1.0.0", "description": "Smart contracts for the BrickChain platform", "main": "index.js", "scripts": {"compile": "hardhat compile", "test": "hardhat test", "deploy:local": "hardhat run scripts/deploy.js --network hardhat", "deploy:sonicblaze": "hardhat run scripts/deploy.js --network sonicBlaze", "deploy:sepolia": "hardhat run scripts/deploy.js --network sepolia"}, "dependencies": {"@openzeppelin/contracts": "^5.0.0"}, "devDependencies": {"@nomicfoundation/hardhat-chai-matchers": "^2.0.0", "@nomicfoundation/hardhat-ethers": "^3.0.0", "@nomicfoundation/hardhat-network-helpers": "^1.0.0", "@nomicfoundation/hardhat-toolbox": "^4.0.0", "@nomicfoundation/hardhat-verify": "^2.0.0", "chai": "^4.3.7", "dotenv": "^16.3.1", "ethers": "^6.8.1", "hardhat": "^2.19.1", "hardhat-gas-reporter": "^1.0.9", "solidity-coverage": "^0.8.5"}}