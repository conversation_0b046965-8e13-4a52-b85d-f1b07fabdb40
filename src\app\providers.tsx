'use client';

import React, { useState } from 'react';
import { ThemeProvider } from '../context/ThemeContext';
import { NavigationProvider } from '../context/NavigationContext';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { SessionContextProvider } from '@supabase/auth-helpers-react';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import Web3Provider from '@/components/Web3Provider';
import { WalletAuthProvider } from '@/context/WalletAuthContext';
import { ToastProvider } from '@/context/ToastContext';
import ErrorBoundary from '@/components/ErrorBoundary';

export default function Providers({ children }: { children: React.ReactNode }) {
  const [supabaseClient] = useState(() => createClientComponentClient({
    supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL!,
    supabaseKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  }));

  return (
    <ErrorBoundary>
      <Web3Provider>
        <SessionContextProvider supabaseClient={supabaseClient}>
          <WalletAuthProvider>
            <ThemeProvider>
              <NavigationProvider>
                <ToastProvider position="top-right">
                  <div className="flex flex-col min-h-screen">
                    <Navbar />
                    <main className="flex-grow">
                      {children}
                    </main>
                    <Footer />
                  </div>
                </ToastProvider>
              </NavigationProvider>
            </ThemeProvider>
          </WalletAuthProvider>
        </SessionContextProvider>
      </Web3Provider>
    </ErrorBoundary>
  );
}