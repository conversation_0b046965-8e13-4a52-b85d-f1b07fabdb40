/**
 * Image Processing Utilities
 * 
 * This file contains utilities for image optimization, thumbnail generation,
 * and metadata extraction to improve image loading performance and user experience.
 */

import sharp from 'sharp';

// Define image optimization options
export interface ImageOptimizationOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  format?: 'jpeg' | 'png' | 'webp' | 'avif';
  progressive?: boolean;
}

// Define image metadata
export interface ImageMetadata {
  width: number;
  height: number;
  format: string;
  size: number;
  hasAlpha?: boolean;
  exif?: Record<string, any>;
}

/**
 * Optimize an image for web display
 * 
 * @param imageFile Original image file
 * @param options Optimization options
 * @returns Promise with optimized image as Buffer
 */
export async function optimizeImage(
  imageFile: File,
  options: ImageOptimizationOptions = {}
): Promise<{ buffer: Buffer; metadata: ImageMetadata }> {
  // Default optimization options
  const {
    maxWidth = 1920,
    maxHeight = 1080,
    quality = 80,
    format = 'webp',
    progressive = true
  } = options;

  try {
    // Convert File to Buffer
    const arrayBuffer = await imageFile.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    
    // Create Sharp instance
    let image = sharp(buffer);
    
    // Get image metadata
    const metadata = await image.metadata();
    
    // Resize if needed (maintaining aspect ratio)
    if (metadata.width && metadata.height) {
      if (metadata.width > maxWidth || metadata.height > maxHeight) {
        image = image.resize({
          width: maxWidth,
          height: maxHeight,
          fit: 'inside',
          withoutEnlargement: true
        });
      }
    }
    
    // Convert to desired format
    switch (format) {
      case 'jpeg':
        image = image.jpeg({ quality, progressive });
        break;
      case 'png':
        image = image.png({ progressive });
        break;
      case 'webp':
        image = image.webp({ quality });
        break;
      case 'avif':
        image = image.avif({ quality });
        break;
    }
    
    // Process the image
    const outputBuffer = await image.toBuffer({ resolveWithObject: true });
    
    // Return the optimized image and its metadata
    return {
      buffer: outputBuffer.data,
      metadata: {
        width: outputBuffer.info.width,
        height: outputBuffer.info.height,
        format: outputBuffer.info.format,
        size: outputBuffer.info.size,
        hasAlpha: metadata.hasAlpha,
        exif: metadata.exif ? metadata.exif : undefined
      }
    };
  } catch (error) {
    console.error('Error optimizing image:', error);
    throw new Error(`Failed to optimize image: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Generate a thumbnail from an image
 * 
 * @param imageFile Original image file
 * @param width Thumbnail width
 * @param height Thumbnail height
 * @returns Promise with thumbnail as Buffer
 */
export async function generateThumbnail(
  imageFile: File,
  width: number = 300,
  height: number = 300
): Promise<Buffer> {
  try {
    // Convert File to Buffer
    const arrayBuffer = await imageFile.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    
    // Generate thumbnail
    const thumbnail = await sharp(buffer)
      .resize({
        width,
        height,
        fit: 'cover',
        position: 'centre'
      })
      .webp({ quality: 80 })
      .toBuffer();
    
    return thumbnail;
  } catch (error) {
    console.error('Error generating thumbnail:', error);
    throw new Error(`Failed to generate thumbnail: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Extract metadata from an image
 * 
 * @param imageFile Image file
 * @returns Promise with image metadata
 */
export async function extractImageMetadata(imageFile: File): Promise<ImageMetadata> {
  try {
    // Convert File to Buffer
    const arrayBuffer = await imageFile.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    
    // Get image metadata
    const metadata = await sharp(buffer).metadata();
    
    return {
      width: metadata.width || 0,
      height: metadata.height || 0,
      format: metadata.format || '',
      size: buffer.length,
      hasAlpha: metadata.hasAlpha,
      exif: metadata.exif ? metadata.exif : undefined
    };
  } catch (error) {
    console.error('Error extracting image metadata:', error);
    throw new Error(`Failed to extract image metadata: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
