'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@supabase/supabase-js';
import { Plus, Search, Filter, Edit, Trash2, Loader2 } from 'lucide-react';
import LoadingSpinner from '@/components/LoadingSpinner';
import ReturnForm from '@/components/ReturnForm';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

type Return = {
  id: string;
  property_id: string;
  property_name: string;
  amount: number;
  date: string;
  status: 'pending' | 'completed' | 'failed';
  investor_id: string;
  investor_email: string;
};

export default function OwnerReturnsPage() {
  const [returns, setReturns] = useState<Return[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedReturn, setSelectedReturn] = useState<Return | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [statusFilter, setStatusFilter] = useState<string>('all');

  useEffect(() => {
    fetchReturns();
  }, []);

  const fetchReturns = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      const { data, error } = await supabase
        .from('returns')
        .select(`
          *,
          properties(name),
          profiles(email)
        `)
        .eq('property_id', supabase
          .from('properties')
          .select('id')
          .eq('owner_id', user.id)
        )
        .order('date', { ascending: false });

      if (error) throw error;

      const formattedReturns = data?.map(ret => ({
        id: ret.id,
        property_id: ret.property_id,
        property_name: ret.properties.name,
        amount: ret.amount,
        date: ret.date,
        status: ret.status,
        investor_id: ret.investor_id,
        investor_email: ret.profiles.email
      })) || [];

      setReturns(formattedReturns);
    } catch (error) {
      console.error('Error fetching returns:', error);
      setError('Failed to load returns');
    } finally {
      setLoading(false);
    }
  };

  const filteredReturns = returns.filter(ret => {
    const matchesSearch = 
      ret.property_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      ret.investor_email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || ret.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const handleUpdateStatus = async (id: string, newStatus: Return['status']) => {
    try {
      const { error } = await supabase
        .from('returns')
        .update({ status: newStatus })
        .eq('id', id);

      if (error) throw error;
      setReturns(returns.map(ret => 
        ret.id === id ? { ...ret, status: newStatus } : ret
      ));
    } catch (error) {
      console.error('Error updating return status:', error);
      setError('Failed to update return status');
    }
  };

  const handleSaveReturn = async (data: any) => {
    try {
      if (selectedReturn) {
        // Update existing return
        const { error } = await supabase
          .from('returns')
          .update({
            property_id: data.property_id,
            investor_id: data.investor_id,
            amount: data.amount,
            date: data.date,
            status: data.status
          })
          .eq('id', selectedReturn.id);

        if (error) throw error;
      } else {
        // Create new return
        const { error } = await supabase
          .from('returns')
          .insert([{
            property_id: data.property_id,
            investor_id: data.investor_id,
            amount: data.amount,
            date: data.date,
            status: data.status
          }]);

        if (error) throw error;
      }

      // Refresh returns list
      await fetchReturns();
      setIsModalOpen(false);
    } catch (error) {
      console.error('Error saving return:', error);
      setError('Failed to save return');
    }
  };

  const handleDeleteReturn = async (id: string) => {
    try {
      const { error } = await supabase
        .from('returns')
        .delete()
        .eq('id', id);

      if (error) throw error;
      await fetchReturns();
    } catch (error) {
      console.error('Error deleting return:', error);
      setError('Failed to delete return');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-red-500">{error}</div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <h1 className="text-2xl font-bold">Property Returns</h1>
        <button
          onClick={() => {
            setSelectedReturn(null);
            setIsModalOpen(true);
          }}
          className="flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus size={20} />
          Add Return
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <input
            type="text"
            placeholder="Search returns..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div className="relative">
          <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Status</option>
            <option value="pending">Pending</option>
            <option value="completed">Completed</option>
            <option value="failed">Failed</option>
          </select>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Property
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Investor
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Amount
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Date
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredReturns.map((ret) => (
              <tr key={ret.id}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">{ret.property_name}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">{ret.investor_email}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium">${ret.amount.toLocaleString()}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-500">
                    {new Date(ret.date).toLocaleDateString()}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <select
                    value={ret.status}
                    onChange={(e) => handleUpdateStatus(ret.id, e.target.value as Return['status'])}
                    className={`text-sm border rounded px-2 py-1 ${
                      ret.status === 'completed' ? 'text-green-600' :
                      ret.status === 'failed' ? 'text-red-600' :
                      'text-yellow-600'
                    }`}
                  >
                    <option value="pending">Pending</option>
                    <option value="completed">Completed</option>
                    <option value="failed">Failed</option>
                  </select>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button
                    onClick={() => {
                      setSelectedReturn(ret);
                      setIsModalOpen(true);
                    }}
                    className="text-blue-600 hover:text-blue-900 mr-4"
                  >
                    <Edit size={16} />
                  </button>
                  <button
                    onClick={() => {
                      if (confirm('Are you sure you want to delete this return?')) {
                        handleDeleteReturn(ret.id);
                      }
                    }}
                    className="text-red-600 hover:text-red-900"
                  >
                    <Trash2 size={16} />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {filteredReturns.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500">No returns found</p>
        </div>
      )}

      {/* Return Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h2 className="text-xl font-bold mb-4">
              {selectedReturn ? 'Edit Return' : 'Add Return'}
            </h2>
            {/* Add your return form here */}
            <div className="flex justify-end gap-2 mt-6">
              <button
                onClick={() => setIsModalOpen(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  // Handle save
                  setIsModalOpen(false);
                }}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Save
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 