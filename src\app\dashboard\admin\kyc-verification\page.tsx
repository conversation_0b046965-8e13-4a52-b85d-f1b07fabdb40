'use client';
import React, { useState, useEffect } from 'react';
import { useSupabaseClient } from '@supabase/auth-helpers-react';
import { useUser } from '@/context/UserContext';
import { 
  Shield, AlertCircle, CheckCircle, XCircle, Clock, FileText, 
  Search, Filter, User, Eye, ThumbsUp, ThumbsDown, Loader2 
} from 'lucide-react';
import LoadingSpinner from '@/components/LoadingSpinner';

type VerificationStatus = 'pending' | 'verified' | 'rejected' | 'expired' | 'all';

interface KYCDocument {
  id: string;
  user_id: string;
  document_type: string;
  document_category: string;
  file_path: string;
  file_name: string;
  file_size: number;
  file_type: string;
  verification_status: VerificationStatus;
  verification_notes?: string;
  verified_by?: string;
  verified_at?: string;
  expires_at?: string;
  created_at: string;
  updated_at: string;
  // Join with profiles
  profiles?: {
    full_name: string;
    email: string;
  };
}

export default function KYCVerificationPage() {
  const { user } = useUser();
  const supabase = useSupabaseClient();
  const [loading, setLoading] = useState<boolean>(true);
  const [documents, setDocuments] = useState<KYCDocument[]>([]);
  const [error, setError] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<VerificationStatus>('pending');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [selectedDocument, setSelectedDocument] = useState<KYCDocument | null>(null);
  const [verificationNote, setVerificationNote] = useState<string>('');
  const [processingAction, setProcessingAction] = useState<boolean>(false);

  // Check if user is admin
  const [isAdmin, setIsAdmin] = useState<boolean>(false);
  useEffect(() => {
    const checkAdminStatus = async () => {
      if (!user?.id) return;
      
      try {
        const { data: profile, error } = await supabase
          .from('profiles')
          .select('role')
          .eq('id', user.id)
          .single();
        
        if (error) throw error;
        setIsAdmin(profile.role === 'admin');
      } catch (err) {
        console.error('Error checking admin status:', err);
        setError('Failed to verify admin privileges');
      }
    };
    
    checkAdminStatus();
  }, [user?.id, supabase]);

  // Fetch documents
  useEffect(() => {
    const fetchDocuments = async () => {
      if (!user?.id || !isAdmin) return;
      
      try {
        setLoading(true);
        
        let query = supabase
          .from('kyc_documents')
          .select(`
            *,
            profiles:user_id (
              full_name,
              email
            )
          `);
        
        // Apply status filter
        if (statusFilter !== 'all') {
          query = query.eq('verification_status', statusFilter);
        }
        
        // Order by created date (newest first)
        query = query.order('created_at', { ascending: false });
        
        const { data, error: fetchError } = await query;
        
        if (fetchError) throw fetchError;
        setDocuments(data || []);
      } catch (err) {
        console.error('Error fetching KYC documents:', err);
        setError('Failed to load verification documents');
      } finally {
        setLoading(false);
      }
    };

    if (user?.id && isAdmin) {
      fetchDocuments();
    } else {
      setLoading(false);
    }
  }, [user?.id, isAdmin, statusFilter, supabase]);

  // Filter documents by search query
  const filteredDocuments = documents.filter(doc => {
    if (!searchQuery) return true;
    
    const searchLower = searchQuery.toLowerCase();
    return (
      doc.profiles?.full_name.toLowerCase().includes(searchLower) ||
      doc.profiles?.email.toLowerCase().includes(searchLower) ||
      doc.file_name.toLowerCase().includes(searchLower) ||
      doc.document_type.toLowerCase().includes(searchLower)
    );
  });

  // Handle document verification
  const handleVerify = async (status: 'verified' | 'rejected') => {
    if (!selectedDocument) return;
    
    try {
      setProcessingAction(true);
      
      const response = await fetch(`/api/kyc-documents?documentId=${selectedDocument.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          verificationStatus: status,
          verificationNotes: verificationNote
        })
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update document status');
      }
      
      // Update the document in the local state
      setDocuments(prev => prev.map(doc => 
        doc.id === selectedDocument.id
          ? { 
              ...doc, 
              verification_status: status,
              verification_notes: verificationNote,
              verified_by: user?.id,
              verified_at: new Date().toISOString()
            }
          : doc
      ));
      
      // Close the document viewer
      setSelectedDocument(null);
      setVerificationNote('');
    } catch (err) {
      console.error('Error updating document status:', err);
      setError(`Failed to ${status === 'verified' ? 'approve' : 'reject'} document`);
    } finally {
      setProcessingAction(false);
    }
  };

  // Format document type for display
  const formatDocumentType = (type: string) => {
    return type
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Get status badge
  const getStatusBadge = (status: VerificationStatus) => {
    switch (status) {
      case 'verified':
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
            <CheckCircle size={12} className="mr-1" />
            Verified
          </span>
        );
      case 'rejected':
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-red-100 text-red-800">
            <XCircle size={12} className="mr-1" />
            Rejected
          </span>
        );
      case 'expired':
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-yellow-100 text-yellow-800">
            <Clock size={12} className="mr-1" />
            Expired
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
            <Clock size={12} className="mr-1" />
            Pending
          </span>
        );
    }
  };

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-white rounded-lg shadow-md p-6 text-center">
          <p className="text-gray-700">Please log in to access the KYC verification dashboard.</p>
        </div>
      </div>
    );
  }

  if (!isAdmin) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-white rounded-lg shadow-md p-6 text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-bold text-gray-900 mb-2">Access Denied</h2>
          <p className="text-gray-700">You do not have permission to access the KYC verification dashboard.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">KYC Verification Dashboard</h1>
          <p className="text-gray-600 mt-1">
            Review and verify user identity documents.
          </p>
        </div>
        
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg text-red-700">
            <div className="flex items-center">
              <AlertCircle className="h-5 w-5 mr-2" />
              <span>{error}</span>
            </div>
          </div>
        )}
        
        {/* Filters and Search */}
        <div className="mb-6 flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search by name, email, or document type..."
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-indigo-500"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          
          <div className="flex items-center">
            <Filter className="h-5 w-5 text-gray-400 mr-2" />
            <select
              className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as VerificationStatus)}
            >
              <option value="pending">Pending</option>
              <option value="verified">Verified</option>
              <option value="rejected">Rejected</option>
              <option value="expired">Expired</option>
              <option value="all">All Documents</option>
            </select>
          </div>
        </div>
        
        {/* Documents List */}
        {loading ? (
          <div className="flex justify-center py-8">
            <LoadingSpinner size="large" />
          </div>
        ) : filteredDocuments.length === 0 ? (
          <div className="text-center py-8 border border-gray-200 rounded-lg">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-3" />
            <h3 className="text-lg font-medium text-gray-900">No documents found</h3>
            <p className="text-gray-500 mt-1">
              {statusFilter === 'all' 
                ? 'There are no documents matching your search criteria.' 
                : `There are no ${statusFilter} documents to review.`}
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    User
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Document
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Uploaded
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredDocuments.map((doc) => (
                  <tr key={doc.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10 bg-gray-200 rounded-full flex items-center justify-center">
                          <User className="h-5 w-5 text-gray-500" />
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {doc.profiles?.full_name || 'Unknown User'}
                          </div>
                          <div className="text-sm text-gray-500">
                            {doc.profiles?.email || doc.user_id}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{doc.file_name}</div>
                      <div className="text-xs text-gray-500">
                        {(doc.file_size / 1024).toFixed(1)} KB
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {formatDocumentType(doc.document_type)}
                      </div>
                      <div className="text-xs text-gray-500 capitalize">
                        {doc.document_category}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(doc.verification_status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(doc.created_at).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => {
                          setSelectedDocument(doc);
                          setVerificationNote(doc.verification_notes || '');
                        }}
                        className="text-indigo-600 hover:text-indigo-900 flex items-center"
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        Review
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
        
        {/* Document Viewer Modal */}
        {selectedDocument && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-hidden">
              <div className="p-6 border-b border-gray-200">
                <div className="flex justify-between items-center">
                  <h2 className="text-xl font-semibold text-gray-900">
                    Document Review
                  </h2>
                  <button
                    onClick={() => setSelectedDocument(null)}
                    className="text-gray-400 hover:text-gray-500"
                  >
                    <XCircle className="h-6 w-6" />
                  </button>
                </div>
              </div>
              
              <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
                {/* User Info */}
                <div className="mb-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">User Information</h3>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-gray-500">Name</p>
                        <p className="font-medium">{selectedDocument.profiles?.full_name || 'Unknown'}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Email</p>
                        <p className="font-medium">{selectedDocument.profiles?.email || 'Unknown'}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">User ID</p>
                        <p className="font-medium text-xs">{selectedDocument.user_id}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Current Status</p>
                        <div className="mt-1">
                          {getStatusBadge(selectedDocument.verification_status)}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Document Info */}
                <div className="mb-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Document Information</h3>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-gray-500">Document Type</p>
                        <p className="font-medium">{formatDocumentType(selectedDocument.document_type)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Category</p>
                        <p className="font-medium capitalize">{selectedDocument.document_category}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">File Name</p>
                        <p className="font-medium">{selectedDocument.file_name}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">File Size</p>
                        <p className="font-medium">{(selectedDocument.file_size / 1024).toFixed(1)} KB</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Uploaded On</p>
                        <p className="font-medium">{new Date(selectedDocument.created_at).toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">File Type</p>
                        <p className="font-medium">{selectedDocument.file_type}</p>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Document Viewer */}
                <div className="mb-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Document Preview</h3>
                  <div className="bg-gray-100 border border-gray-200 rounded-lg p-4 flex items-center justify-center min-h-[200px]">
                    <div className="text-center">
                      <FileText className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                      <p className="text-gray-500">
                        Document preview not available. Please download the document to view it.
                      </p>
                      <button
                        className="mt-4 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
                        onClick={() => {
                          // Implement document download or viewing logic
                          alert('Document download functionality would be implemented here');
                        }}
                      >
                        Download Document
                      </button>
                    </div>
                  </div>
                </div>
                
                {/* Verification Notes */}
                <div className="mb-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Verification Notes</h3>
                  <textarea
                    className="w-full border border-gray-300 rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                    rows={4}
                    placeholder="Add notes about this document (will be visible to the user)"
                    value={verificationNote}
                    onChange={(e) => setVerificationNote(e.target.value)}
                  />
                </div>
              </div>
              
              <div className="p-6 border-t border-gray-200 flex justify-end space-x-4">
                <button
                  onClick={() => setSelectedDocument(null)}
                  className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
                  disabled={processingAction}
                >
                  Cancel
                </button>
                <button
                  onClick={() => handleVerify('rejected')}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 flex items-center"
                  disabled={processingAction}
                >
                  {processingAction ? (
                    <Loader2 className="animate-spin h-4 w-4 mr-2" />
                  ) : (
                    <ThumbsDown className="h-4 w-4 mr-2" />
                  )}
                  Reject
                </button>
                <button
                  onClick={() => handleVerify('verified')}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center"
                  disabled={processingAction}
                >
                  {processingAction ? (
                    <Loader2 className="animate-spin h-4 w-4 mr-2" />
                  ) : (
                    <ThumbsUp className="h-4 w-4 mr-2" />
                  )}
                  Approve
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
