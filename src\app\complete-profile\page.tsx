'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useWalletAuth } from '@/context/WalletAuthContext';
import { useSupabaseClient } from '@supabase/auth-helpers-react';
import { Mail, User, Phone, Loader2, CheckCircle2 } from 'lucide-react';

export default function CompleteProfile() {
  const router = useRouter();
  const { user, isAuthenticated, walletAddress, refreshUser, loading: authLoading } = useWalletAuth();
  const supabase = useSupabaseClient();
  
  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  
  // Redirect if not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/');
    }
  }, [authLoading, isAuthenticated, router]);
  
  // Pre-fill form if user data exists
  useEffect(() => {
    if (user) {
      if (user.full_name) setFullName(user.full_name);
      if (user.email) setEmail(user.email);
      if (user.phone) setPhone(user.phone);
      
      // If profile is already complete, redirect to dashboard
      if (user.full_name && user.email) {
        router.push('/dashboard');
      }
    }
  }, [user, router]);
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!isAuthenticated || !user) {
      setError('You must be authenticated to complete your profile');
      return;
    }
    
    if (!fullName || !email) {
      setError('Name and email are required');
      return;
    }
    
    try {
      setLoading(true);
      setError('');
      
      // Update the user's profile
      const { error: updateError } = await supabase
        .from('profiles')
        .update({
          full_name: fullName,
          email: email,
          phone: phone || null,
          updated_at: new Date().toISOString()
        })
        .eq('id', user.id);
      
      if (updateError) {
        throw updateError;
      }
      
      // Refresh the user data
      await refreshUser();
      
      // Show success message
      setSuccess(true);
      
      // Redirect to dashboard after a short delay
      setTimeout(() => {
        router.push('/dashboard');
      }, 2000);
    } catch (err: any) {
      console.error('Error updating profile:', err);
      setError(err.message || 'Failed to update profile');
    } finally {
      setLoading(false);
    }
  };
  
  if (authLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-gray-950 to-indigo-950">
        <div className="flex flex-col items-center">
          <Loader2 className="h-12 w-12 text-indigo-500 animate-spin mb-4" />
          <p className="text-white text-lg">Loading your profile...</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="flex min-h-screen bg-gradient-to-br from-gray-950 to-indigo-950">
      <div className="w-full max-w-md mx-auto p-6 flex flex-col justify-center">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">Complete Your Profile</h1>
          <p className="text-gray-400">
            Please provide some additional information to complete your account setup
          </p>
        </div>
        
        {success ? (
          <div className="bg-green-900/30 border border-green-800 rounded-lg p-6 text-center">
            <CheckCircle2 className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <h2 className="text-xl font-bold text-white mb-2">Profile Updated!</h2>
            <p className="text-gray-300 mb-4">Your profile has been successfully updated.</p>
            <p className="text-gray-400 text-sm">Redirecting to dashboard...</p>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="bg-gray-900/50 border border-gray-800 rounded-lg p-4 mb-6">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-indigo-600/30 rounded-full flex items-center justify-center">
                  <CheckCircle2 className="h-5 w-5 text-indigo-400" />
                </div>
                <div>
                  <p className="text-white font-medium">Wallet Connected</p>
                  <p className="text-gray-400 text-sm">{walletAddress}</p>
                </div>
              </div>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-300 block">Full Name</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <User className="h-5 w-5 text-gray-500" />
                </div>
                <input 
                  type="text" 
                  value={fullName} 
                  onChange={(e) => setFullName(e.target.value)} 
                  className="block w-full pl-10 px-3 py-3 border border-gray-700 rounded-lg bg-gray-900/80 text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all"
                  placeholder="Your full name"
                  required 
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-300 block">Email Address</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Mail className="h-5 w-5 text-gray-500" />
                </div>
                <input 
                  type="email" 
                  value={email} 
                  onChange={(e) => setEmail(e.target.value)} 
                  className="block w-full pl-10 px-3 py-3 border border-gray-700 rounded-lg bg-gray-900/80 text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all"
                  placeholder="<EMAIL>"
                  required 
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-300 block">Phone Number (Optional)</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Phone className="h-5 w-5 text-gray-500" />
                </div>
                <input 
                  type="tel" 
                  value={phone} 
                  onChange={(e) => setPhone(e.target.value)} 
                  className="block w-full pl-10 px-3 py-3 border border-gray-700 rounded-lg bg-gray-900/80 text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all"
                  placeholder="Your phone number"
                />
              </div>
            </div>
            
            {error && (
              <div className="p-3 bg-red-900/40 border border-red-800 rounded-lg">
                <p className="text-red-300 text-sm">{error}</p>
              </div>
            )}
            
            <button 
              type="submit" 
              disabled={loading} 
              className="w-full py-3 px-4 bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 rounded-lg text-white font-medium flex items-center justify-center space-x-2 shadow-lg shadow-purple-900/30 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-900 transition-all"
            >
              {loading ? (
                <>
                  <Loader2 className="h-5 w-5 animate-spin" />
                  <span>Saving...</span>
                </>
              ) : (
                <span>Complete Profile</span>
              )}
            </button>
          </form>
        )}
      </div>
    </div>
  );
}
