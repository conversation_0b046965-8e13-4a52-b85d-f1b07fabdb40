'use client';
import React, { useState, useEffect } from 'react';
import { createClient } from '@supabase/supabase-js';
import { Pie<PERSON>hart, Users, Building, CreditCard, TrendingUp, Activity } from 'lucide-react';
import Link from 'next/link';
import { useUser } from '../../../context/UserContext';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';
const supabase = createClient(supabaseUrl, supabaseAnonKey);

type PlatformStats = {
  totalUsers: number;
  totalProperties: number;
  totalInvestments: number;
  usersByRole: {
    owner: number;
    investor: number;
    admin: number;
  };
  loading: boolean;
};

export default function AdminDashboard() {
  const { user } = useUser();
  const [stats, setStats] = useState<PlatformStats>({
    totalUsers: 0,
    totalProperties: 0,
    totalInvestments: 0,
    usersByRole: {
      owner: 0,
      investor: 0,
      admin: 0
    },
    loading: true
  });

  useEffect(() => {
    const fetchPlatformStats = async () => {
      try {
        // Fetch users count
        const { count: totalUsers, error: usersError } = await supabase
          .from('profiles')
          .select('*', { count: 'exact', head: true });
        
        if (usersError) throw usersError;

        // Fetch users grouped by role
        const { data: userRoles, error: rolesError } = await supabase
          .from('profiles')
          .select('role')
          .not('role', 'is', null);

        if (rolesError) throw rolesError;

        // Count users by role
        const usersByRole = {
          owner: 0,
          investor: 0,
          admin: 0
        };
        
        userRoles.forEach(user => {
          if (user.role in usersByRole) {
            usersByRole[user.role as keyof typeof usersByRole]++;
          }
        });

        // Fetch properties count
        const { count: totalProperties, error: propertiesError } = await supabase
          .from('properties')
          .select('*', { count: 'exact', head: true });
          
        if (propertiesError) throw propertiesError;

        // Fetch investments count
        const { count: totalInvestments, error: investmentsError } = await supabase
          .from('investments')
          .select('*', { count: 'exact', head: true });
          
        if (investmentsError) throw investmentsError;

        setStats({
          totalUsers: totalUsers || 0,
          totalProperties: totalProperties || 0,
          totalInvestments: totalInvestments || 0,
          usersByRole,
          loading: false
        });
      } catch (error) {
        console.error('Error fetching admin stats:', error);
        setStats(prev => ({ ...prev, loading: false }));
      }
    };

    if (user?.role === 'admin') {
      fetchPlatformStats();
    }
  }, [user]);

  if (!user || user.role !== 'admin') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="bg-red-900/40 border border-red-800 rounded-lg p-6 max-w-md">
          <h2 className="text-xl font-bold text-white mb-4">Access Denied</h2>
          <p className="text-red-300">You do not have permission to access the admin dashboard.</p>
        </div>
      </div>
    );
  }

  const StatCard = ({ 
    title, 
    value, 
    icon: Icon, 
    color, 
    href 
  }: { 
    title: string; 
    value: number | string; 
    icon: React.ElementType; 
    color: string; 
    href: string 
  }) => (
    <Link 
      href={href}
      className={`bg-indigo-900/30 rounded-xl p-6 border border-indigo-800 hover:bg-indigo-800/40 transition-colors`}
    >
      <div className="flex items-start justify-between">
        <div>
          <p className="text-gray-400">{title}</p>
          <h3 className="text-2xl font-bold text-white mt-1">
            {stats.loading ? '...' : value}
          </h3>
        </div>
        <div className={`p-3 rounded-lg ${color}`}>
          <Icon className="text-white" size={24} />
        </div>
      </div>
    </Link>
  );

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-white">Admin Dashboard</h1>
        <span className="px-4 py-1.5 bg-indigo-600 text-indigo-100 rounded-full text-sm font-medium">
          Platform Overview
        </span>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard 
          title="Total Users" 
          value={stats.totalUsers} 
          icon={Users} 
          color="bg-blue-600" 
          href="/dashboard/admin/users" 
        />
        <StatCard 
          title="Properties" 
          value={stats.totalProperties} 
          icon={Building} 
          color="bg-purple-600" 
          href="/dashboard/admin/properties" 
        />
        <StatCard 
          title="Investments" 
          value={stats.totalInvestments} 
          icon={CreditCard} 
          color="bg-cyan-600" 
          href="/dashboard/admin/transactions" 
        />
        <StatCard 
          title="Active Users" 
          value="82%" 
          icon={Activity} 
          color="bg-green-600" 
          href="/dashboard/admin/analytics" 
        />
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* User Distribution */}
        <div className="lg:col-span-1 bg-indigo-900/30 rounded-xl p-6 border border-indigo-800">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-white">User Distribution</h3>
            <div className="p-2 rounded-lg bg-indigo-700">
              <PieChart className="text-white" size={20} />
            </div>
          </div>
          
          <div className="space-y-4">
            <div>
              <div className="flex items-center justify-between mb-1">
                <span className="text-gray-300">Owners</span>
                <span className="text-gray-300">{stats.usersByRole.owner}</span>
              </div>
              <div className="h-2 bg-gray-700 rounded-full overflow-hidden">
                <div 
                  className="h-full bg-blue-500" 
                  style={{ 
                    width: `${stats.totalUsers ? (stats.usersByRole.owner / stats.totalUsers * 100) : 0}%` 
                  }}
                ></div>
              </div>
            </div>
            
            <div>
              <div className="flex items-center justify-between mb-1">
                <span className="text-gray-300">Investors</span>
                <span className="text-gray-300">{stats.usersByRole.investor}</span>
              </div>
              <div className="h-2 bg-gray-700 rounded-full overflow-hidden">
                <div 
                  className="h-full bg-purple-500" 
                  style={{ 
                    width: `${stats.totalUsers ? (stats.usersByRole.investor / stats.totalUsers * 100) : 0}%` 
                  }}
                ></div>
              </div>
            </div>
            
            <div>
              <div className="flex items-center justify-between mb-1">
                <span className="text-gray-300">Admins</span>
                <span className="text-gray-300">{stats.usersByRole.admin}</span>
              </div>
              <div className="h-2 bg-gray-700 rounded-full overflow-hidden">
                <div 
                  className="h-full bg-cyan-500" 
                  style={{ 
                    width: `${stats.totalUsers ? (stats.usersByRole.admin / stats.totalUsers * 100) : 0}%` 
                  }}
                ></div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Quick Actions */}
        <div className="lg:col-span-2 bg-indigo-900/30 rounded-xl p-6 border border-indigo-800">
          <h3 className="text-lg font-medium text-white mb-4">Quick Actions</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link
              href="/dashboard/admin/users"
              className="p-4 bg-indigo-800/50 hover:bg-indigo-800/70 rounded-lg flex flex-col items-center justify-center text-center transition-colors"
            >
              <Users size={32} className="text-indigo-300 mb-2" />
              <span className="text-white font-medium">Manage Users</span>
            </Link>
            
            <Link
              href="/dashboard/admin/properties"
              className="p-4 bg-indigo-800/50 hover:bg-indigo-800/70 rounded-lg flex flex-col items-center justify-center text-center transition-colors"
            >
              <Building size={32} className="text-indigo-300 mb-2" />
              <span className="text-white font-medium">Properties</span>
            </Link>
            
            <Link
              href="/dashboard/admin/transactions"
              className="p-4 bg-indigo-800/50 hover:bg-indigo-800/70 rounded-lg flex flex-col items-center justify-center text-center transition-colors"
            >
              <CreditCard size={32} className="text-indigo-300 mb-2" />
              <span className="text-white font-medium">Transactions</span>
            </Link>
          </div>
        </div>
      </div>
      
      {/* Recent Activity */}
      <div className="bg-indigo-900/30 rounded-xl p-6 border border-indigo-800">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-medium text-white">Recent Platform Activity</h3>
          <Link href="/dashboard/admin/activity" className="text-indigo-400 hover:text-indigo-300 text-sm">
            View All
          </Link>
        </div>
        
        <div className="space-y-6">
          {stats.loading ? (
            Array(3).fill(0).map((_, i) => (
              <div key={i} className="animate-pulse flex items-start space-x-4">
                <div className="h-10 w-10 rounded-full bg-indigo-800"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-indigo-800 rounded w-3/4"></div>
                  <div className="h-3 bg-indigo-800/60 rounded w-1/2"></div>
                </div>
              </div>
            ))
          ) : (
            <>
              <div className="flex items-start space-x-4">
                <div className="h-10 w-10 rounded-full bg-green-600/30 border border-green-500 flex items-center justify-center">
                  <Users size={18} className="text-green-300" />
                </div>
                <div>
                  <p className="text-white">New user registered</p>
                  <p className="text-gray-400 text-sm">John Doe joined as an Investor</p>
                  <p className="text-gray-500 text-xs mt-1">10 minutes ago</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-4">
                <div className="h-10 w-10 rounded-full bg-purple-600/30 border border-purple-500 flex items-center justify-center">
                  <Building size={18} className="text-purple-300" />
                </div>
                <div>
                  <p className="text-white">New property listed</p>
                  <p className="text-gray-400 text-sm">Luxury Apartment in Downtown</p>
                  <p className="text-gray-500 text-xs mt-1">2 hours ago</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-4">
                <div className="h-10 w-10 rounded-full bg-blue-600/30 border border-blue-500 flex items-center justify-center">
                  <CreditCard size={18} className="text-blue-300" />
                </div>
                <div>
                  <p className="text-white">New investment</p>
                  <p className="text-gray-400 text-sm">$25,000 invested in Beach House Property</p>
                  <p className="text-gray-500 text-xs mt-1">1 day ago</p>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
} 