'use client';

import { useState } from 'react';
import { useWallet } from '@/context/WalletContext';
import { usePublicClient, useWalletClient } from 'wagmi';
import { investInProperty } from '@/utils/contracts';
import { Wallet, AlertCircle, Check, Loader2 } from 'lucide-react';
import { Property } from './PropertyCard';

interface PropertyInvestmentProps {
  property: Property;
  onClose: () => void;
}

export default function PropertyInvestment({ property, onClose }: PropertyInvestmentProps) {
  const { isConnected } = useWallet();
  const { data: walletClient } = useWalletClient();
  const publicClient = usePublicClient();
  
  const [shares, setShares] = useState<number>(1);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<boolean>(false);
  
  // Calculate investment amount
  const sharePrice = property.price / (property.total_shares || 1000);
  const investmentAmount = sharePrice * shares;
  
  // Calculate ETH equivalent (assuming 1 ETH = $2000 for simplicity)
  const ethPrice = investmentAmount / 2000;
  
  const handleSharesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    if (isNaN(value) || value < 1) {
      setShares(1);
    } else {
      const maxShares = property.available_shares || 1000;
      setShares(Math.min(value, maxShares));
    }
  };
  
  const handleInvest = async () => {
    if (!isConnected || !walletClient) {
      setError('Please connect your wallet first');
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      // Call the contract to invest
      const hash = await investInProperty(
        walletClient,
        property.id,
        shares,
        ethPrice / shares
      );
      
      // Wait for transaction to be mined
      await publicClient.waitForTransactionReceipt({ hash });
      
      setSuccess(true);
      setTimeout(() => {
        onClose();
      }, 3000);
    } catch (err: any) {
      console.error('Investment error:', err);
      setError(err.message || 'Failed to complete investment');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className="bg-gray-900 rounded-xl p-6 max-w-md w-full">
      <h2 className="text-2xl font-bold text-white mb-4">Invest in {property.name}</h2>
      
      {success ? (
        <div className="bg-green-900/30 border border-green-800 rounded-lg p-4 mb-6 flex items-center">
          <Check className="text-green-400 mr-3 flex-shrink-0" />
          <p className="text-green-300">
            Investment successful! You have purchased {shares} shares of {property.name}.
          </p>
        </div>
      ) : error ? (
        <div className="bg-red-900/30 border border-red-800 rounded-lg p-4 mb-6 flex items-center">
          <AlertCircle className="text-red-400 mr-3 flex-shrink-0" />
          <p className="text-red-300">{error}</p>
        </div>
      ) : null}
      
      <div className="mb-6">
        <label className="block text-gray-300 text-sm mb-2">Number of Shares</label>
        <div className="flex items-center">
          <input
            type="number"
            value={shares}
            onChange={handleSharesChange}
            min="1"
            max={property.available_shares || 1000}
            className="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-indigo-500"
            disabled={loading || success}
          />
          <span className="ml-2 text-gray-400">
            / {property.available_shares || 1000}
          </span>
        </div>
      </div>
      
      <div className="bg-gray-800/50 rounded-lg p-4 mb-6">
        <div className="flex justify-between mb-2">
          <span className="text-gray-400">Price per Share:</span>
          <span className="text-white">${sharePrice.toFixed(2)}</span>
        </div>
        <div className="flex justify-between mb-2">
          <span className="text-gray-400">Total Investment:</span>
          <span className="text-white">${investmentAmount.toFixed(2)}</span>
        </div>
        <div className="flex justify-between pt-2 border-t border-gray-700">
          <span className="text-gray-400">ETH Equivalent:</span>
          <span className="text-indigo-400 font-medium">{ethPrice.toFixed(4)} ETH</span>
        </div>
      </div>
      
      <div className="flex gap-4">
        <button
          onClick={onClose}
          className="flex-1 px-4 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded-lg transition-colors"
          disabled={loading}
        >
          Cancel
        </button>
        <button
          onClick={handleInvest}
          className="flex-1 px-4 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white rounded-lg flex items-center justify-center gap-2 transition-colors"
          disabled={loading || success || !isConnected}
        >
          {loading ? (
            <>
              <Loader2 className="animate-spin" size={18} />
              Processing...
            </>
          ) : success ? (
            <>
              <Check size={18} />
              Completed
            </>
          ) : (
            <>
              <Wallet size={18} />
              Invest Now
            </>
          )}
        </button>
      </div>
    </div>
  );
}
