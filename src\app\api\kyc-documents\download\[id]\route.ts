import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { downloadKYCDocument } from '@/utils/fileStorage';

/**
 * GET /api/kyc-documents/download/[id]
 * 
 * Download a KYC document by ID
 * 
 * Path parameters:
 *   id: Document ID
 * 
 * Query parameters:
 *   key: Encryption key (optional, required for encrypted documents)
 */
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    // Verify authentication
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const documentId = params.id;
    if (!documentId) {
      return NextResponse.json({ error: 'Missing document ID' }, { status: 400 });
    }
    
    // Get document metadata to check permissions and determine if encrypted
    const { data: document, error: docError } = await supabase
      .from('kyc_documents')
      .select('*')
      .eq('id', documentId)
      .single();
    
    if (docError) {
      return NextResponse.json({ error: 'Document not found' }, { status: 404 });
    }
    
    // Check authorization (only document owner or admin can download)
    if (document.user_id !== user.id) {
      // Check if user is admin
      const { data: userProfile, error: profileError } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single();
      
      if (profileError || userProfile.role !== 'admin') {
        return NextResponse.json({ error: 'Forbidden: Access denied' }, { status: 403 });
      }
    }
    
    // Get encryption key from query parameters if document is encrypted
    const url = new URL(req.url);
    const encryptionKey = url.searchParams.get('key');
    
    if (document.file_type === 'application/encrypted' && !encryptionKey) {
      return NextResponse.json({ 
        error: 'Encryption key required for encrypted documents',
        encrypted: true
      }, { status: 400 });
    }
    
    // Download and decrypt document if needed
    try {
      // For encrypted documents, use the provided key
      // For non-encrypted documents, the key is ignored
      const fileBlob = await downloadKYCDocument(
        documentId,
        user.id,
        encryptionKey || `${user.id}-default-key`
      );
      
      // Convert Blob to ArrayBuffer for response
      const arrayBuffer = await fileBlob.arrayBuffer();
      
      // Determine content type and filename
      const contentType = document.file_type === 'application/encrypted' 
        ? document.file_name.split('.').pop()?.toLowerCase() === 'pdf'
          ? 'application/pdf'
          : 'application/octet-stream'
        : document.file_type;
      
      const filename = document.file_name.startsWith('encrypted-')
        ? document.file_name.substring(10) // Remove 'encrypted-' prefix
        : document.file_name;
      
      // Create response with appropriate headers
      const response = new NextResponse(arrayBuffer, {
        status: 200,
        headers: {
          'Content-Type': contentType,
          'Content-Disposition': `attachment; filename="${filename}"`,
          'Content-Length': arrayBuffer.byteLength.toString()
        }
      });
      
      return response;
    } catch (downloadError) {
      console.error('Error downloading document:', downloadError);
      return NextResponse.json({ 
        error: 'Failed to download document',
        details: downloadError instanceof Error ? downloadError.message : 'Unknown error'
      }, { status: 500 });
    }
  } catch (error) {
    console.error('Error in GET /api/kyc-documents/download/[id]:', error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Internal server error' 
    }, { status: 500 });
  }
}
