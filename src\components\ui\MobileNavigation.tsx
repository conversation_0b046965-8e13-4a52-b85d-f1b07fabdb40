'use client';

import React, { useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { Menu, X, ChevronDown } from 'lucide-react';
import { cn } from '@/utils/cn';
import OptimizedLink from '@/components/OptimizedLink';

export interface NavigationItem {
  href: string;
  label: string;
  icon?: React.ReactNode;
  badge?: string | number;
  children?: NavigationItem[];
}

export interface MobileNavigationProps {
  items: NavigationItem[];
  logo?: React.ReactNode;
  actions?: React.ReactNode;
  className?: string;
  onItemClick?: (item: NavigationItem) => void;
}

const MobileNavigation: React.FC<MobileNavigationProps> = ({
  items,
  logo,
  actions,
  className,
  onItemClick,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const pathname = usePathname();

  // Close menu when route changes
  useEffect(() => {
    setIsOpen(false);
  }, [pathname]);

  // Prevent body scroll when menu is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  const toggleExpanded = (href: string) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(href)) {
        newSet.delete(href);
      } else {
        newSet.add(href);
      }
      return newSet;
    });
  };

  const handleItemClick = (item: NavigationItem) => {
    onItemClick?.(item);
    if (!item.children) {
      setIsOpen(false);
    }
  };

  const isActive = (href: string) => {
    return pathname === href || pathname.startsWith(`${href}/`);
  };

  const renderNavigationItem = (item: NavigationItem, level = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.has(item.href);
    const active = isActive(item.href);

    return (
      <div key={item.href} className="space-y-1">
        {hasChildren ? (
          <button
            onClick={() => toggleExpanded(item.href)}
            className={cn(
              'w-full flex items-center justify-between px-4 py-3 text-left rounded-lg transition-colors',
              level > 0 && 'ml-4',
              active
                ? 'bg-indigo-700/30 text-cyan-400 font-medium'
                : 'text-gray-300 hover:bg-indigo-800/20 hover:text-white'
            )}
          >
            <div className="flex items-center gap-3">
              {item.icon && (
                <span className={cn('flex-shrink-0', active ? 'text-cyan-400' : 'text-gray-400')}>
                  {item.icon}
                </span>
              )}
              <span>{item.label}</span>
              {item.badge && (
                <span className="ml-auto bg-indigo-600 text-white text-xs px-2 py-1 rounded-full">
                  {item.badge}
                </span>
              )}
            </div>
            <ChevronDown
              className={cn(
                'w-4 h-4 transition-transform',
                isExpanded ? 'rotate-180' : ''
              )}
            />
          </button>
        ) : (
          <OptimizedLink
            href={item.href}
            onClick={() => handleItemClick(item)}
            className={cn(
              'flex items-center gap-3 px-4 py-3 rounded-lg transition-colors',
              level > 0 && 'ml-4',
              active
                ? 'bg-indigo-700/30 text-cyan-400 font-medium'
                : 'text-gray-300 hover:bg-indigo-800/20 hover:text-white'
            )}
          >
            {item.icon && (
              <span className={cn('flex-shrink-0', active ? 'text-cyan-400' : 'text-gray-400')}>
                {item.icon}
              </span>
            )}
            <span className="flex-1">{item.label}</span>
            {item.badge && (
              <span className="bg-indigo-600 text-white text-xs px-2 py-1 rounded-full">
                {item.badge}
              </span>
            )}
          </OptimizedLink>
        )}

        {/* Render children */}
        {hasChildren && isExpanded && (
          <div className="space-y-1 pl-4">
            {item.children!.map(child => renderNavigationItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <>
      {/* Mobile Header */}
      <header className={cn('md:hidden bg-gray-900 border-b border-gray-800', className)}>
        <div className="flex items-center justify-between px-4 py-3">
          {/* Logo */}
          <div className="flex items-center">
            {logo}
          </div>

          {/* Actions */}
          <div className="flex items-center gap-2">
            {actions}
            
            {/* Menu Toggle */}
            <button
              onClick={() => setIsOpen(!isOpen)}
              className={cn(
                'p-2 rounded-lg text-gray-300 hover:text-white hover:bg-gray-800',
                'focus:outline-none focus:ring-2 focus:ring-indigo-500',
                'transition-colors duration-200'
              )}
              aria-label={isOpen ? 'Close menu' : 'Open menu'}
              aria-expanded={isOpen}
            >
              {isOpen ? (
                <X className="w-6 h-6" />
              ) : (
                <Menu className="w-6 h-6" />
              )}
            </button>
          </div>
        </div>
      </header>

      {/* Mobile Menu Overlay */}
      {isOpen && (
        <div className="md:hidden fixed inset-0 z-50 bg-black/80 backdrop-blur-sm">
          <div className="fixed inset-y-0 left-0 w-full max-w-sm bg-gray-900 shadow-xl">
            {/* Menu Header */}
            <div className="flex items-center justify-between px-4 py-3 border-b border-gray-800">
              {logo}
              <button
                onClick={() => setIsOpen(false)}
                className="p-2 rounded-lg text-gray-300 hover:text-white hover:bg-gray-800 transition-colors"
                aria-label="Close menu"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            {/* Menu Content */}
            <nav className="flex-1 overflow-y-auto p-4">
              <div className="space-y-2">
                {items.map(item => renderNavigationItem(item))}
              </div>
            </nav>

            {/* Menu Footer */}
            {actions && (
              <div className="border-t border-gray-800 p-4">
                {actions}
              </div>
            )}
          </div>
        </div>
      )}
    </>
  );
};

export default MobileNavigation;
