import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';
const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testReturnsRelationship() {
  console.log('Starting returns-property relationship test...');
  
  try {
    // 1. Create a test property
    console.log('\n1. Creating test property...');
    const { data: property, error: propertyError } = await supabase
      .from('properties')
      .insert([{
        name: 'Test Property',
        location: 'Test Location',
        price: 100000,
        return_rate: 8,
        status: 'available',
        owner_id: 'test-owner-id' // Replace with a real owner ID if needed
      }])
      .select()
      .single();

    if (propertyError) {
      throw new Error(`Failed to create test property: ${propertyError.message}`);
    }
    console.log('✅ Test property created:', property);

    // 2. Create a test return
    console.log('\n2. Creating test return...');
    const { data: returnData, error: returnError } = await supabase
      .from('returns')
      .insert([{
        property_id: property.id,
        amount: 1000,
        date: new Date().toISOString(),
        status: 'pending',
        investor_id: 'test-investor-id' // Replace with a real investor ID if needed
      }])
      .select()
      .single();

    if (returnError) {
      throw new Error(`Failed to create test return: ${returnError.message}`);
    }
    console.log('✅ Test return created:', returnData);

    // 3. Verify the relationship
    console.log('\n3. Verifying relationship...');
    const { data: verifiedReturn, error: verifyError } = await supabase
      .from('returns')
      .select(`
        *,
        properties(*)
      `)
      .eq('id', returnData.id)
      .single();

    if (verifyError) {
      throw new Error(`Failed to verify relationship: ${verifyError.message}`);
    }
    console.log('✅ Relationship verified:', verifiedReturn);

    // 4. Test error case - invalid property_id
    console.log('\n4. Testing error case - invalid property_id...');
    const { error: invalidPropertyError } = await supabase
      .from('returns')
      .insert([{
        property_id: 'invalid-uuid',
        amount: 1000,
        date: new Date().toISOString(),
        status: 'pending',
        investor_id: 'test-investor-id'
      }]);

    if (invalidPropertyError) {
      console.log('✅ Error case verified - invalid property_id rejected:', invalidPropertyError.message);
    } else {
      throw new Error('Expected error for invalid property_id but got none');
    }

    // 5. Cleanup
    console.log('\n5. Cleaning up test data...');
    const { error: deleteReturnError } = await supabase
      .from('returns')
      .delete()
      .eq('id', returnData.id);

    if (deleteReturnError) {
      console.warn('Warning: Failed to delete test return:', deleteReturnError.message);
    }

    const { error: deletePropertyError } = await supabase
      .from('properties')
      .delete()
      .eq('id', property.id);

    if (deletePropertyError) {
      console.warn('Warning: Failed to delete test property:', deletePropertyError.message);
    }

    console.log('✅ Test data cleaned up');

    console.log('\n🎉 All tests completed successfully!');
  } catch (error) {
    console.error('\n❌ Test failed:', error);
  }
}

// Run the test
testReturnsRelationship(); 