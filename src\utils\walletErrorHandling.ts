/**
 * Wallet Error Handling Utilities
 * 
 * This file contains utilities for handling wallet connection errors
 * and providing clear error messages to users.
 */

// Define error types
export type WalletErrorType = 
  | 'connection_rejected'
  | 'already_processing'
  | 'unsupported_chain'
  | 'user_rejected'
  | 'unauthorized'
  | 'disconnected'
  | 'chain_disconnected'
  | 'signature_rejected'
  | 'wallet_not_found'
  | 'provider_error'
  | 'rpc_error'
  | 'timeout'
  | 'unknown';

// Error interface
export interface WalletError {
  type: WalletErrorType;
  message: string;
  details?: string;
  code?: number;
  retry?: boolean;
  solution?: string;
}

/**
 * Parse wallet connection errors and return a user-friendly error object
 * 
 * @param error The error object from the wallet connection attempt
 * @returns A structured wallet error object
 */
export function parseWalletError(error: any): WalletError {
  // Default error
  const defaultError: WalletError = {
    type: 'unknown',
    message: 'An unknown error occurred while connecting to your wallet',
    retry: true
  };
  
  // If no error, return default
  if (!error) return defaultError;
  
  // Extract error message
  const errorMessage = error.message || error.toString();
  const errorCode = error.code;
  
  // Check for common error patterns
  if (errorMessage.includes('User rejected') || errorMessage.includes('User denied')) {
    return {
      type: 'user_rejected',
      message: 'You rejected the connection request',
      details: errorMessage,
      code: errorCode,
      retry: true,
      solution: 'Please approve the connection request in your wallet'
    };
  }
  
  if (errorMessage.includes('Already processing')) {
    return {
      type: 'already_processing',
      message: 'A wallet connection is already in progress',
      details: errorMessage,
      code: errorCode,
      retry: true,
      solution: 'Please check your wallet for pending requests'
    };
  }
  
  if (errorMessage.includes('Unsupported chain')) {
    return {
      type: 'unsupported_chain',
      message: 'Your wallet is connected to an unsupported network',
      details: errorMessage,
      code: errorCode,
      retry: true,
      solution: 'Please switch to a supported network in your wallet'
    };
  }
  
  if (errorMessage.includes('disconnected') || errorMessage.includes('Disconnected')) {
    return {
      type: 'disconnected',
      message: 'Your wallet was disconnected',
      details: errorMessage,
      code: errorCode,
      retry: true,
      solution: 'Please try connecting again'
    };
  }
  
  if (errorMessage.includes('chain disconnected')) {
    return {
      type: 'chain_disconnected',
      message: 'Your wallet disconnected from the current network',
      details: errorMessage,
      code: errorCode,
      retry: true,
      solution: 'Please check your network connection and try again'
    };
  }
  
  if (errorMessage.includes('User denied message signature')) {
    return {
      type: 'signature_rejected',
      message: 'You rejected the signature request',
      details: errorMessage,
      code: errorCode,
      retry: true,
      solution: 'Please approve the signature request in your wallet'
    };
  }
  
  if (errorMessage.includes('wallet_not_found') || errorMessage.includes('No provider found')) {
    return {
      type: 'wallet_not_found',
      message: 'No compatible wallet was found',
      details: errorMessage,
      code: errorCode,
      retry: false,
      solution: 'Please install a compatible wallet extension like MetaMask'
    };
  }
  
  if (errorMessage.includes('Unauthorized') || errorCode === 4100) {
    return {
      type: 'unauthorized',
      message: 'Wallet connection not authorized',
      details: errorMessage,
      code: errorCode,
      retry: true,
      solution: 'Please authorize the connection in your wallet settings'
    };
  }
  
  if (errorMessage.includes('Provider error') || errorCode === -32603) {
    return {
      type: 'provider_error',
      message: 'Your wallet provider encountered an error',
      details: errorMessage,
      code: errorCode,
      retry: true,
      solution: 'Please try refreshing the page or restart your wallet'
    };
  }
  
  if (errorMessage.includes('timeout') || errorMessage.includes('Timed out')) {
    return {
      type: 'timeout',
      message: 'Connection request timed out',
      details: errorMessage,
      code: errorCode,
      retry: true,
      solution: 'Please check your internet connection and try again'
    };
  }
  
  // If no specific error pattern matched, return the original error with default type
  return {
    type: 'unknown',
    message: 'An error occurred while connecting to your wallet',
    details: errorMessage,
    code: errorCode,
    retry: true,
    solution: 'Please try again or use a different wallet'
  };
}

/**
 * Get a user-friendly error message for a wallet error
 * 
 * @param error The wallet error object
 * @returns A user-friendly error message
 */
export function getWalletErrorMessage(error: WalletError): string {
  return error.solution 
    ? `${error.message}. ${error.solution}`
    : error.message;
}

/**
 * Retry a wallet connection with exponential backoff
 * 
 * @param connectFn The function to retry
 * @param maxAttempts Maximum number of retry attempts
 * @param baseDelay Base delay in milliseconds
 * @returns Promise that resolves when connection succeeds or rejects after max attempts
 */
export async function retryWalletConnection<T>(
  connectFn: () => Promise<T>,
  maxAttempts: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let attempts = 0;
  let lastError: any;
  
  while (attempts < maxAttempts) {
    try {
      return await connectFn();
    } catch (error) {
      lastError = error;
      attempts++;
      
      // Parse the error to check if it's retryable
      const parsedError = parseWalletError(error);
      
      // If the error is not retryable, throw immediately
      if (!parsedError.retry) {
        throw parsedError;
      }
      
      // If we've reached max attempts, throw the error
      if (attempts >= maxAttempts) {
        throw parsedError;
      }
      
      // Wait with exponential backoff before retrying
      const delay = baseDelay * Math.pow(2, attempts - 1);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  // This should never be reached due to the throw in the loop,
  // but TypeScript requires a return statement
  throw parseWalletError(lastError);
}
