'use client';

import { useState, useEffect } from 'react';
import { useSupabaseClient } from '@supabase/auth-helpers-react';
import type { SupabaseClient } from '@supabase/supabase-js';
import { Plus, Search, Filter, Database, AlertCircle, CheckCircle, Wallet } from 'lucide-react';
import LoadingSpinner from '@/components/LoadingSpinner';
import PropertyForm, { Property } from '@/components/PropertyForm';
import PropertyCard from '@/components/PropertyCard';
import Notification from '@/components/Notification';
import { fixInvestmentsTable } from '@/utils/databaseFix';
import { useWallet } from '@/context/WalletContext';
import ConnectWalletButton from '@/components/ConnectWalletButton';

export default function OwnerPropertiesPage() {
  const supabase = useSupabaseClient<SupabaseClient>();
  const { isConnected, openWalletModal } = useWallet();
  const [properties, setProperties] = useState<Property[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedProperty, setSelectedProperty] = useState<Property | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showWalletPrompt, setShowWalletPrompt] = useState(false);

  // Notification state
  const [notification, setNotification] = useState<{
    type: 'success' | 'error';
    message: string;
    isVisible: boolean;
  }>({
    type: 'success',
    message: '',
    isVisible: false
  });

  useEffect(() => {
    if (supabase) {
      fetchProperties();
    }
  }, [supabase]);

  const fetchProperties = async () => {
    if (!supabase) return;
    try {
      setLoading(true);
      setError(null);
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      // Get all properties for the current user
      const { data, error: queryError } = await supabase
        .from('properties')
        .select('*')
        .eq('owner_id', user.id)
        .order('created_at', { ascending: false });

      if (queryError) throw queryError;

      setProperties(data || []);
    } catch (err: any) {
      const errorMsg = err?.message || JSON.stringify(err) || 'Failed to fetch properties.';
      console.error('Error fetching properties:', errorMsg);
      setError('Failed to load properties. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const filteredProperties = properties.filter(property =>
    property.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    property.location.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Show notification
  const showNotification = (type: 'success' | 'error', message: string) => {
    setNotification({
      type,
      message,
      isVisible: true
    });
  };

  // Handle property save (create or update)
  const handleSaveProperty = async (propertyData: Property) => {
    setIsSubmitting(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      // Add owner_id to property data
      const data = {
        ...propertyData,
        owner_id: user.id
      };

      // Explicitly type the result
      let result: Property;

      if (propertyData.id) {
        // Update existing property
        const { data: updatedData, error } = await supabase
          .from('properties')
          .update(data)
          .eq('id', propertyData.id)
          .select();

        if (error) throw error;
        if (!updatedData || updatedData.length === 0) {
          throw new Error('Failed to update property');
        }

        result = updatedData[0] as Property;

        // Update local state
        setProperties(prev =>
          prev.map(p => p.id === propertyData.id ? result : p)
        );

        showNotification('success', 'Property updated successfully!');
      } else {
        // Create new property
        const { data: newData, error } = await supabase
          .from('properties')
          .insert(data)
          .select();

        if (error) throw error;
        if (!newData || newData.length === 0) {
          throw new Error('Failed to create property');
        }

        result = newData[0] as Property;

        // Update local state
        setProperties(prev => [result, ...prev]);

        showNotification('success', 'Property created successfully!');
      }

      // Close the modal
      setIsModalOpen(false);
      setSelectedProperty(null);

    } catch (err: any) {
      console.error('Error saving property:', err);
      showNotification('error', `Failed to save property: ${err.message || 'Unknown error'}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle property delete
  const handleDeleteProperty = async (id: string) => {
    if (!confirm('Are you sure you want to delete this property? This action cannot be undone.')) return;

    try {
      const { error } = await supabase
        .from('properties')
        .delete()
        .eq('id', id);

      if (error) throw error;

      // Update local state
      setProperties(properties.filter(p => p.id !== id));
      showNotification('success', 'Property deleted successfully');
    } catch (error: any) {
      console.error('Error deleting property:', error);
      showNotification('error', `Failed to delete property: ${error.message || 'Unknown error'}`);
    }
  };

  // Handle property publish/unpublish
  const handlePublishProperty = async (id: string, publish: boolean) => {
    try {
      const { error } = await supabase
        .from('properties')
        .update({ published: publish })
        .eq('id', id);

      if (error) throw error;

      // Update local state
      setProperties(prev =>
        prev.map(p => p.id === id ? { ...p, published: publish } : p)
      );

      showNotification('success', `Property ${publish ? 'published' : 'unpublished'} successfully`);
    } catch (error: any) {
      console.error('Error updating property:', error);
      showNotification('error', `Failed to ${publish ? 'publish' : 'unpublish'} property: ${error.message || 'Unknown error'}`);
    }
  };

  // Handle property archive/unarchive
  const handleArchiveProperty = async (id: string, archive: boolean) => {
    try {
      const { error } = await supabase
        .from('properties')
        .update({ status: archive ? 'archived' : 'draft' })
        .eq('id', id);

      if (error) throw error;

      // Update local state
      setProperties(prev =>
        prev.map(p => p.id === id ? { ...p, status: archive ? 'archived' : 'draft' } : p)
      );

      showNotification('success', `Property ${archive ? 'archived' : 'restored'} successfully`);
    } catch (error: any) {
      console.error('Error updating property:', error);
      showNotification('error', `Failed to ${archive ? 'archive' : 'restore'} property: ${error.message || 'Unknown error'}`);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Notification */}
      <Notification
        type={notification.type}
        message={notification.message}
        isVisible={notification.isVisible}
        onClose={() => setNotification(prev => ({ ...prev, isVisible: false }))}
      />

      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <h1 className="text-2xl font-bold">My Properties</h1>
        <button
          onClick={() => {
            if (!isConnected) {
              setShowWalletPrompt(true);
            } else {
              setSelectedProperty(null);
              setIsModalOpen(true);
            }
          }}
          className="flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus size={20} />
          Add Property
        </button>
      </div>

      {/* Search */}
      <div className="mb-6">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <input
            type="text"
            placeholder="Search properties..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      {/* Error */}
      {error && (
        <div className="bg-red-900/30 border border-red-800 rounded-xl p-4 mb-6">
          <p className="text-red-300 whitespace-pre-line">{error}</p>
        </div>
      )}

      {/* Property Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredProperties.map((property) => (
          <PropertyCard
            key={property.id}
            property={property}
            onEdit={(property) => {
              setSelectedProperty(property);
              setIsModalOpen(true);
            }}
            onDelete={handleDeleteProperty}
            onPublish={handlePublishProperty}
            onArchive={handleArchiveProperty}
          />
        ))}
      </div>

      {/* Empty State */}
      {filteredProperties.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500">No properties found</p>
        </div>
      )}

      {/* Property Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-5xl max-h-[90vh] overflow-y-auto">
            <h2 className="text-xl font-bold mb-4">
              {selectedProperty ? 'Edit Property' : 'Add Property'}
            </h2>

            <PropertyForm
              property={selectedProperty || undefined}
              onSave={handleSaveProperty}
              onCancel={() => {
                setIsModalOpen(false);
                setSelectedProperty(null);
              }}
              isSubmitting={isSubmitting}
            />
          </div>
        </div>
      )}

      {/* Wallet Connection Prompt Modal */}
      {showWalletPrompt && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex justify-between items-start mb-4">
              <h3 className="text-xl font-bold">Connect Your Wallet</h3>
              <button
                onClick={() => setShowWalletPrompt(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                ✕
              </button>
            </div>

            <div className="space-y-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-center space-x-3 mb-2">
                  <div className="p-2 bg-blue-100 rounded-full">
                    <Wallet size={24} className="text-blue-600" />
                  </div>
                  <p className="font-medium">Wallet Required</p>
                </div>
                <p className="text-gray-600 mb-2">
                  You need to connect your wallet before you can list a property.
                </p>
                <p className="text-gray-600">
                  Connecting your wallet allows you to tokenize your property and manage ownership on the blockchain.
                </p>
              </div>

              <div className="flex justify-end space-x-4">
                <button
                  onClick={() => setShowWalletPrompt(false)}
                  className="px-4 py-2 bg-gray-200 hover:bg-gray-300 rounded-lg text-gray-800"
                >
                  Cancel
                </button>
                <button
                  onClick={() => {
                    openWalletModal();
                    setShowWalletPrompt(false);
                  }}
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg text-white flex items-center"
                >
                  <Wallet size={16} className="mr-2" />
                  Connect Wallet
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}