// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC721/extensions/ERC721URIStorage.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/Counters.sol";

/**
 * @title PropertyRegistry
 * @dev ERC721 token for representing full property ownership
 * Each token represents a unique property
 */
contract PropertyRegistry is ERC721URIStorage, Ownable {
    using Counters for Counters.Counter;

    // Token ID counter
    Counters.Counter private _tokenIdCounter;

    // Investment Manager contract address
    address private _investmentManager;

    // Property struct
    struct Property {
        uint256 id;
        string name;
        string location;
        uint256 valuation;
        bool fractionalized;
        address owner;
    }

    // Mapping from token ID to Property
    mapping(uint256 => Property) private _properties;

    // Mapping from token ID to locked status
    mapping(uint256 => bool) private _locked;

    // Array of property IDs
    uint256[] private _propertyIds;

    // Events
    event PropertyMinted(uint256 indexed tokenId, address indexed owner, string metadataURI);
    event PropertyLocked(uint256 indexed tokenId);
    event PropertyUnlocked(uint256 indexed tokenId);

    constructor() ERC721("BrickChain Property", "BCP") Ownable(msg.sender) {}

    /**
     * @dev Sets the investment manager contract address
     * @param investmentManager Address of the InvestmentManager contract
     */
    function setInvestmentManager(address investmentManager) external onlyOwner {
        _investmentManager = investmentManager;
    }

    /**
     * @dev Mints a new property token
     * @param to Address to mint the token to
     * @param name Name of the property
     * @param location Location of the property
     * @param valuation Valuation of the property in ETH (scaled by 10^18)
     * @param metadataURI URI for the property metadata
     * @return tokenId ID of the minted token
     */
    function mintProperty(
        address to,
        string memory name,
        string memory location,
        uint256 valuation,
        string memory metadataURI
    ) external returns (uint256) {
        uint256 tokenId = _tokenIdCounter.current();
        _tokenIdCounter.increment();

        _safeMint(to, tokenId);
        _setTokenURI(tokenId, metadataURI);

        _properties[tokenId] = Property({
            id: tokenId,
            name: name,
            location: location,
            valuation: valuation,
            fractionalized: false,
            owner: to
        });

        _propertyIds.push(tokenId);

        emit PropertyMinted(tokenId, to, metadataURI);

        return tokenId;
    }

    /**
     * @dev Locks a property token when it's fractionalized
     * @param tokenId ID of the token to lock
     */
    function lockProperty(uint256 tokenId) external {
        require(msg.sender == _investmentManager, "Only investment manager can lock");
        require(ownerOf(tokenId) == _investmentManager, "Token must be owned by investment manager");
        require(!_locked[tokenId], "Token already locked");

        _locked[tokenId] = true;
        _properties[tokenId].fractionalized = true;

        emit PropertyLocked(tokenId);
    }

    /**
     * @dev Unlocks a property token when all fractions are recombined
     * @param tokenId ID of the token to unlock
     * @param to Address to transfer the unlocked token to
     */
    function unlockProperty(uint256 tokenId, address to) external {
        require(msg.sender == _investmentManager, "Only investment manager can unlock");
        require(_locked[tokenId], "Token not locked");

        _locked[tokenId] = false;
        _properties[tokenId].fractionalized = false;
        _properties[tokenId].owner = to;

        emit PropertyUnlocked(tokenId);
    }

    /**
     * @dev Returns a property by ID
     * @param tokenId ID of the property
     */
    function getProperty(uint256 tokenId) external view returns (Property memory) {
        require(_exists(tokenId), "Property does not exist");
        return _properties[tokenId];
    }

    /**
     * @dev Returns all property IDs
     */
    function getAllPropertyIds() external view returns (uint256[] memory) {
        return _propertyIds;
    }

    /**
     * @dev Checks if a token is locked
     * @param tokenId ID of the token
     */
    function isLocked(uint256 tokenId) external view returns (bool) {
        require(_exists(tokenId), "Property does not exist");
        return _locked[tokenId];
    }

    /**
     * @dev Override transfer function to prevent transfer of locked tokens
     */
    function _update(address to, uint256 tokenId, address auth)
        internal
        override
        returns (address)
    {
        require(!_locked[tokenId] || msg.sender == _investmentManager, "Token is locked");
        return super._update(to, tokenId, auth);
    }

    /**
     * @dev Checks if a token exists
     * @param tokenId ID of the token
     */
    function _exists(uint256 tokenId) internal view returns (bool) {
        return _ownerOf(tokenId) != address(0);
    }
}
