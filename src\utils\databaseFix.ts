import { SupabaseClient } from '@supabase/supabase-js';

/**
 * Utility function to fix the investments table by adding missing columns
 */
export async function fixInvestmentsTable(supabase: SupabaseClient): Promise<{ success: boolean; message: string }> {
  try {
    // Check if the amount column exists
    const { data: columnCheck, error: checkError } = await supabase
      .from('information_schema.columns')
      .select('column_name')
      .eq('table_name', 'investments')
      .eq('column_name', 'amount');
      
    if (checkError) {
      console.error('Error checking column existence:', checkError);
      return { success: false, message: `Error checking column: ${checkError.message}` };
    }
    
    // If amount column doesn't exist, add it
    if (!columnCheck || columnCheck.length === 0) {
      // We need to use raw SQL to add the column
      // This requires the execute_sql RPC function to be available
      // or direct SQL execution privileges
      
      try {
        // Try using the execute-sql API endpoint
        const response = await fetch('/api/execute-sql', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            sql: `
              ALTER TABLE investments 
              ADD COLUMN IF NOT EXISTS amount NUMERIC DEFAULT 0;
              
              ALTER TABLE investments 
              ADD COLUMN IF NOT EXISTS date_invested TIMESTAMP WITH TIME ZONE DEFAULT NOW();
              
              ALTER TABLE investments 
              ADD COLUMN IF NOT EXISTS created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
              
              ALTER TABLE investments 
              ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
            `
          }),
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to execute SQL');
        }
        
        return { 
          success: true, 
          message: 'Successfully added missing columns to investments table' 
        };
      } catch (apiError: any) {
        console.error('Error using API to fix database:', apiError);
        
        // Fallback to direct RPC call if available
        try {
          const { error: rpcError } = await supabase.rpc('execute_sql', {
            sql: `
              ALTER TABLE investments 
              ADD COLUMN IF NOT EXISTS amount NUMERIC DEFAULT 0;
              
              ALTER TABLE investments 
              ADD COLUMN IF NOT EXISTS date_invested TIMESTAMP WITH TIME ZONE DEFAULT NOW();
              
              ALTER TABLE investments 
              ADD COLUMN IF NOT EXISTS created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
              
              ALTER TABLE investments 
              ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
            `
          });
          
          if (rpcError) {
            throw rpcError;
          }
          
          return { 
            success: true, 
            message: 'Successfully added missing columns to investments table' 
          };
        } catch (rpcError: any) {
          console.error('Error using RPC to fix database:', rpcError);
          return { 
            success: false, 
            message: `Could not fix database: ${rpcError.message || apiError.message}` 
          };
        }
      }
    }
    
    // Column already exists
    return { 
      success: true, 
      message: 'The investments table already has the required columns' 
    };
  } catch (error: any) {
    console.error('Error in fixInvestmentsTable:', error);
    return { 
      success: false, 
      message: `Database fix failed: ${error.message}` 
    };
  }
}
