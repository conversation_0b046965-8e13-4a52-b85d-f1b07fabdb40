'use client';

import { useState, useEffect } from 'react';
import { useWalletAuth } from '@/context/WalletAuthContext';
import { useConnectModal } from '@rainbow-me/rainbowkit';
import { Wallet, ArrowRight, CheckCircle, Shield } from 'lucide-react';
import Button from '@/components/ui/Button';
import { useToast } from '@/context/ToastContext';

/**
 * Enhanced onboarding component with improved accessibility, animations, and user feedback.
 * Guides users through wallet connection, authentication, and profile completion.
 */
export default function WalletOnboarding() {
  const { openConnectModal } = useConnectModal();
  const {
    isWalletConnected,
    isAuthenticated,
    signInWithWallet,
    signingIn,
    walletError
  } = useWalletAuth();

  const { success, error: showError } = useToast();

  // Enhanced state management
  const [currentStep, setCurrentStep] = useState(1);

  // Determine current step based on auth state
  const step = !isWalletConnected ? 1 : !isAuthenticated ? 2 : 3;

  // Handle step transitions with toast notifications
  useEffect(() => {
    if (step !== currentStep) {
      setCurrentStep(step);

      // Show success messages for completed steps
      if (step === 2 && currentStep === 1) {
        success('Wallet Connected', 'Your wallet has been successfully connected.');
      } else if (step === 3 && currentStep === 2) {
        success('Authentication Complete', 'You are now authenticated and ready to use BrickChain.');
      }
    }
  }, [step, currentStep, success]);

  // Handle wallet errors
  useEffect(() => {
    if (walletError) {
      showError('Wallet Error', walletError.message);
    }
  }, [walletError, showError]);

  // Step configuration for better maintainability
  const steps = [
    { id: 1, label: 'Connect', description: 'Connect your wallet', icon: Wallet },
    { id: 2, label: 'Authenticate', description: 'Sign message to verify', icon: Shield },
    { id: 3, label: 'Complete', description: 'Ready to explore', icon: CheckCircle }
  ];

  return (
    <div
      className="max-w-md mx-auto bg-indigo-900/30 backdrop-blur-sm border border-indigo-800/50 rounded-xl p-6 transition-all duration-300"
      role="main"
      aria-labelledby="onboarding-title"
    >
      <h2
        id="onboarding-title"
        className="text-2xl font-bold text-white mb-6 text-center"
      >
        Get Started with BrickChain
      </h2>

      {/* Enhanced Progress indicator with accessibility */}
      <div
        className="flex items-center justify-between mb-8 px-2"
        role="progressbar"
        aria-valuenow={step}
        aria-valuemin={1}
        aria-valuemax={3}
        aria-label={`Step ${step} of 3: ${steps[step - 1]?.description}`}
      >
        {steps.map((stepConfig, index) => {
          const stepNumber = stepConfig.id;
          const isCompleted = step > stepNumber;
          const isCurrent = step === stepNumber;
          const StepIcon = stepConfig.icon;

          return (
            <div key={stepNumber} className="flex flex-col items-center relative">
              {/* Connection line */}
              {index < steps.length - 1 && (
                <div
                  className={`absolute top-5 left-12 w-16 h-0.5 transition-colors duration-300 ${
                    isCompleted ? 'bg-green-500' : 'bg-indigo-800'
                  }`}
                  aria-hidden="true"
                />
              )}

              <div
                className={`w-10 h-10 rounded-full flex items-center justify-center mb-2 transition-all duration-300 transform ${
                  isCompleted
                    ? 'bg-green-500 text-white scale-110'
                    : isCurrent
                      ? 'bg-indigo-600 text-white scale-105 ring-2 ring-indigo-400 ring-opacity-50'
                      : 'bg-indigo-900/50 text-indigo-300'
                } ${isCurrent ? 'animate-pulse' : ''}`}
                aria-label={`${stepConfig.description} - ${isCompleted ? 'completed' : isCurrent ? 'current' : 'pending'}`}
              >
                {isCompleted ? (
                  <CheckCircle size={20} aria-hidden="true" />
                ) : isCurrent ? (
                  <StepIcon size={20} aria-hidden="true" />
                ) : (
                  <span aria-hidden="true">{stepNumber}</span>
                )}
              </div>
              <span className={`text-xs transition-colors duration-300 ${
                isCurrent ? 'text-white font-medium' : 'text-indigo-300'
              }`}>
                {stepConfig.label}
              </span>
            </div>
          );
        })}
      </div>

      {/* Enhanced Step content with better accessibility and animations */}
      <div className="mb-6" role="region" aria-live="polite" aria-label="Current step content">
        {step === 1 && (
          <div className="text-center animate-fade-in">
            <div className="relative">
              <Wallet className="h-12 w-12 text-indigo-400 mx-auto mb-4 animate-bounce" />
              <div className="absolute inset-0 h-12 w-12 mx-auto bg-indigo-400/20 rounded-full animate-ping"></div>
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">Connect Your Wallet</h3>
            <p className="text-indigo-200 mb-6 leading-relaxed">
              Connect your crypto wallet to access the BrickChain platform and start investing in tokenized real estate.
            </p>
            <Button
              onClick={openConnectModal}
              variant="primary"
              size="lg"
              fullWidth
              icon={<Wallet size={18} />}
              iconPosition="left"
              aria-describedby="connect-wallet-description"
            >
              Connect Wallet
            </Button>
            <p id="connect-wallet-description" className="sr-only">
              This will open a modal to connect your cryptocurrency wallet
            </p>
          </div>
        )}

        {step === 2 && (
          <div className="text-center animate-fade-in">
            <div className="relative mb-4">
              <Shield className="h-12 w-12 text-indigo-400 mx-auto" />
              {signingIn && (
                <div className="absolute inset-0 h-12 w-12 mx-auto bg-indigo-400/20 rounded-full animate-pulse"></div>
              )}
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">Sign Message to Authenticate</h3>
            <p className="text-indigo-200 mb-6 leading-relaxed">
              Sign a secure message with your wallet to verify your identity and complete authentication.
            </p>
            <Button
              onClick={signInWithWallet}
              variant="primary"
              size="lg"
              fullWidth
              loading={signingIn}
              loadingText="Signing In..."
              icon={!signingIn ? <ArrowRight size={18} /> : undefined}
              iconPosition="left"
              aria-describedby="sign-message-description"
              aria-label={signingIn ? 'Signing in progress' : 'Sign message to authenticate'}
            >
              Sign Message
            </Button>
            <p id="sign-message-description" className="sr-only">
              This will prompt your wallet to sign a secure message for authentication
            </p>
            {walletError && (
              <div
                className="mt-4 p-3 bg-red-900/40 border border-red-800 rounded-lg"
                role="alert"
                aria-live="assertive"
              >
                <p className="text-red-400 text-sm">{walletError.message}</p>
              </div>
            )}
          </div>
        )}

        {step === 3 && (
          <div className="text-center animate-fade-in">
            <div className="relative">
              <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4 animate-bounce" />
              <div className="absolute inset-0 h-12 w-12 mx-auto bg-green-500/20 rounded-full animate-ping"></div>
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">Successfully Authenticated</h3>
            <p className="text-indigo-200 mb-6 leading-relaxed">
              You&apos;re now ready to explore the BrickChain platform and start your real estate investment journey.
            </p>
            <Button
              onClick={() => window.location.href = '/dashboard'}
              variant="primary"
              size="lg"
              fullWidth
              icon={<ArrowRight size={18} />}
              iconPosition="left"
              aria-describedby="dashboard-link-description"
            >
              Go to Dashboard
            </Button>
            <p id="dashboard-link-description" className="sr-only">
              Navigate to your personal dashboard to manage investments and properties
            </p>
          </div>
        )}
      </div>
    </div>
  );
}