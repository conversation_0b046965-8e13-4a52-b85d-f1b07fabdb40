// Setup Storage Buckets for BrickChain
// Run this script in the browser console when logged in as an admin user

async function setupStorageBuckets() {
  try {
    console.log('Starting storage bucket setup...');
    
    // Get environment variables
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';
    
    if (!supabaseUrl || !supabaseAnonKey) {
      throw new Error('Supabase credentials not configured');
    }
    
    // Create Supabase client
    const { createClient } = window.supabase;
    const supabase = createClient(supabaseUrl, supabaseAnonKey);
    
    // Check if user is authenticated
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      throw new Error('You must be logged in to set up storage buckets');
    }
    
    // Check if user is admin
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();
    
    if (profileError || profile.role !== 'admin') {
      throw new Error('You must be an admin to set up storage buckets');
    }
    
    console.log('Authentication verified. Setting up storage buckets...');
    
    // Get existing buckets
    const { data: existingBuckets, error: bucketsError } = await supabase.storage.listBuckets();
    
    if (bucketsError) {
      throw bucketsError;
    }
    
    const existingBucketNames = existingBuckets.map(bucket => bucket.name);
    console.log('Existing buckets:', existingBucketNames);
    
    // Define buckets to create
    const bucketsToCreate = [
      {
        name: 'properties',
        public: true,
        fileSizeLimit: 10485760, // 10MB
        allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'application/pdf']
      },
      {
        name: 'kyc_documents',
        public: false,
        fileSizeLimit: 10485760, // 10MB
        allowedMimeTypes: [
          'image/jpeg', 
          'image/png', 
          'application/pdf', 
          'application/msword', 
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'application/encrypted'
        ]
      }
    ];
    
    // Create buckets if they don't exist
    for (const bucket of bucketsToCreate) {
      if (!existingBucketNames.includes(bucket.name)) {
        console.log(`Creating bucket: ${bucket.name}`);
        
        const { error: createError } = await supabase.storage.createBucket(bucket.name, {
          public: bucket.public,
          fileSizeLimit: bucket.fileSizeLimit,
          allowedMimeTypes: bucket.allowedMimeTypes
        });
        
        if (createError) {
          console.error(`Error creating bucket ${bucket.name}:`, createError);
          continue;
        }
        
        console.log(`Successfully created bucket: ${bucket.name}`);
      } else {
        console.log(`Bucket already exists: ${bucket.name}`);
        
        // Update bucket configuration
        const { error: updateError } = await supabase.storage.updateBucket(bucket.name, {
          public: bucket.public,
          fileSizeLimit: bucket.fileSizeLimit,
          allowedMimeTypes: bucket.allowedMimeTypes
        });
        
        if (updateError) {
          console.error(`Error updating bucket ${bucket.name}:`, updateError);
          continue;
        }
        
        console.log(`Successfully updated bucket: ${bucket.name}`);
      }
    }
    
    // Set up RLS policies for kyc_documents bucket
    if (existingBucketNames.includes('kyc_documents') || bucketsToCreate.some(b => b.name === 'kyc_documents')) {
      console.log('Setting up RLS policies for kyc_documents bucket...');
      
      // Create SQL to set up RLS policies
      const rls_sql = `
        -- Enable RLS on storage.objects
        ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;
        
        -- Create policy for kyc_documents bucket
        -- Users can only select their own documents
        DROP POLICY IF EXISTS "Users can select their own KYC documents" ON storage.objects;
        CREATE POLICY "Users can select their own KYC documents" ON storage.objects
          FOR SELECT
          USING (
            bucket_id = 'kyc_documents' AND
            (auth.uid() = SPLIT_PART(name, '/', 2)::uuid OR
             EXISTS (
               SELECT 1 FROM profiles
               WHERE id = auth.uid() AND role = 'admin'
             ))
          );
        
        -- Users can only insert their own documents
        DROP POLICY IF EXISTS "Users can insert their own KYC documents" ON storage.objects;
        CREATE POLICY "Users can insert their own KYC documents" ON storage.objects
          FOR INSERT
          WITH CHECK (
            bucket_id = 'kyc_documents' AND
            auth.uid() = SPLIT_PART(name, '/', 2)::uuid
          );
        
        -- Users can only update their own documents
        DROP POLICY IF EXISTS "Users can update their own KYC documents" ON storage.objects;
        CREATE POLICY "Users can update their own KYC documents" ON storage.objects
          FOR UPDATE
          USING (
            bucket_id = 'kyc_documents' AND
            auth.uid() = SPLIT_PART(name, '/', 2)::uuid
          );
        
        -- Users can only delete their own documents
        DROP POLICY IF EXISTS "Users can delete their own KYC documents" ON storage.objects;
        CREATE POLICY "Users can delete their own KYC documents" ON storage.objects
          FOR DELETE
          USING (
            bucket_id = 'kyc_documents' AND
            (auth.uid() = SPLIT_PART(name, '/', 2)::uuid OR
             EXISTS (
               SELECT 1 FROM profiles
               WHERE id = auth.uid() AND role = 'admin'
             ))
          );
      `;
      
      // Execute SQL
      const { error: rlsError } = await supabase.rpc('execute_sql', { sql: rls_sql });
      
      if (rlsError) {
        console.error('Error setting up RLS policies:', rlsError);
      } else {
        console.log('Successfully set up RLS policies for kyc_documents bucket');
      }
    }
    
    console.log('Storage bucket setup completed successfully!');
    
    return {
      success: true,
      message: 'Storage buckets set up successfully'
    };
  } catch (error) {
    console.error('Error setting up storage buckets:', error);
    return {
      success: false,
      message: error.message || 'An unknown error occurred'
    };
  }
}

// Execute the function
setupStorageBuckets().then(result => {
  console.log('Result:', result);
});
