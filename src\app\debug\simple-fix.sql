-- Simple fix for database schema issues in BrickChain
-- This script focuses only on fixing the property_id column in the investments table
-- Run this in your Supabase SQL Editor

-- Start a transaction so we can roll back if anything goes wrong
BEGIN;

-- Check if required tables exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'properties') THEN
        RAISE EXCEPTION 'Table "properties" does not exist';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'investments') THEN
        RAISE EXCEPTION 'Table "investments" does not exist';
    END IF;
    
    RAISE NOTICE 'Required tables exist. Proceeding with schema modifications.';
END$$;

-- Create backup table for safety
CREATE TABLE IF NOT EXISTS investments_backup AS SELECT * FROM investments;
RAISE NOTICE 'Created backup table: investments_backup';

-- Check the current data types
DO $$
DECLARE
    property_id_type TEXT;
    properties_id_type TEXT;
BEGIN
    -- Get data types
    SELECT data_type INTO property_id_type 
    FROM information_schema.columns 
    WHERE table_name = 'investments' AND column_name = 'property_id';
    
    SELECT data_type INTO properties_id_type 
    FROM information_schema.columns 
    WHERE table_name = 'properties' AND column_name = 'id';
    
    -- Log the current types
    RAISE NOTICE 'Current types: investments.property_id=%, properties.id=%', 
                 property_id_type, properties_id_type;
                 
    -- If there's a mismatch and properties.id is UUID (which is expected)
    IF property_id_type <> properties_id_type AND properties_id_type = 'uuid' THEN
        -- Add a temporary column to store the current property_id values
        ALTER TABLE investments ADD COLUMN IF NOT EXISTS property_id_old BIGINT;
        UPDATE investments SET property_id_old = property_id;
        RAISE NOTICE 'Backed up property_id values to property_id_old';
        
        -- Drop any existing constraints on property_id
        -- First check if any constraint exists
        IF EXISTS (
            SELECT 1
            FROM information_schema.table_constraints
            WHERE table_name = 'investments'
            AND constraint_type = 'FOREIGN KEY'
            AND constraint_name LIKE '%property_id%'
        ) THEN
            -- Get the constraint name and drop it
            EXECUTE format('ALTER TABLE investments DROP CONSTRAINT %I',
                (SELECT constraint_name
                FROM information_schema.table_constraints
                WHERE table_name = 'investments'
                AND constraint_type = 'FOREIGN KEY'
                AND constraint_name LIKE '%property_id%'
                LIMIT 1)
            );
            RAISE NOTICE 'Dropped existing constraint on property_id';
        ELSE
            RAISE NOTICE 'No existing constraint found on property_id';
        END IF;
        
        -- Change the column type to UUID
        ALTER TABLE investments ALTER COLUMN property_id TYPE UUID USING NULL;
        RAISE NOTICE 'Changed investments.property_id to UUID type';
        
        -- Create a mapping table to help with the conversion
        CREATE TEMP TABLE property_id_mapping (
            bigint_id BIGINT PRIMARY KEY,
            uuid_id UUID
        );
        
        -- Insert mappings from properties table
        INSERT INTO property_id_mapping (bigint_id, uuid_id)
        SELECT 
            ROW_NUMBER() OVER (ORDER BY id),
            id
        FROM properties;
        
        -- Update the property_id in investments using the mapping
        UPDATE investments i
        SET property_id = m.uuid_id
        FROM property_id_mapping m
        WHERE i.property_id_old = m.bigint_id;
        
        RAISE NOTICE 'Updated investments.property_id values using mapping';
        
        -- Add foreign key constraint
        ALTER TABLE investments
        ADD CONSTRAINT investments_property_id_fkey
        FOREIGN KEY (property_id)
        REFERENCES properties(id)
        ON DELETE CASCADE;
        RAISE NOTICE 'Added foreign key constraint: investments_property_id_fkey';
    ELSE
        RAISE NOTICE 'No type conversion needed for investments.property_id';
    END IF;
END$$;

-- Verify the changes
DO $$
DECLARE
    property_id_type TEXT;
    properties_id_type TEXT;
BEGIN
    -- Get data types of relevant columns after changes
    SELECT data_type INTO property_id_type 
    FROM information_schema.columns 
    WHERE table_name = 'investments' AND column_name = 'property_id';
    
    SELECT data_type INTO properties_id_type 
    FROM information_schema.columns 
    WHERE table_name = 'properties' AND column_name = 'id';
    
    -- Log the updated types
    RAISE NOTICE 'Updated types: investments.property_id=%, properties.id=%', 
                 property_id_type, properties_id_type;
                 
    -- Check if foreign key constraint exists
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'investments_property_id_fkey'
        AND table_name = 'investments'
    ) THEN
        RAISE NOTICE 'Foreign key constraint exists: investments_property_id_fkey';
    ELSE
        RAISE NOTICE 'Warning: Foreign key constraint does not exist: investments_property_id_fkey';
    END IF;
END$$;

-- Commit the transaction if everything went well
COMMIT;

-- Note: After running this SQL, go to the Supabase Dashboard and refresh the schema cache
-- 1. Visit the Supabase Dashboard
-- 2. Go to Database > Schema
-- 3. Click "Refresh" in the Schema Editor
