import { NextRequest, NextResponse } from 'next/server';
// The Web3Storage client has type issues, but we'll handle them in the implementation
import { create as createW3Client } from '@web3-storage/w3up-client';

export const runtime = 'nodejs';

export async function POST(req: NextRequest) {
  const { imageUrl } = await req.json();
  if (!imageUrl) {
    return NextResponse.json({ error: 'Missing imageUrl' }, { status: 400 });
  }

  // Download image from Supabase Storage
  const response = await fetch(imageUrl);
  if (!response.ok) {
    return NextResponse.json({ error: 'Failed to download image from Supabase Storage' }, { status: 500 });
  }
  const blob = await response.blob();
  const fileName = imageUrl.split('/').pop() || 'image.jpg';
  const file = new File([blob], fileName, { type: blob.type });

  // Upload to web3.storage (w3up)
  const client = await createW3Client();
  // You may need to handle login/space selection here if not already authorized
  // await client.login('<EMAIL>');
  // await client.setCurrentSpace('did:key:...');
  const cid = await client.uploadFile(file);
  const ipfsUrl = `https://w3s.link/ipfs/${cid}`;

  return NextResponse.json({ ipfsUrl });
} 