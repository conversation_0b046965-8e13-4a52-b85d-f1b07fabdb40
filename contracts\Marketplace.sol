// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/token/ERC1155/utils/ERC1155Holder.sol";
import "@openzeppelin/contracts/token/ERC721/utils/ERC721Holder.sol";
import "./PropertyRegistry.sol";
import "./FractionalOwnership.sol";
import "./BrickToken.sol";

/**
 * @title AssetMarketplace
 * @dev Facilitates buying and selling of property tokens (both ERC-721 and ERC-1155)
 */
contract AssetMarketplace is Ownable, ReentrancyGuard, ERC1155Holder, ERC721Holder {
    // Property registry contract
    PropertyRegistry private _propertyRegistry;

    // Fractional ownership contract
    FractionalOwnership private _fractionalOwnership;

    // Brick token contract
    BrickToken private _brickToken;

    // Listing types
    enum ListingType { PROPERTY, SHARES }

    // Listing struct
    struct Listing {
        uint256 id;
        uint256 propertyId;
        address seller;
        uint256 shares; // 0 for full property listings
        uint256 price;
        uint256 timestamp;
        bool active;
        ListingType listingType;
        bool acceptBCT;
    }

    // Offer struct
    struct Offer {
        uint256 id;
        uint256 listingId;
        address buyer;
        uint256 price;
        uint256 timestamp;
        bool active;
        bool useBCT;
    }

    // Mapping from listing ID to Listing
    mapping(uint256 => Listing) private _listings;

    // Mapping from offer ID to Offer
    mapping(uint256 => Offer) private _offers;

    // Next listing ID
    uint256 private _nextListingId = 1;

    // Next offer ID
    uint256 private _nextOfferId = 1;

    // Platform fee percentage (scaled by 10^2)
    uint256 private _platformFeePercentage = 250; // 2.5%

    // Events
    event PropertyListed(uint256 indexed listingId, uint256 indexed propertyId, address indexed seller, uint256 price, bool acceptBCT);
    event SharesListed(uint256 indexed listingId, uint256 indexed propertyId, address indexed seller, uint256 shares, uint256 price, bool acceptBCT);
    event ListingCancelled(uint256 indexed listingId);
    event PropertyPurchased(uint256 indexed listingId, uint256 indexed propertyId, address indexed buyer, uint256 price, bool usedBCT);
    event SharesPurchased(uint256 indexed listingId, uint256 indexed propertyId, address indexed buyer, uint256 shares, uint256 price, bool usedBCT);
    event OfferCreated(uint256 indexed offerId, uint256 indexed listingId, address indexed buyer, uint256 price, bool useBCT);
    event OfferCancelled(uint256 indexed offerId);
    event OfferAccepted(uint256 indexed offerId, uint256 indexed listingId);

    constructor(
        address propertyRegistryAddress,
        address fractionalOwnershipAddress,
        address brickTokenAddress
    ) Ownable(msg.sender) {
        _propertyRegistry = PropertyRegistry(propertyRegistryAddress);
        _fractionalOwnership = FractionalOwnership(fractionalOwnershipAddress);
        _brickToken = BrickToken(brickTokenAddress);
    }

    /**
     * @dev Lists a full property for sale
     * @param propertyId ID of the property
     * @param price Price in ETH
     * @param acceptBCT Whether to accept BCT tokens as payment
     * @return listingId ID of the created listing
     */
    function listProperty(
        uint256 propertyId,
        uint256 price,
        bool acceptBCT
    ) external nonReentrant returns (uint256) {
        require(price > 0, "Price must be greater than 0");

        // Check if caller is the property owner
        require(_propertyRegistry.ownerOf(propertyId) == msg.sender, "Not the property owner");
        require(!_propertyRegistry.isLocked(propertyId), "Property is locked (fractionalized)");

        // Create listing
        uint256 listingId = _nextListingId++;
        _listings[listingId] = Listing({
            id: listingId,
            propertyId: propertyId,
            seller: msg.sender,
            shares: 0, // 0 for full property
            price: price,
            timestamp: block.timestamp,
            active: true,
            listingType: ListingType.PROPERTY,
            acceptBCT: acceptBCT
        });

        // Transfer property to marketplace
        _propertyRegistry.safeTransferFrom(msg.sender, address(this), propertyId);

        emit PropertyListed(listingId, propertyId, msg.sender, price, acceptBCT);

        return listingId;
    }

    /**
     * @dev Lists shares of a property for sale
     * @param propertyId ID of the property
     * @param shares Number of shares to sell
     * @param price Total price for all shares in ETH
     * @param acceptBCT Whether to accept BCT tokens as payment
     * @return listingId ID of the created listing
     */
    function listShares(
        uint256 propertyId,
        uint256 shares,
        uint256 price,
        bool acceptBCT
    ) external nonReentrant returns (uint256) {
        require(shares > 0, "Shares must be greater than 0");
        require(price > 0, "Price must be greater than 0");

        // Check if seller has enough shares
        require(_fractionalOwnership.balanceOf(msg.sender, propertyId) >= shares, "Not enough shares");

        // Create listing
        uint256 listingId = _nextListingId++;
        _listings[listingId] = Listing({
            id: listingId,
            propertyId: propertyId,
            seller: msg.sender,
            shares: shares,
            price: price,
            timestamp: block.timestamp,
            active: true,
            listingType: ListingType.SHARES,
            acceptBCT: acceptBCT
        });

        // Transfer shares to marketplace
        _fractionalOwnership.safeTransferFrom(msg.sender, address(this), propertyId, shares, "");

        emit SharesListed(listingId, propertyId, msg.sender, shares, price, acceptBCT);

        return listingId;
    }

    /**
     * @dev Cancels a listing
     * @param listingId ID of the listing
     */
    function cancelListing(uint256 listingId) external nonReentrant {
        Listing storage listing = _listings[listingId];
        require(listing.active, "Listing not active");
        require(listing.seller == msg.sender, "Not the seller");

        listing.active = false;

        // Return asset to seller
        if (listing.listingType == ListingType.PROPERTY) {
            _propertyRegistry.safeTransferFrom(address(this), msg.sender, listing.propertyId);
        } else {
            _fractionalOwnership.safeTransferFrom(address(this), msg.sender, listing.propertyId, listing.shares, "");
        }

        emit ListingCancelled(listingId);
    }

    /**
     * @dev Purchases a listed property or shares with ETH
     * @param listingId ID of the listing
     */
    function purchase(uint256 listingId) external payable nonReentrant {
        _purchaseInternal(listingId, false);
    }

    /**
     * @dev Purchases a listed property or shares with BCT tokens
     * @param listingId ID of the listing
     */
    function purchaseWithBCT(uint256 listingId) external nonReentrant {
        _purchaseInternal(listingId, true);
    }

    /**
     * @dev Internal function to purchase a listing
     * @param listingId ID of the listing
     * @param useBCT Whether to use BCT tokens for payment
     */
    function _purchaseInternal(uint256 listingId, bool useBCT) private {
        Listing storage listing = _listings[listingId];
        require(listing.active, "Listing not active");

        // Check if BCT is accepted for this listing
        if (useBCT) {
            require(listing.acceptBCT, "BCT not accepted for this listing");
        }

        // Calculate platform fee
        uint256 platformFee = (listing.price * _platformFeePercentage) / 10000;
        uint256 sellerAmount = listing.price - platformFee;

        // Handle payment
        if (useBCT) {
            // Apply discount if using BCT
            platformFee = _brickToken.calculateDiscount(msg.sender, platformFee);

            // Transfer BCT tokens
            uint256 bctAmount = listing.price + platformFee;
            _brickToken.transferFrom(msg.sender, address(this), bctAmount);

            // Burn a portion of the BCT
            _brickToken.burn(bctAmount / 2);

            // Transfer seller amount in BCT
            _brickToken.transfer(listing.seller, sellerAmount);
        } else {
            // Check if enough ETH was sent
            require(msg.value >= listing.price, "Insufficient ETH sent");

            // Transfer ETH to seller
            payable(listing.seller).transfer(sellerAmount);

            // Refund excess ETH
            if (msg.value > listing.price) {
                payable(msg.sender).transfer(msg.value - listing.price);
            }
        }

        // Transfer asset to buyer
        if (listing.listingType == ListingType.PROPERTY) {
            _propertyRegistry.safeTransferFrom(address(this), msg.sender, listing.propertyId);
            emit PropertyPurchased(listingId, listing.propertyId, msg.sender, listing.price, useBCT);
        } else {
            _fractionalOwnership.safeTransferFrom(address(this), msg.sender, listing.propertyId, listing.shares, "");
            emit SharesPurchased(listingId, listing.propertyId, msg.sender, listing.shares, listing.price, useBCT);
        }

        // Reward buyer with BCT tokens (0.5% of purchase value)
        uint256 rewardAmount = (listing.price * 50) / 10000;
        _brickToken.mint(msg.sender, rewardAmount);

        // Mark listing as inactive
        listing.active = false;
    }

    /**
     * @dev Creates an offer for a listing
     * @param listingId ID of the listing
     * @param price Offered price
     * @param useBCT Whether to use BCT tokens for payment
     * @return offerId ID of the created offer
     */
    function createOffer(
        uint256 listingId,
        uint256 price,
        bool useBCT
    ) external payable nonReentrant returns (uint256) {
        Listing storage listing = _listings[listingId];
        require(listing.active, "Listing not active");

        // Check if BCT is accepted for this listing
        if (useBCT) {
            require(listing.acceptBCT, "BCT not accepted for this listing");
        } else {
            // Check if enough ETH was sent
            require(msg.value >= price, "Insufficient ETH sent");
        }

        // Create offer
        uint256 offerId = _nextOfferId++;
        _offers[offerId] = Offer({
            id: offerId,
            listingId: listingId,
            buyer: msg.sender,
            price: price,
            timestamp: block.timestamp,
            active: true,
            useBCT: useBCT
        });

        emit OfferCreated(offerId, listingId, msg.sender, price, useBCT);

        return offerId;
    }

    /**
     * @dev Cancels an offer
     * @param offerId ID of the offer
     */
    function cancelOffer(uint256 offerId) external nonReentrant {
        Offer storage offer = _offers[offerId];
        require(offer.active, "Offer not active");
        require(offer.buyer == msg.sender, "Not the buyer");

        offer.active = false;

        // Refund ETH to buyer if ETH was used
        if (!offer.useBCT) {
            payable(msg.sender).transfer(offer.price);
        }

        emit OfferCancelled(offerId);
    }

    /**
     * @dev Accepts an offer
     * @param offerId ID of the offer
     */
    function acceptOffer(uint256 offerId) external nonReentrant {
        Offer storage offer = _offers[offerId];
        require(offer.active, "Offer not active");

        Listing storage listing = _listings[offer.listingId];
        require(listing.active, "Listing not active");
        require(listing.seller == msg.sender, "Not the seller");

        // Calculate platform fee
        uint256 platformFee = (offer.price * _platformFeePercentage) / 10000;
        uint256 sellerAmount = offer.price - platformFee;

        // Handle payment
        if (offer.useBCT) {
            // Apply discount if using BCT
            platformFee = _brickToken.calculateDiscount(offer.buyer, platformFee);

            // Transfer BCT tokens from buyer to this contract
            uint256 bctAmount = offer.price + platformFee;
            _brickToken.transferFrom(offer.buyer, address(this), bctAmount);

            // Burn a portion of the BCT
            _brickToken.burn(bctAmount / 2);

            // Transfer seller amount in BCT
            _brickToken.transfer(msg.sender, sellerAmount);
        } else {
            // Transfer ETH to seller
            payable(msg.sender).transfer(sellerAmount);
        }

        // Transfer asset to buyer
        if (listing.listingType == ListingType.PROPERTY) {
            _propertyRegistry.safeTransferFrom(address(this), offer.buyer, listing.propertyId);
        } else {
            _fractionalOwnership.safeTransferFrom(address(this), offer.buyer, listing.propertyId, listing.shares, "");
        }

        // Reward buyer with BCT tokens (0.5% of purchase value)
        uint256 rewardAmount = (offer.price * 50) / 10000;
        _brickToken.mint(offer.buyer, rewardAmount);

        // Mark listing and offer as inactive
        listing.active = false;
        offer.active = false;

        emit OfferAccepted(offerId, offer.listingId);
    }

    /**
     * @dev Sets the platform fee percentage
     * @param feePercentage New fee percentage (scaled by 10^2)
     */
    function setPlatformFeePercentage(uint256 feePercentage) external onlyOwner {
        require(feePercentage <= 1000, "Fee too high"); // Max 10%
        _platformFeePercentage = feePercentage;
    }

    /**
     * @dev Returns the platform fee percentage
     */
    function getPlatformFeePercentage() external view returns (uint256) {
        return _platformFeePercentage;
    }

    /**
     * @dev Returns a listing by ID
     * @param listingId ID of the listing
     */
    function getListing(uint256 listingId) external view returns (Listing memory) {
        return _listings[listingId];
    }

    /**
     * @dev Returns an offer by ID
     * @param offerId ID of the offer
     */
    function getOffer(uint256 offerId) external view returns (Offer memory) {
        return _offers[offerId];
    }

    /**
     * @dev Withdraws platform fees
     */
    function withdrawFees() external onlyOwner {
        // Withdraw ETH
        if (address(this).balance > 0) {
            payable(owner()).transfer(address(this).balance);
        }

        // Withdraw BCT
        uint256 bctBalance = _brickToken.balanceOf(address(this));
        if (bctBalance > 0) {
            _brickToken.transfer(owner(), bctBalance);
        }
    }
}
