// Navigation script for BrickChain debug pages
console.log('BrickChain Debug Navigation Helper');
console.log('===================================');
console.log('');
console.log('Available debug pages:');
console.log('1. Main Debug Dashboard: http://localhost:3000/debug');
console.log('2. IPFS Test Page: http://localhost:3000/debug/test-returns');
console.log('');
console.log('If accessing these pages directly doesn\'t work, try:');
console.log('- Check if your development server is running (npm run dev)');
console.log('- Verify there are no errors in your browser console');
console.log('- Try navigating from the homepage to /debug first');
console.log('');
console.log('You may also add a direct link to your homepage:');
console.log(`
// Example code to add to your homepage component:
<div className="mt-4 p-2 bg-gray-100 rounded text-center">
  <a href="/debug" className="text-blue-600 hover:underline">Debug Dashboard</a>
</div>
`); 