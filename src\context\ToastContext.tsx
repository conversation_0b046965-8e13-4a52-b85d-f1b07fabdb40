'use client';

import React, { createContext, useContext, useState, useCallback } from 'react';
import { ToastContainer, type ToastProps, type ToastType } from '@/components/ui/Toast';

interface ToastContextType {
  toasts: ToastProps[];
  addToast: (toast: Omit<ToastProps, 'id' | 'onClose'>) => string;
  removeToast: (id: string) => void;
  clearToasts: () => void;
  success: (title: string, description?: string, options?: Partial<ToastProps>) => string;
  error: (title: string, description?: string, options?: Partial<ToastProps>) => string;
  warning: (title: string, description?: string, options?: Partial<ToastProps>) => string;
  info: (title: string, description?: string, options?: Partial<ToastProps>) => string;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

export const useToast = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

interface ToastProviderProps {
  children: React.ReactNode;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center';
  maxToasts?: number;
}

export const ToastProvider: React.FC<ToastProviderProps> = ({
  children,
  position = 'top-right',
  maxToasts = 5,
}) => {
  const [toasts, setToasts] = useState<ToastProps[]>([]);

  const generateId = useCallback(() => {
    return `toast-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  const addToast = useCallback((toast: Omit<ToastProps, 'id' | 'onClose'>) => {
    const id = generateId();
    const newToast: ToastProps = {
      ...toast,
      id,
      onClose: removeToast,
    };

    setToasts(prev => {
      const updated = [...prev, newToast];
      // Limit the number of toasts
      if (updated.length > maxToasts) {
        return updated.slice(-maxToasts);
      }
      return updated;
    });

    return id;
  }, [generateId, maxToasts]);

  const removeToast = useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  }, []);

  const clearToasts = useCallback(() => {
    setToasts([]);
  }, []);

  // Convenience methods for different toast types
  const success = useCallback((
    title: string, 
    description?: string, 
    options?: Partial<ToastProps>
  ) => {
    return addToast({
      type: 'success',
      title,
      description,
      ...options,
    });
  }, [addToast]);

  const error = useCallback((
    title: string, 
    description?: string, 
    options?: Partial<ToastProps>
  ) => {
    return addToast({
      type: 'error',
      title,
      description,
      duration: 0, // Error toasts don't auto-dismiss by default
      ...options,
    });
  }, [addToast]);

  const warning = useCallback((
    title: string, 
    description?: string, 
    options?: Partial<ToastProps>
  ) => {
    return addToast({
      type: 'warning',
      title,
      description,
      ...options,
    });
  }, [addToast]);

  const info = useCallback((
    title: string, 
    description?: string, 
    options?: Partial<ToastProps>
  ) => {
    return addToast({
      type: 'info',
      title,
      description,
      ...options,
    });
  }, [addToast]);

  const value: ToastContextType = {
    toasts,
    addToast,
    removeToast,
    clearToasts,
    success,
    error,
    warning,
    info,
  };

  return (
    <ToastContext.Provider value={value}>
      {children}
      <ToastContainer toasts={toasts} position={position} />
    </ToastContext.Provider>
  );
};
