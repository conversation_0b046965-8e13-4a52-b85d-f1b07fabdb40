import React from 'react';
export default function HowItWorks() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-r from-indigo-900 to-purple-900 text-white">
      <div className="bg-indigo-800 bg-opacity-50 p-8 rounded-lg shadow-lg max-w-2xl w-full">
        <h1 className="text-3xl font-bold mb-4">How BrickChain Works</h1>
        <ol className="list-decimal list-inside mb-6 space-y-4">
          <li>
            <span className="font-semibold">List Your Property:</span> Property owners submit property details and documents. All information is securely stored on IPFS for transparency and immutability.
          </li>
          <li>
            <span className="font-semibold">Tokenization:</span> Each property is minted as an ERC-721 NFT, representing full ownership. Owners can then fractionalize the property into ERC-1155 tokens (shares).
          </li>
          <li>
            <span className="font-semibold">Marketplace Listing:</span> Fractionalized properties are listed on the BrickChain marketplace, where investors can browse and purchase shares using crypto.
          </li>
          <li>
            <span className="font-semibold">Invest & Trade:</span> Investors buy shares, gaining exposure to real estate returns. Shares can be traded on the marketplace, providing liquidity.
          </li>
          <li>
            <span className="font-semibold">Earn Returns:</span> Rental income and appreciation are distributed to investors based on their shareholding. All transactions are transparent and recorded on-chain.
          </li>
        </ol>
        <h2 className="text-xl font-semibold mb-2">Why BrickChain?</h2>
        <ul className="list-disc list-inside space-y-1">
          <li>Global access to real estate investment</li>
          <li>Low minimum investment</li>
          <li>Instant liquidity via token trading</li>
          <li>Secure, transparent, and decentralized</li>
        </ul>
      </div>
    </div>
  );
} 