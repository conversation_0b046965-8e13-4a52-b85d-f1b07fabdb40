'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useSupabaseClient } from '@supabase/auth-helpers-react';
import { useWalletAuth } from '@/context/WalletAuthContext';
import { CheckCircle2, Upload, User, MapPin, FileText, Loader2, ChevronRight, ChevronLeft } from 'lucide-react';

interface KYCStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  estimatedTime: string;
}

export default function SimplifiedKYC() {
  const router = useRouter();
  const supabase = useSupabaseClient();
  const { user } = useWalletAuth();
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [formData, setFormData] = useState({
    fullName: '',
    dateOfBirth: '',
    address: '',
    city: '',
    country: '',
    postalCode: '',
    idType: 'passport',
    idNumber: '',
    idFrontFile: null as File | null,
    idBackFile: null as File | null,
    selfieFile: null as File | null,
  });

  // Define KYC steps
  const steps: KYCStep[] = [
    {
      id: 'personal',
      title: 'Personal Information',
      description: 'Basic details for identification',
      icon: <User className="h-6 w-6" />,
      estimatedTime: '2 min',
    },
    {
      id: 'address',
      title: 'Address Verification',
      description: 'Your residential address',
      icon: <MapPin className="h-6 w-6" />,
      estimatedTime: '2 min',
    },
    {
      id: 'document',
      title: 'Document Upload',
      description: 'Government-issued ID verification',
      icon: <FileText className="h-6 w-6" />,
      estimatedTime: '3 min',
    },
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>, fieldName: string) => {
    if (e.target.files && e.target.files[0]) {
      setFormData({ ...formData, [fieldName]: e.target.files[0] });
    }
  };

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
      window.scrollTo(0, 0);
    } else {
      handleSubmit();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
      window.scrollTo(0, 0);
    }
  };

  const handleSubmit = async () => {
    if (!user) {
      setError('You must be logged in to complete KYC');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Simulate API call with a delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      // In a real implementation, you would:
      // 1. Upload the files to storage
      // 2. Create KYC record in database
      // 3. Update user profile with KYC status

      // Example of updating user profile with KYC status
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ 
          kyc_status: 'completed',
          kyc_completed_at: new Date().toISOString(),
          full_name: formData.fullName
        })
        .eq('id', user.id);

      if (updateError) throw updateError;

      setSuccess(true);
      
      // Redirect after a delay
      setTimeout(() => {
        router.push('/dashboard');
      }, 3000);
    } catch (err) {
      console.error('KYC submission error:', err);
      setError('Failed to submit KYC information. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Render success state
  if (success) {
    return (
      <div className="max-w-2xl mx-auto p-6 bg-gradient-to-br from-gray-900 to-indigo-950 rounded-xl border border-indigo-800 shadow-xl">
        <div className="text-center py-12">
          <div className="w-20 h-20 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
            <CheckCircle2 className="h-12 w-12 text-green-500" />
          </div>
          <h2 className="text-3xl font-bold text-white mb-4">KYC Verification Submitted</h2>
          <p className="text-gray-300 mb-8 max-w-md mx-auto">
            Your information has been submitted successfully and is now being reviewed. You'll be notified once the verification is complete.
          </p>
          <div className="flex justify-center">
            <button
              onClick={() => router.push('/dashboard')}
              className="px-6 py-3 bg-indigo-600 hover:bg-indigo-700 rounded-lg text-white font-medium transition-colors"
            >
              Go to Dashboard
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto p-6 bg-gradient-to-br from-gray-900 to-indigo-950 rounded-xl border border-indigo-800 shadow-xl">
      {/* Progress indicator */}
      <div className="mb-8">
        <div className="flex justify-between mb-2">
          {steps.map((step, index) => (
            <div 
              key={step.id}
              className={`flex flex-col items-center ${
                index <= currentStep ? 'text-indigo-400' : 'text-gray-500'
              }`}
              style={{ width: `${100 / steps.length}%` }}
            >
              <div 
                className={`w-10 h-10 rounded-full flex items-center justify-center mb-2 ${
                  index < currentStep 
                    ? 'bg-indigo-600 text-white' 
                    : index === currentStep 
                      ? 'bg-indigo-800 text-white border-2 border-indigo-500' 
                      : 'bg-gray-800 text-gray-500'
                }`}
              >
                {index < currentStep ? (
                  <CheckCircle2 className="h-5 w-5" />
                ) : (
                  <span>{index + 1}</span>
                )}
              </div>
              <span className="text-sm font-medium">{step.title}</span>
            </div>
          ))}
        </div>
        <div className="relative h-2 bg-gray-800 rounded-full mt-4">
          <div 
            className="absolute h-2 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-full transition-all duration-300"
            style={{ width: `${(currentStep / (steps.length - 1)) * 100}%` }}
          ></div>
        </div>
      </div>

      {/* Current step content */}
      <div className="mb-8">
        <div className="flex items-center mb-6">
          <div className="w-12 h-12 rounded-lg bg-indigo-800/50 flex items-center justify-center mr-4">
            {steps[currentStep].icon}
          </div>
          <div>
            <h2 className="text-2xl font-bold text-white">{steps[currentStep].title}</h2>
            <p className="text-gray-400">{steps[currentStep].description}</p>
          </div>
          <div className="ml-auto text-gray-400 text-sm flex items-center">
            <span className="mr-2">Est. time:</span>
            <span>{steps[currentStep].estimatedTime}</span>
          </div>
        </div>

        {/* Form fields based on current step */}
        {currentStep === 0 && (
          <div className="space-y-4">
            <div>
              <label className="block text-gray-300 mb-1">Full Name</label>
              <input
                type="text"
                name="fullName"
                value={formData.fullName}
                onChange={handleInputChange}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                placeholder="Enter your full name"
                required
              />
            </div>
            <div>
              <label className="block text-gray-300 mb-1">Date of Birth</label>
              <input
                type="date"
                name="dateOfBirth"
                value={formData.dateOfBirth}
                onChange={handleInputChange}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                required
              />
            </div>
          </div>
        )}

        {currentStep === 1 && (
          <div className="space-y-4">
            <div>
              <label className="block text-gray-300 mb-1">Street Address</label>
              <input
                type="text"
                name="address"
                value={formData.address}
                onChange={handleInputChange}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                placeholder="Enter your street address"
                required
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-gray-300 mb-1">City</label>
                <input
                  type="text"
                  name="city"
                  value={formData.city}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                  placeholder="City"
                  required
                />
              </div>
              <div>
                <label className="block text-gray-300 mb-1">Postal Code</label>
                <input
                  type="text"
                  name="postalCode"
                  value={formData.postalCode}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                  placeholder="Postal code"
                  required
                />
              </div>
            </div>
            <div>
              <label className="block text-gray-300 mb-1">Country</label>
              <input
                type="text"
                name="country"
                value={formData.country}
                onChange={handleInputChange}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                placeholder="Country"
                required
              />
            </div>
          </div>
        )}

        {currentStep === 2 && (
          <div className="space-y-6">
            <div>
              <label className="block text-gray-300 mb-1">ID Type</label>
              <select
                name="idType"
                value={formData.idType}
                onChange={handleInputChange}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                required
              >
                <option value="passport">Passport</option>
                <option value="driverLicense">Driver's License</option>
                <option value="nationalId">National ID</option>
              </select>
            </div>
            <div>
              <label className="block text-gray-300 mb-1">ID Number</label>
              <input
                type="text"
                name="idNumber"
                value={formData.idNumber}
                onChange={handleInputChange}
                className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                placeholder="Enter your ID number"
                required
              />
            </div>
            <div className="space-y-4">
              <p className="text-gray-300 font-medium">Upload Document Images</p>
              
              <div className="border border-dashed border-gray-600 rounded-lg p-4 hover:border-indigo-500 transition-colors">
                <label className="flex flex-col items-center cursor-pointer">
                  <Upload className="h-8 w-8 text-gray-400 mb-2" />
                  <span className="text-gray-300 mb-1">Front of ID</span>
                  <span className="text-xs text-gray-500 mb-2">JPG, PNG or PDF (max 5MB)</span>
                  <input
                    type="file"
                    className="hidden"
                    accept="image/jpeg,image/png,application/pdf"
                    onChange={(e) => handleFileChange(e, 'idFrontFile')}
                  />
                  {formData.idFrontFile && (
                    <span className="text-green-500 text-sm mt-2">
                      {formData.idFrontFile.name} uploaded
                    </span>
                  )}
                </label>
              </div>
            </div>
          </div>
        )}

        {/* Error message */}
        {error && (
          <div className="mt-4 p-3 bg-red-900/40 border border-red-800 rounded-lg">
            <p className="text-red-300 text-sm">{error}</p>
          </div>
        )}
      </div>

      {/* Navigation buttons */}
      <div className="flex justify-between">
        <button
          type="button"
          onClick={handlePrevious}
          disabled={currentStep === 0 || loading}
          className={`px-6 py-3 rounded-lg text-white font-medium flex items-center ${
            currentStep === 0 
              ? 'opacity-50 cursor-not-allowed bg-gray-700' 
              : 'bg-gray-800 hover:bg-gray-700'
          }`}
        >
          <ChevronLeft className="h-5 w-5 mr-1" />
          Back
        </button>
        
        <button
          type="button"
          onClick={handleNext}
          disabled={loading}
          className="px-6 py-3 bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 rounded-lg text-white font-medium flex items-center"
        >
          {loading ? (
            <>
              <Loader2 className="h-5 w-5 mr-2 animate-spin" />
              Processing...
            </>
          ) : (
            <>
              {currentStep < steps.length - 1 ? 'Continue' : 'Submit'}
              <ChevronRight className="h-5 w-5 ml-1" />
            </>
          )}
        </button>
      </div>
    </div>
  );
}
