'use client';
import React, { useState, useEffect } from 'react';
import { createClient } from '@supabase/supabase-js';
import { Building, Calendar, DollarSign, TrendingUp, ArrowRight, Filter, Search } from 'lucide-react';
// No longer need to import DashboardWrapper
import { useUser } from '../../../../context/UserContext';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';
const supabase = createClient(supabaseUrl, supabaseAnonKey);

type Property = {
  name: string;
  image_url: string;
  location: string;
  return_rate: number;
};

type InvestmentWithProperty = {
  id: string;
  property_id: string;
  amount: number;
  date_invested: string;
  properties: Property;
};

type Transaction = {
  id: string;
  property_id: string;
  amount: number;
  date_invested: string;
  type: 'investment' | 'return';
  property_name?: string;
  property_image?: string;
  property_location?: string;
  return_rate?: number;
  monthly_return?: number;
};

export default function InvestorHistoryPage() {
  const { user } = useUser();
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<'all' | 'investment' | 'return'>('all');
  const [totalInvested, setTotalInvested] = useState(0);
  const [totalReturns, setTotalReturns] = useState(0);
  const [exporting, setExporting] = useState(false);

  useEffect(() => {
    const fetchTransactionHistory = async () => {
      if (!user) return;

      try {
        setLoading(true);
        setError(null);

        // Fetch user's investments without nested properties
        const { data: userInvestments, error: investmentsError } = await supabase
          .from('investments')
          .select(`
            id,
            property_id,
            amount,
            date_invested
          `)
          .eq('investor_id', user.id)
          .order('date_invested', { ascending: false });

        if (investmentsError) throw investmentsError;

        // Early exit if no investments
        if (!userInvestments || userInvestments.length === 0) {
          setTransactions([]);
          setTotalInvested(0);
          setTotalReturns(0);
          setLoading(false);
          return;
        }

        // Get property IDs from investments
        const propertyIds = userInvestments.map(inv => inv.property_id);

        // Fetch properties in a separate query
        const { data: properties, error: propertiesError } = await supabase
          .from('properties')
          .select('id, name, image_url, location, return_rate')
          .in('id', propertyIds);

        if (propertiesError) throw propertiesError;

        // Create a map of property data by ID for easy lookup
        const propertyMap: Record<string, {
          name: string;
          image_url: string;
          location: string;
          return_rate: number
        }> = {};

        (properties || []).forEach(property => {
          if (property && property.id) {
            propertyMap[property.id] = {
              name: property.name,
              image_url: property.image_url,
              location: property.location,
              return_rate: property.return_rate
            };
          }
        });

        // Format the investments data
        const formattedTransactions = userInvestments.map(inv => {
          const property = propertyMap[inv.property_id] || {
            name: 'Unknown Property',
            image_url: '',
            location: 'Unknown Location',
            return_rate: 0
          };

          return {
            id: inv.id,
            property_id: inv.property_id,
            amount: inv.amount,
            date_invested: inv.date_invested,
            type: 'investment' as const,
            property_name: property.name,
            property_image: property.image_url,
            property_location: property.location,
            return_rate: property.return_rate,
            monthly_return: (inv.amount * (property.return_rate || 0)) / 12 / 100
          };
        });

        // Add return transactions (simulated for now)
        const returnTransactions = formattedTransactions.map(inv => ({
          ...inv,
          id: `return-${inv.id}`,
          type: 'return' as const,
          amount: inv.monthly_return || 0,
          date_invested: new Date(new Date(inv.date_invested).getTime() + 30 * 24 * 60 * 60 * 1000).toISOString()
        }));

        const allTransactions = [...formattedTransactions, ...returnTransactions]
          .sort((a, b) => new Date(b.date_invested).getTime() - new Date(a.date_invested).getTime());

        setTransactions(allTransactions);

        // Calculate totals
        const invested = formattedTransactions.reduce((sum, inv) => sum + inv.amount, 0);
        const returns = returnTransactions.reduce((sum, inv) => sum + inv.amount, 0);

        setTotalInvested(invested);
        setTotalReturns(returns);
      } catch (error: any) {
        const errorMsg = error?.message || JSON.stringify(error) || 'Failed to fetch transaction history.';
        console.error('Error fetching transaction history:', errorMsg);
        setError('Failed to load transaction history. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchTransactionHistory();
  }, [user]);

  // Filter transactions based on search term and type filter
  const filteredTransactions = transactions.filter(transaction => {
    const matchesSearch = searchTerm === '' ||
      transaction.property_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.property_location?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesType = typeFilter === 'all' || transaction.type === typeFilter;

    return matchesSearch && matchesType;
  });

  const exportToCSV = async () => {
    try {
      setExporting(true);

      // Prepare CSV data
      const headers = ['Date', 'Type', 'Property', 'Location', 'Amount', 'Return Rate'];
      const rows = filteredTransactions.map(t => [
        new Date(t.date_invested).toLocaleDateString(),
        t.type === 'investment' ? 'Investment' : 'Return',
        t.property_name || 'N/A',
        t.property_location || 'N/A',
        t.amount.toLocaleString(),
        t.return_rate ? `${t.return_rate}%` : 'N/A'
      ]);

      const csvContent = [
        headers.join(','),
        ...rows.map(row => row.join(','))
      ].join('\n');

      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `transaction_history_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Error exporting data:', error);
      setError('Failed to export data. Please try again.');
    } finally {
      setExporting(false);
    }
  };

  return (
      <div className="space-y-6">
        {error && (
          <div className="bg-red-900/30 border border-red-800 rounded-xl p-4">
            <p className="text-red-300 whitespace-pre-line">{error}</p>
          </div>
        )}

        {/* Transaction Summary */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 md:gap-6">
          <div className="bg-indigo-900/30 border border-indigo-800 rounded-xl p-4 md:p-6">
            <div className="flex items-start justify-between">
              <div>
                <p className="text-sm md:text-base text-gray-400">Total Invested</p>
                <h3 className="text-xl md:text-2xl font-bold text-white mt-1">
                  ${loading ? '...' : totalInvested.toLocaleString()}
                </h3>
              </div>
              <div className="p-2 md:p-3 rounded-lg bg-blue-600">
                <DollarSign className="text-white w-5 h-5 md:w-6 md:h-6" />
              </div>
            </div>
          </div>

          <div className="bg-indigo-900/30 border border-indigo-800 rounded-xl p-4 md:p-6">
            <div className="flex items-start justify-between">
              <div>
                <p className="text-sm md:text-base text-gray-400">Total Returns</p>
                <h3 className="text-xl md:text-2xl font-bold text-white mt-1">
                  ${loading ? '...' : totalReturns.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                </h3>
              </div>
              <div className="p-2 md:p-3 rounded-lg bg-green-600">
                <TrendingUp className="text-white w-5 h-5 md:w-6 md:h-6" />
              </div>
            </div>
          </div>
        </div>

        {/* Transaction History */}
        <div className="bg-indigo-900/30 border border-indigo-800 rounded-xl overflow-hidden">
          <div className="p-4 md:p-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <h3 className="text-lg md:text-xl font-bold text-white">Transaction History</h3>
                <p className="text-sm md:text-base text-gray-400 mt-1">Your complete investment history</p>
              </div>

              <div className="flex flex-col sm:flex-row gap-3">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search size={18} className="text-gray-500" />
                  </div>
                  <input
                    type="text"
                    placeholder="Search transactions..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 pr-4 py-2 bg-indigo-900/30 border border-indigo-800 rounded-lg text-white w-full text-sm md:text-base focus:ring-2 focus:ring-indigo-500"
                  />
                </div>

                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Filter size={18} className="text-gray-500" />
                  </div>
                  <select
                    value={typeFilter}
                    onChange={(e) => setTypeFilter(e.target.value as 'all' | 'investment' | 'return')}
                    className="pl-10 pr-4 py-2 bg-indigo-900/30 border border-indigo-800 rounded-lg text-white text-sm md:text-base focus:ring-2 focus:ring-indigo-500 appearance-none"
                  >
                    <option value="all">All Transactions</option>
                    <option value="investment">Investments</option>
                    <option value="return">Returns</option>
                  </select>
                </div>

                {/* Export Button */}
                <button
                  onClick={exportToCSV}
                  disabled={exporting || filteredTransactions.length === 0}
                  className="inline-flex items-center px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {exporting ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Exporting...
                    </>
                  ) : (
                    <>
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                      </svg>
                      Export CSV
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>

          {loading ? (
            <div className="p-4 md:p-6 space-y-4">
              {[1, 2, 3].map((_, index) => (
                <div key={index} className="animate-pulse flex items-center space-x-4">
                  <div className="w-16 h-16 bg-indigo-800/40 rounded-lg"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-indigo-800/40 rounded w-1/4"></div>
                    <div className="h-3 bg-indigo-800/40 rounded w-1/3"></div>
                  </div>
                  <div className="h-6 bg-indigo-800/40 rounded w-16"></div>
                </div>
              ))}
            </div>
          ) : filteredTransactions.length === 0 ? (
            <div className="p-8 text-center">
              <Calendar size={48} className="mx-auto text-indigo-600/40 mb-4" />
              <h3 className="text-lg md:text-xl font-semibold text-white mb-2">No Transactions Found</h3>
              <p className="text-sm md:text-base text-gray-400">
                {searchTerm || typeFilter !== 'all'
                  ? 'Try adjusting your search or filter criteria'
                  : 'You haven\'t made any investments yet'}
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <div className="min-w-full divide-y divide-indigo-800/30">
                {filteredTransactions.map((transaction) => (
                  <div key={transaction.id} className="p-4 md:p-6 hover:bg-indigo-800/10">
                    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                      <div className="flex items-center space-x-4">
                        <div className="w-16 h-16 rounded-lg bg-indigo-800/20 overflow-hidden flex-shrink-0">
                          {transaction.property_image ? (
                            <img
                              src={transaction.property_image}
                              alt={transaction.property_name}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <Building size={24} className="m-auto text-indigo-400" />
                          )}
                        </div>
                        <div>
                          <h4 className="font-medium text-white">{transaction.property_name}</h4>
                          <p className="text-sm text-gray-400">{transaction.property_location}</p>
                        </div>
                      </div>
                      <div className="flex flex-col sm:items-end gap-1">
                        <div className="flex items-center gap-2">
                          <span className={`font-medium ${
                            transaction.type === 'return' ? 'text-green-400' : 'text-white'
                          }`}>
                            {transaction.type === 'return' ? '+' : '-'}${transaction.amount.toLocaleString()}
                          </span>
                          <span className={`px-2.5 py-1 rounded-full text-xs font-medium ${
                            transaction.type === 'return'
                              ? 'bg-green-900/40 text-green-300'
                              : 'bg-blue-900/40 text-blue-300'
                          }`}>
                            {transaction.type === 'return' ? 'Return' : 'Investment'}
                          </span>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-gray-400">
                          <Calendar size={14} className="mr-1" />
                          {new Date(transaction.date_invested).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
  );
}