'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON>ert<PERSON>riangle, RefreshCw } from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  isChunkError: boolean;
}

/**
 * ErrorBoundary component to catch and handle errors in React components
 * Specifically designed to handle chunk loading errors that can occur with code splitting
 */
class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      isChunkError: false
    };
  }

  static getDerivedStateFromError(error: Error): State {
    // Check if this is a chunk loading error
    const isChunkError = 
      error.message.includes('Failed to load chunk') || 
      error.message.includes('Loading chunk') ||
      error.message.includes('Loading CSS chunk') ||
      error.message.includes("can't find module") ||
      error.message.includes('Unable to preload chunk');
    
    return {
      hasError: true,
      error,
      errorInfo: null,
      isChunkError
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Log the error to console
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo,
      isChunkError: 
        error.message.includes('Failed to load chunk') || 
        error.message.includes('Loading chunk') ||
        error.message.includes('Loading CSS chunk') ||
        error.message.includes("can't find module") ||
        error.message.includes('Unable to preload chunk')
    });
  }

  handleRefresh = (): void => {
    // Clear application cache and reload the page
    if ('caches' in window) {
      caches.keys().then((names) => {
        names.forEach(name => {
          caches.delete(name);
        });
      });
    }
    
    // Reload the page
    window.location.reload();
  };

  render(): ReactNode {
    if (this.state.hasError) {
      // If a custom fallback is provided, use it
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <div className="min-h-[50vh] flex items-center justify-center p-6">
          <div className="bg-indigo-900/30 border border-indigo-800 rounded-xl p-6 max-w-lg w-full">
            <div className="flex items-center mb-4">
              <AlertTriangle size={24} className="text-amber-500 mr-2" />
              <h2 className="text-xl font-semibold text-white">
                {this.state.isChunkError 
                  ? 'Application Loading Error' 
                  : 'Something went wrong'}
              </h2>
            </div>
            
            <div className="mb-6">
              {this.state.isChunkError ? (
                <p className="text-gray-300">
                  There was a problem loading part of the application. This could be due to a network issue or a recent update.
                </p>
              ) : (
                <p className="text-gray-300">
                  An unexpected error occurred. Our team has been notified.
                </p>
              )}
              
              {this.state.error && (
                <div className="mt-4 p-3 bg-red-900/30 border border-red-800 rounded-lg overflow-auto max-h-32">
                  <p className="text-red-300 text-sm font-mono">
                    {this.state.error.toString()}
                  </p>
                </div>
              )}
            </div>
            
            <div className="flex flex-col sm:flex-row gap-3">
              <button
                onClick={this.handleRefresh}
                className="flex items-center justify-center px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors"
              >
                <RefreshCw size={18} className="mr-2" />
                Refresh Page
              </button>
              
              <a
                href="/"
                className="flex items-center justify-center px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
              >
                Return to Home
              </a>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
