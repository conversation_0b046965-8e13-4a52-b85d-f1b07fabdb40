# BrickChain File Storage System

This document provides a comprehensive overview of the file storage system implemented in the BrickChain application.

## Overview

The file storage system consists of two main components:

1. **Property Image Storage**: For storing and managing property images with optimization and thumbnails
2. **Secure KYC Document Storage**: For storing and managing sensitive user verification documents with encryption

## Architecture

The system uses a hybrid approach:

- **Primary Storage**: IPFS (via Web3.Storage) for decentralized, permanent storage
- **Fallback Storage**: Supabase Storage for reliable, traditional cloud storage
- **Metadata Storage**: Supabase Database for storing file metadata and relationships

## Setup Instructions

### 1. Access the Setup Page

Navigate to the setup page in your dashboard:

```
/dashboard/setup-storage
```

This page provides a user-friendly interface to set up the necessary database tables and storage buckets.

### 2. Set Up Database Tables

Click the "Set Up Database Tables" button to create the following tables:

- `property_images`: For storing property image metadata
- `kyc_documents`: For storing KYC document metadata
- `audit_logs`: For tracking document verification actions

### 3. Set Up Storage Buckets

Click the "Set Up Storage Buckets" button to create the following Supabase Storage buckets:

- `properties`: Public bucket for property images
- `kyc_documents`: Private bucket for KYC documents with row-level security

## Property Image Storage

### Features

- Multiple images per property
- Image categorization (primary, additional, floorplan, document)
- IPFS storage with Supabase fallback
- Image validation and optimization
- Automatic thumbnail generation
- Metadata extraction (dimensions, format)

### File Organization

```
properties/
  ├── {property_id}/
  │   ├── primary/
  │   │   ├── {timestamp}-{filename}.jpg  # Main property image
  │   ├── additional/
  │   │   ├── {timestamp}-{filename}.jpg  # Additional images
  │   ├── floorplans/
  │   │   ├── {timestamp}-{filename}.jpg  # Floor plans
  │   ├── documents/
  │   │   ├── {timestamp}-{filename}.pdf  # Public property documents
  │   └── thumbnails/
  │       ├── {timestamp}-{filename}.webp # Generated thumbnails
```

### Usage

1. When editing a property, use the `PropertyImageUploader` component:

```jsx
<PropertyImageUploader
  propertyId={property.id}
  onImagesChange={(images) => setPropertyImages(images)}
  maxImages={10}
  allowedTypes={['primary', 'additional', 'floorplan', 'document']}
/>
```

2. Images are automatically optimized and thumbnails are generated
3. Images are stored in IPFS with Supabase as fallback
4. Metadata is stored in the database for easy retrieval

### API Endpoints

- `POST /api/property-images`: Upload a new property image
- `GET /api/property-images?propertyId={id}`: Get all images for a property
- `DELETE /api/property-images/{id}`: Delete a property image

## KYC Document Storage

### Features

- Secure document upload with client-side encryption
- Document categorization (identity, address, financial)
- Document verification workflow
- Expiration tracking
- Compliance with data protection regulations

### File Organization

```
kyc_documents/
  ├── {user_id}/
  │   ├── identity/
  │   │   ├── passport/
  │   │   │   ├── {timestamp}-{filename}.pdf  # Encrypted passport
  │   │   ├── national_id/
  │   │   │   ├── {timestamp}-{filename}.jpg  # Encrypted ID card
  │   ├── address/
  │   │   ├── proof_of_address/
  │   │   │   ├── {timestamp}-{filename}.pdf  # Encrypted utility bill
  │   ├── financial/
  │   │   ├── bank_statement/
  │   │   │   ├── {timestamp}-{filename}.pdf  # Encrypted bank statement
```

### Security Measures

1. **Client-side Encryption**:
   - Documents are encrypted in the browser before upload
   - AES-256 encryption with PBKDF2 key derivation
   - Unique salt and IV for each document
   - Encryption key derived from user ID and application secret

2. **Access Controls**:
   - Row-level security in Supabase
   - Users can only access their own documents
   - Only admins can verify documents
   - Audit logging for all verification actions

3. **Compliance Features**:
   - Document expiration tracking
   - Verification status tracking
   - Audit trail for all actions

### Usage

1. Navigate to the KYC page in your dashboard:
   ```
   /dashboard/kyc
   ```

2. Upload the required documents using the `KYCDocumentUploader` component:

```jsx
<KYCDocumentUploader
  documentType="passport"
  documentCategory="identity"
  onDocumentChange={handleDocumentChange}
  encrypt={true}
/>
```

3. Documents are encrypted and securely stored
4. Admins can verify documents through the verification dashboard

### API Endpoints

- `POST /api/kyc-documents`: Upload a new KYC document
- `GET /api/kyc-documents?userId={id}`: Get all documents for a user
- `GET /api/kyc-documents/download/{id}?key={encryptionKey}`: Download and decrypt a document
- `PUT /api/kyc-documents/verify`: Update document verification status
- `GET /api/kyc-documents/verify`: Get pending documents for verification

## Admin Verification

Admins can verify KYC documents through the admin verification dashboard:

```
/dashboard/admin/kyc-verification
```

This dashboard allows admins to:

1. View pending documents
2. Review document details
3. Download and view documents (with proper decryption)
4. Approve or reject documents
5. Add verification notes
6. Set document expiration dates

## Technical Implementation

### Core Utilities

The file storage system is implemented in the following files:

- `src/utils/fileStorage.ts`: Core file storage utilities
- `src/utils/imageProcessor.ts`: Image optimization and processing
- `src/utils/ipfs.ts`: IPFS integration

### Components

- `src/components/PropertyImageUploader.tsx`: UI for property image uploads
- `src/components/KYCDocumentUploader.tsx`: UI for KYC document uploads

### Database Schema

#### Property Images Table

```sql
CREATE TABLE property_images (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  property_id UUID REFERENCES properties(id) ON DELETE CASCADE,
  image_url TEXT NOT NULL,
  image_cid TEXT,
  thumbnail_url TEXT,
  image_type VARCHAR(50) NOT NULL,
  file_name TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  file_type TEXT NOT NULL,
  width INTEGER,
  height INTEGER,
  optimized BOOLEAN DEFAULT FALSE,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
```

#### KYC Documents Table

```sql
CREATE TABLE kyc_documents (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  document_type kyc_document_type NOT NULL,
  document_category VARCHAR(50) NOT NULL,
  file_path TEXT NOT NULL,
  file_name TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  file_type TEXT NOT NULL,
  verification_status verification_status DEFAULT 'pending',
  verification_notes TEXT,
  verified_by UUID REFERENCES auth.users(id),
  verified_at TIMESTAMP WITH TIME ZONE,
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
```

#### Audit Logs Table

```sql
CREATE TABLE audit_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  action TEXT NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  target_id UUID,
  target_type TEXT NOT NULL,
  details JSONB,
  ip_address TEXT,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
```

## Troubleshooting

### Common Issues

1. **Image Upload Fails**
   - Check IPFS configuration
   - Ensure Supabase storage bucket exists
   - Verify file size and type restrictions

2. **Document Encryption Fails**
   - Check browser compatibility (requires modern browser)
   - Ensure file is not too large
   - Check for proper encryption key configuration

3. **Document Download Fails**
   - Verify correct encryption key
   - Check user permissions
   - Ensure document exists in storage

### Debugging

Enable debug logging by setting the following in your `.env.local` file:

```
NEXT_PUBLIC_DEBUG_FILE_STORAGE=true
```

This will output detailed logs to the console during file operations.
