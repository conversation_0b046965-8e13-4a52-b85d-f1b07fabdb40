#!/usr/bin/env node

/**
 * Local Supabase Setup Script
 * 
 * This script helps you get started with local Supabase development quickly.
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

function runCommand(command, description) {
  console.log(colorize(`🔄 ${description}...`, 'cyan'));
  try {
    execSync(command, { stdio: 'inherit' });
    console.log(colorize(`✅ ${description} completed`, 'green'));
    return true;
  } catch (error) {
    console.error(colorize(`❌ ${description} failed:`, 'red'), error.message);
    return false;
  }
}

function checkPrerequisites() {
  console.log(colorize('🔍 Checking prerequisites...', 'blue'));
  
  try {
    execSync('docker --version', { stdio: 'pipe' });
    console.log(colorize('✅ Docker is installed', 'green'));
  } catch (error) {
    console.error(colorize('❌ Docker is not installed or not running', 'red'));
    console.log(colorize('Please install Docker Desktop and make sure it\'s running', 'yellow'));
    return false;
  }
  
  try {
    execSync('supabase --version', { stdio: 'pipe' });
    console.log(colorize('✅ Supabase CLI is installed', 'green'));
  } catch (error) {
    console.error(colorize('❌ Supabase CLI is not installed', 'red'));
    console.log(colorize('Installing Supabase CLI...', 'yellow'));
    
    try {
      execSync('npm install -g supabase', { stdio: 'inherit' });
      console.log(colorize('✅ Supabase CLI installed successfully', 'green'));
    } catch (installError) {
      console.error(colorize('❌ Failed to install Supabase CLI', 'red'));
      console.log(colorize('Please install manually: npm install -g supabase', 'yellow'));
      return false;
    }
  }
  
  return true;
}

function setupEnvironment() {
  console.log(colorize('\n🔧 Setting up local environment...', 'blue'));
  
  // Install dependencies
  if (!runCommand('npm install', 'Installing dependencies')) {
    return false;
  }
  
  // Switch to local environment
  if (!runCommand('node scripts/switch-env.js local', 'Switching to local environment')) {
    return false;
  }
  
  return true;
}

function startSupabase() {
  console.log(colorize('\n🚀 Starting Supabase services...', 'blue'));
  
  if (!runCommand('supabase start', 'Starting Supabase')) {
    return false;
  }
  
  console.log(colorize('\n📊 Supabase services are now running:', 'green'));
  console.log('  • API: http://localhost:54321');
  console.log('  • Studio: http://localhost:54323');
  console.log('  • Database: postgresql://postgres:postgres@localhost:54322/postgres');
  
  return true;
}

function seedDatabase() {
  console.log(colorize('\n🌱 Seeding database with sample data...', 'blue'));
  
  if (!runCommand('npm run db:seed', 'Seeding database')) {
    console.log(colorize('⚠️  Database seeding failed, but you can try again later with: npm run db:seed', 'yellow'));
  }
}

function showNextSteps() {
  console.log(colorize('\n🎉 Setup completed successfully!', 'bright'));
  console.log(colorize('\n📋 Next steps:', 'blue'));
  console.log('1. Open Supabase Studio: http://localhost:54323');
  console.log('2. Start your development server: npm run dev');
  console.log('3. Your app will now use the local Supabase instance');
  
  console.log(colorize('\n🛠️  Useful commands:', 'blue'));
  console.log('  npm run db:query "SELECT * FROM users"     - Run SQL queries');
  console.log('  npm run db:query --interactive             - Interactive SQL mode');
  console.log('  npm run supabase:studio                    - Open Studio');
  console.log('  npm run supabase:status                    - Check service status');
  console.log('  node scripts/switch-env.js remote          - Switch back to remote');
  
  console.log(colorize('\n📚 Documentation:', 'blue'));
  console.log('  docs/SUPABASE-LOCAL-DEVELOPMENT.md - Complete guide');
}

async function main() {
  console.log(colorize('🏗️  BrickChain Local Supabase Setup', 'bright'));
  console.log('─'.repeat(50));
  
  // Check prerequisites
  if (!checkPrerequisites()) {
    process.exit(1);
  }
  
  // Setup environment
  if (!setupEnvironment()) {
    process.exit(1);
  }
  
  // Start Supabase
  if (!startSupabase()) {
    process.exit(1);
  }
  
  // Seed database
  seedDatabase();
  
  // Show next steps
  showNextSteps();
}

main().catch(console.error);
