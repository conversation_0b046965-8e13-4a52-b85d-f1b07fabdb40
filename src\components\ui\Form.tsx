'use client';

import React, { createContext, useContext, useId } from 'react';
import { cn } from '@/utils/cn';

// Form Context
interface FormContextType {
  formId: string;
}

const FormContext = createContext<FormContextType | undefined>(undefined);

const useFormContext = () => {
  const context = useContext(FormContext);
  if (!context) {
    throw new Error('Form components must be used within a Form');
  }
  return context;
};

// Form Root Component
export interface FormProps extends React.FormHTMLAttributes<HTMLFormElement> {
  children: React.ReactNode;
}

export const Form: React.FC<FormProps> = ({ children, className, ...props }) => {
  const formId = useId();

  return (
    <FormContext.Provider value={{ formId }}>
      <form
        className={cn('space-y-6', className)}
        noValidate
        {...props}
      >
        {children}
      </form>
    </FormContext.Provider>
  );
};

// Form Field Component
export interface FormFieldProps {
  children: React.ReactNode;
  className?: string;
}

export const FormField: React.FC<FormFieldProps> = ({ children, className }) => {
  return (
    <div className={cn('space-y-2', className)}>
      {children}
    </div>
  );
};

// Form Label Component
export interface FormLabelProps extends React.LabelHTMLAttributes<HTMLLabelElement> {
  required?: boolean;
  children: React.ReactNode;
}

export const FormLabel: React.FC<FormLabelProps> = ({
  children,
  required = false,
  className,
  ...props
}) => {
  return (
    <label
      className={cn(
        'block text-sm font-medium text-gray-300',
        required && "after:content-['*'] after:ml-0.5 after:text-red-500",
        className
      )}
      {...props}
    >
      {children}
    </label>
  );
};

// Form Control Component (wrapper for inputs)
export interface FormControlProps {
  children: React.ReactNode;
  className?: string;
}

export const FormControl: React.FC<FormControlProps> = ({ children, className }) => {
  return (
    <div className={cn('relative', className)}>
      {children}
    </div>
  );
};

// Form Description Component
export interface FormDescriptionProps extends React.HTMLAttributes<HTMLParagraphElement> {
  children: React.ReactNode;
}

export const FormDescription: React.FC<FormDescriptionProps> = ({
  children,
  className,
  ...props
}) => {
  return (
    <p
      className={cn('text-sm text-gray-400', className)}
      {...props}
    >
      {children}
    </p>
  );
};

// Form Message Component (for errors/success)
export interface FormMessageProps extends React.HTMLAttributes<HTMLParagraphElement> {
  children: React.ReactNode;
  type?: 'error' | 'success' | 'warning' | 'info';
}

export const FormMessage: React.FC<FormMessageProps> = ({
  children,
  type = 'error',
  className,
  ...props
}) => {
  const styles = {
    error: 'text-red-400',
    success: 'text-green-400',
    warning: 'text-yellow-400',
    info: 'text-blue-400',
  };

  return (
    <p
      className={cn('text-sm flex items-center gap-1', styles[type], className)}
      role={type === 'error' ? 'alert' : undefined}
      {...props}
    >
      {children}
    </p>
  );
};

// Form Section Component (for grouping related fields)
export interface FormSectionProps {
  title?: string;
  description?: string;
  children: React.ReactNode;
  className?: string;
}

export const FormSection: React.FC<FormSectionProps> = ({
  title,
  description,
  children,
  className,
}) => {
  return (
    <div className={cn('space-y-4', className)}>
      {(title || description) && (
        <div className="space-y-1">
          {title && (
            <h3 className="text-lg font-medium text-white">{title}</h3>
          )}
          {description && (
            <p className="text-sm text-gray-400">{description}</p>
          )}
        </div>
      )}
      <div className="space-y-4">
        {children}
      </div>
    </div>
  );
};

// Form Actions Component (for buttons)
export interface FormActionsProps {
  children: React.ReactNode;
  className?: string;
  align?: 'left' | 'center' | 'right' | 'between';
}

export const FormActions: React.FC<FormActionsProps> = ({
  children,
  className,
  align = 'right',
}) => {
  const alignments = {
    left: 'justify-start',
    center: 'justify-center',
    right: 'justify-end',
    between: 'justify-between',
  };

  return (
    <div className={cn('flex gap-3', alignments[align], className)}>
      {children}
    </div>
  );
};

// Form Grid Component (for responsive layouts)
export interface FormGridProps {
  children: React.ReactNode;
  columns?: 1 | 2 | 3 | 4;
  className?: string;
}

export const FormGrid: React.FC<FormGridProps> = ({
  children,
  columns = 2,
  className,
}) => {
  const gridCols = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
  };

  return (
    <div className={cn('grid gap-4', gridCols[columns], className)}>
      {children}
    </div>
  );
};

// Export all components as named exports
export {
  useFormContext,
};
